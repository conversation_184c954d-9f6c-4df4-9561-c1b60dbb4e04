# has-bigints <sup>[![Version Badge][npm-version-svg]][package-url]</sup>

[![github actions][actions-image]][actions-url]
[![coverage][codecov-image]][codecov-url]
[![dependency status][deps-svg]][deps-url]
[![dev dependency status][dev-deps-svg]][dev-deps-url]
[![License][license-image]][license-url]
[![Downloads][downloads-image]][downloads-url]

[![npm badge][npm-badge-png]][package-url]

Determine if the JS environment has BigInt support.

## Example

```js
var hasBigInts = require('has-bigints');

hasBigInts() === true; // if the environment has native BigInt support. Not polyfillable, not forgeable.
```

## Tests
Simply clone the repo, `npm install`, and run `npm test`

[package-url]: https://npmjs.org/package/has-bigints
[npm-version-svg]: https://versionbadg.es/inspect-js/has-bigints.svg
[deps-svg]: https://david-dm.org/inspect-js/has-bigints.svg
[deps-url]: https://david-dm.org/inspect-js/has-bigints
[dev-deps-svg]: https://david-dm.org/inspect-js/has-bigints/dev-status.svg
[dev-deps-url]: https://david-dm.org/inspect-js/has-bigints#info=devDependencies
[npm-badge-png]: https://nodei.co/npm/has-bigints.png?downloads=true&stars=true
[license-image]: https://img.shields.io/npm/l/has-bigints.svg
[license-url]: LICENSE
[downloads-image]: https://img.shields.io/npm/dm/has-bigints.svg
[downloads-url]: https://npm-stat.com/charts.html?package=has-bigints
[codecov-image]: https://codecov.io/gh/inspect-js/has-bigints/branch/main/graphs/badge.svg
[codecov-url]: https://app.codecov.io/gh/inspect-js/has-bigints/
[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/has-bigints
[actions-url]: https://github.com/inspect-js/has-bigints/actions
