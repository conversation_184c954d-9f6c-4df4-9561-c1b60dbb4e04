declare const FormData: {
    new (form?: HTMLFormElement | undefined, submitter?: HTMLElement | null | undefined): FormData;
    prototype: FormData;
};
declare const Blob: {
    new (blobParts?: BlobPart[] | undefined, options?: BlobPropertyBag | undefined): Blob;
    prototype: Blob;
};
declare const File: {
    new (fileBits: BlobPart[], fileName: string, options?: FilePropertyBag | undefined): File;
    prototype: File;
};

export { Blob, File, FormData };
