{"name": "index-to-position", "version": "1.1.0", "description": "Convert a string index to its line and column position", "license": "MIT", "repository": "sindresorhus/index-to-position", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsc index.d.ts"}, "files": ["index.js", "index.d.ts"], "keywords": ["index", "position", "line", "column", "text", "coordinate", "string", "character", "line number", "column number", "location"], "devDependencies": {"ava": "^5.3.1", "typescript": "^5.2.2", "xo": "^0.56.0"}}