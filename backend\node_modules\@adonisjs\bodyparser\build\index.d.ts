import type { Multipart } from './src/multipart/main.js';
import { MultipartFile } from './src/multipart/file.js';
import type { FileValidationOptions } from './src/types.js';
export { defineConfig } from './src/define_config.js';
export { MultipartFile, Multipart };
/**
 * Extending request class with custom properties.
 */
declare module '@adonisjs/http-server' {
    interface Request {
        multipart: Multipart;
        __raw_files: Record<string, MultipartFile | MultipartFile[]>;
        allFiles(): Record<string, MultipartFile | MultipartFile[]>;
        file(key: string, options?: Partial<FileValidationOptions>): MultipartFile | null;
        files(key: string, options?: Partial<FileValidationOptions>): MultipartFile[];
    }
}
/**
 * Extending feature flags to decide how to process the multipart
 * request body
 */
declare module '@adonisjs/application/types' {
    interface ExperimentalFlagsList {
        mergeMultipartFieldsAndFiles?: boolean;
    }
}
