{"name": "got", "version": "14.4.7", "description": "Human-friendly and powerful HTTP request library for Node.js", "license": "MIT", "repository": "sindresorhus/got", "funding": "https://github.com/sindresorhus/got?sponsor=1", "type": "module", "exports": {"types": "./dist/source/index.d.ts", "default": "./dist/source/index.js"}, "sideEffects": false, "engines": {"node": ">=20"}, "scripts": {"test": "xo && tsc --noEmit && NODE_OPTIONS='--import=tsx/esm' ava", "release": "np", "build": "del-cli dist && tsc", "prepare": "npm run build"}, "files": ["dist/source"], "keywords": ["http", "https", "http2", "get", "got", "url", "uri", "request", "simple", "curl", "wget", "fetch", "net", "network", "gzip", "brotli", "requests", "human-friendly", "axios", "superagent", "node-fetch", "ky"], "dependencies": {"@sindresorhus/is": "^7.0.1", "@szmarczak/http-timer": "^5.0.1", "cacheable-lookup": "^7.0.0", "cacheable-request": "^12.0.1", "decompress-response": "^6.0.0", "form-data-encoder": "^4.0.2", "http2-wrapper": "^2.2.1", "lowercase-keys": "^3.0.0", "p-cancelable": "^4.0.1", "responselike": "^3.0.0", "type-fest": "^4.26.1"}, "devDependencies": {"@hapi/bourne": "^3.0.0", "@sindresorhus/tsconfig": "^6.0.0", "@sinonjs/fake-timers": "^11.2.2", "@types/benchmark": "^2.1.5", "@types/express": "^5.0.0", "@types/node": "^22.7.5", "@types/pem": "^1.14.4", "@types/readable-stream": "^4.0.14", "@types/request": "^2.48.12", "@types/sinon": "^17.0.2", "@types/sinonjs__fake-timers": "^8.1.5", "ava": "^5.3.1", "axios": "^1.7.7", "benchmark": "^2.1.4", "bluebird": "^3.7.2", "body-parser": "^1.20.3", "create-cert": "^1.0.6", "create-test-server": "^3.0.1", "del-cli": "^6.0.0", "delay": "^6.0.0", "expect-type": "^1.0.0", "express": "^4.21.1", "form-data": "^4.0.0", "formdata-node": "^6.0.3", "get-stream": "^9.0.1", "nock": "^13.5.5", "node-fetch": "^3.3.2", "np": "^10.0.5", "nyc": "^17.1.0", "p-event": "^6.0.1", "pem": "^1.14.8", "pify": "^6.1.0", "readable-stream": "^4.4.2", "request": "^2.88.2", "sinon": "^19.0.2", "slow-stream": "0.0.4", "tempy": "^3.1.0", "then-busboy": "^5.2.1", "tough-cookie": "^4.1.4", "tsx": "^4.19.1", "typescript": "^5.6.3", "xo": "^0.56.0"}, "ava": {"files": ["test/*", "!test/*.types.ts"], "timeout": "10m", "extensions": {"ts": "module"}, "workerThreads": false}, "nyc": {"reporter": ["text", "html", "lcov"], "extension": [".ts"], "exclude": ["**/test/**"]}, "xo": {"ignores": ["documentation/examples/*"], "rules": {"@typescript-eslint/no-empty-function": "off", "n/no-deprecated-api": "off", "@typescript-eslint/no-implicit-any-catch": "off", "ava/assertion-arguments": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/await-thenable": "off", "@typescript-eslint/no-redundant-type-constituents": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/promise-function-async": "off", "no-lone-blocks": "off", "unicorn/no-await-expression-member": "off", "unicorn/prefer-event-target": "off"}}, "runkitExampleFilename": "./documentation/examples/runkit-example.js"}