# @adonisjs/config

<br />

[![gh-workflow-image]][gh-workflow-url] [![npm-image]][npm-url] ![][typescript-image] [![license-image]][license-url]

## Introduction
Config provider to read AdonisJS application config values without directly relying on hard-coded paths.

## Official Documentation
The documentation is available on the [AdonisJS website](https://docs.adonisjs.com/guides/config)

## Contributing
One of the primary goals of AdonisJS is to have a vibrant community of users and contributors who believes in the principles of the framework.

We encourage you to read the [contribution guide](https://github.com/adonisjs/.github/blob/main/docs/CONTRIBUTING.md) before contributing to the framework.

## Code of Conduct
In order to ensure that the AdonisJS community is welcoming to all, please review and abide by the [Code of Conduct](https://github.com/adonisjs/.github/blob/main/docs/CODE_OF_CONDUCT.md).

## License
AdonisJS config is open-sourced software licensed under the [MIT license](LICENSE.md).

[gh-workflow-image]: https://img.shields.io/github/actions/workflow/status/adonisjs/config/checks.yml?style=for-the-badge
[gh-workflow-url]: https://github.com/adonisjs/config/actions/workflows/checks.yml "Github action"

[typescript-image]: https://img.shields.io/badge/Typescript-294E80.svg?style=for-the-badge&logo=typescript
[typescript-url]:  "typescript"

[npm-image]: https://img.shields.io/npm/v/@adonisjs/config.svg?style=for-the-badge&logo=npm
[npm-url]: https://npmjs.org/package/@adonisjs/config "npm"

[license-image]: https://img.shields.io/npm/l/@adonisjs/config?color=blueviolet&style=for-the-badge
[license-url]: LICENSE.md "license"
