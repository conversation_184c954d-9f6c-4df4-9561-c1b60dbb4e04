{"name": "@adonisjs/application", "version": "8.4.1", "description": "AdonisJS application class to read app related data", "type": "module", "main": "build/index.js", "files": ["build", "!build/bin", "!build/tests"], "exports": {".": "./build/index.js", "./types": "./build/src/types.js", "./factories": "./build/factories/main.js"}, "engines": {"node": ">=18.16.0"}, "scripts": {"pretest": "npm run lint", "test": "cross-env NODE_DEBUG=adonisjs:app c8 npm run quick:test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit", "clean": "del-cli build", "precompile": "npm run lint && npm run clean", "compile": "tsup-node && tsc --emitDeclarationOnly --declaration", "build": "npm run compile", "version": "npm run build", "prepublishOnly": "npm run build", "release": "release-it", "quick:test": "node --import=ts-node-maintained/register/esm --enable-source-maps bin/test.ts"}, "devDependencies": {"@adonisjs/config": "^5.0.2", "@adonisjs/eslint-config": "^2.0.0", "@adonisjs/fold": "^10.1.3", "@adonisjs/prettier-config": "^1.4.4", "@adonisjs/tsconfig": "^1.4.0", "@japa/assert": "^4.0.1", "@japa/expect-type": "^2.0.3", "@japa/file-system": "^2.3.2", "@japa/runner": "^4.2.0", "@poppinss/cliui": "^6.4.3", "@release-it/conventional-changelog": "^10.0.1", "@swc/core": "1.10.7", "@types/fs-extra": "^11.0.4", "@types/glob-parent": "^5.1.3", "@types/node": "^22.15.18", "@types/semver": "^7.7.0", "c8": "^10.1.3", "cross-env": "^7.0.3", "del-cli": "^6.0.0", "eslint": "^9.26.0", "fs-extra": "^11.3.0", "prettier": "^3.5.3", "release-it": "^19.0.2", "semver": "^7.7.2", "ts-dedent": "^2.2.0", "ts-node-maintained": "^10.9.5", "tsup": "^8.4.0", "typescript": "^5.8.3"}, "dependencies": {"@poppinss/hooks": "^7.2.5", "@poppinss/macroable": "^1.0.4", "@poppinss/utils": "^6.9.3", "glob-parent": "^6.0.2", "tempura": "^0.4.1"}, "peerDependencies": {"@adonisjs/config": "^5.0.0", "@adonisjs/fold": "^10.0.0"}, "homepage": "https://github.com/adonisjs/application#readme", "repository": {"type": "git", "url": "git+https://github.com/adonisjs/application.git"}, "bugs": {"url": "https://github.com/adonisjs/application/issues"}, "keywords": ["ad<PERSON><PERSON><PERSON><PERSON>", "app"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "publishConfig": {"access": "public", "provenance": true}, "tsup": {"entry": ["./index.ts", "./src/types.ts", "./factories/main.ts"], "outDir": "./build", "clean": true, "format": "esm", "dts": false, "sourcemap": false, "target": "esnext"}, "release-it": {"git": {"requireCleanWorkingDir": true, "requireUpstream": true, "commitMessage": "chore(release): ${version}", "tagAnnotation": "v${version}", "push": true, "tagName": "v${version}"}, "github": {"release": true}, "npm": {"publish": true, "skipChecks": true}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "angular"}}}}, "c8": {"reporter": ["text", "html"], "exclude": ["tests/**"]}, "prettier": "@adonisjs/prettier-config"}