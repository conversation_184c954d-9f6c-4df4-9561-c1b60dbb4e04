import type { Readable } from 'node:stream';
import { DriveFile } from './driver_file.js';
import { DriveDirectory } from './drive_directory.js';
import type { WriteOptions, FileSnapshot, ObjectMetaData, DriverContract, ObjectVisibility, SignedURLOptions } from './types.js';
/**
 * Disk offers a unified API for working with different drivers
 */
export declare class Disk {
    #private;
    driver: DriverContract;
    constructor(driver: DriverContract);
    /**
     * Creates a new instance of the DriveFile. It can be used
     * to lazily fetch file contents or convert it into a
     * snapshot for persistence
     */
    file(key: string): DriveFile;
    /**
     * Creates a new instance of the DriveFile from the snapshot.
     */
    fromSnapshot(snapshot: FileSnapshot): DriveFile;
    /**
     * Check if the file exists. This method cannot check existence
     * of directories.
     */
    exists(key: string): Promise<boolean>;
    /**
     * Returns file contents as a UTF-8 string. Use "getArrayBuffer" method
     * if you need more control over the file contents decoding.
     */
    get(key: string): Promise<string>;
    /**
     * Returns file contents as a Readable stream.
     */
    getStream(key: string): Promise<Readable>;
    /**
     * Returns file contents as a Uint8Array.
     */
    getBytes(key: string): Promise<Uint8Array>;
    /**
     * @deprecated
     * @see {@link Disk.getBytes}
     */
    getArrayBuffer(key: string): Promise<Uint8Array>;
    /**
     * Returns metadata of the given file.
     */
    getMetaData(key: string): Promise<ObjectMetaData>;
    /**
     * Returns the visibility of the file
     */
    getVisibility(key: string): Promise<ObjectVisibility>;
    /**
     * Returns the public URL of the file
     */
    getUrl(key: string): Promise<string>;
    /**
     * Returns a signed/temporary URL of the file
     */
    getSignedUrl(key: string, options?: SignedURLOptions): Promise<string>;
    /**
     * Returns a signed/temporary URL that can be used to directly upload
     * the file contents to the storage.
     */
    getSignedUploadUrl(key: string, options?: SignedURLOptions): Promise<string>;
    /**
     * Update the visibility of the file
     */
    setVisibility(key: string, visibility: ObjectVisibility): Promise<void>;
    /**
     * Create new file or update an existing file. In case of an error,
     * the "E_CANNOT_WRITE_FILE" exception is thrown
     */
    put(key: string, contents: string | Uint8Array, options?: WriteOptions): Promise<void>;
    /**
     * Create new file or update an existing file using a Readable Stream
     * In case of an error, the "E_CANNOT_WRITE_FILE" exception is thrown
     */
    putStream(key: string, contents: Readable, options?: WriteOptions): Promise<void>;
    /**
     * Copies file from the "source" to the "destination" within the
     * same bucket or the root location of local filesystem.
     *
     * Use "copyFromFs" method to copy files from local filesystem to
     * a cloud provider
     */
    copy(source: string, destination: string, options?: WriteOptions): Promise<void>;
    /**
     * Copies file from the local filesystem to the cloud provider.
     */
    copyFromFs(source: string | URL, destination: string, options?: WriteOptions): Promise<void>;
    /**
     * Moves file from the "source" to the "destination" within the
     * same bucket or the root location of local filesystem.
     *
     * Use "moveFromFs" method to move files from local filesystem to
     * a cloud provider
     */
    move(source: string, destination: string, options?: WriteOptions): Promise<void>;
    /**
     * Moves file from the local filesystem to the cloud provider.
     */
    moveFromFs(source: string | URL, destination: string, options?: WriteOptions): Promise<void>;
    /**
     * Deletes a file for the given key. Use "deleteAll" method to delete
     * files for a matching folder prefix.
     */
    delete(key: string): Promise<void>;
    /**
     * Delete all files matching the given prefix. In case of "fs" driver,
     * the mentioned folder will be deleted.
     */
    deleteAll(prefix?: string): Promise<void>;
    /**
     * Returns a list of objects which includes and files and directories.
     * In case of "recursive" listing, no directories are returned.
     */
    listAll(prefix?: string, options?: {
        recursive?: boolean;
        paginationToken?: string;
    }): Promise<{
        paginationToken?: string;
        objects: Iterable<DriveFile | DriveDirectory>;
    }>;
}
