{"name": "hot-hook", "description": "Easy hot module reloading (HMR) for Node.js and ESM", "version": "0.2.6", "main": "index.js", "type": "module", "files": ["build", "!build/tests", "!build/bin", "import-meta.d.ts"], "exports": {".": "./build/src/hot.js", "./loader": "./build/src/loader.js", "./register": "./build/src/register.js", "./import-meta": {"types": "./import-meta.d.ts"}}, "devDependencies": {"@types/picomatch": "^2.3.3"}, "dependencies": {"chokidar": "^3.6.0", "fast-glob": "^3.3.2", "picomatch": "^4.0.2", "read-package-up": "^11.0.0"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["hmr", "hot", "reload", "esm", "node"], "eslintConfig": {"extends": "@adonisjs/eslint-config/package"}, "prettier": "@adonisjs/prettier-config", "publishConfig": {"access": "public", "tag": "latest"}, "scripts": {"clean": "del-cli build", "typecheck": "tsc --noEmit", "lint": "eslint . --ext=.ts", "format": "prettier --write .", "quick:test": "node --import=./tsnode.esm.js --enable-source-maps bin/test.ts", "pretest": "pnpm lint", "test": "c8 pnpm quick:test", "build": "pnpm clean && tsc"}}