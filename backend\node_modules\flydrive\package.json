{"name": "flydrive", "description": "File storage library with unified API to manage files across multiple cloud storage providers like S3, GCS, R2 and so on", "version": "1.3.0", "engines": {"node": ">=20.6.0"}, "type": "module", "files": ["build", "!build/bin", "!build/tests"], "main": "build/index.js", "exports": {".": "./build/index.js", "./types": "./build/src/types.js", "./drivers/fs": "./build/drivers/fs/main.js", "./drivers/fs/types": "./build/drivers/fs/types.js", "./drivers/gcs": "./build/drivers/gcs/main.js", "./drivers/gcs/types": "./build/drivers/gcs/types.js", "./drivers/s3": "./build/drivers/s3/main.js", "./drivers/s3/types": "./build/drivers/s3/types.js"}, "scripts": {"pretest": "npm run lint", "test": "c8 npm run quick:test", "lint": "eslint .", "format": "prettier --write .", "clean": "del-cli build", "typecheck": "tsc --noEmit", "precompile": "npm run lint && npm run clean", "compile": "tsup-node && tsc --emitDeclarationOnly --declaration", "build": "npm run compile", "prepublishOnly": "npm run build", "release": "release-it", "quick:test": "node --import=ts-node-maintained/register/esm --enable-source-maps bin/test.ts"}, "devDependencies": {"@adonisjs/env": "^6.2.0", "@adonisjs/eslint-config": "^2.1.0", "@adonisjs/prettier-config": "^1.4.5", "@adonisjs/tsconfig": "^1.4.1", "@aws-sdk/client-s3": "^3.840.0", "@aws-sdk/s3-request-presigner": "^3.840.0", "@google-cloud/storage": "^7.16.0", "@japa/assert": "^4.0.1", "@japa/file-system": "^2.3.2", "@japa/runner": "^4.2.0", "@release-it/conventional-changelog": "^10.0.1", "@swc/core": "1.12.9", "@types/etag": "^1.8.4", "@types/mime-types": "^3.0.1", "@types/node": "^22.13.10", "@types/sinon": "^17.0.4", "c8": "^10.1.3", "copyfiles": "^2.4.1", "del-cli": "^6.0.0", "eslint": "^9.30.0", "get-stream": "^9.0.1", "got": "^14.4.7", "prettier": "^3.6.2", "release-it": "^19.0.3", "sinon": "^21.0.0", "ts-node-maintained": "^10.9.5", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "dependencies": {"@humanwhocodes/retry": "^0.4.3", "@poppinss/utils": "^6.10.0", "etag": "^1.8.1", "mime-types": "^3.0.1"}, "peerDependencies": {"@aws-sdk/client-s3": "^3.577.0", "@aws-sdk/s3-request-presigner": "^3.577.0", "@google-cloud/storage": "^7.10.2"}, "peerDependenciesMeta": {"@aws-sdk/client-s3": {"optional": true}, "@aws-sdk/s3-request-presigner": {"optional": true}, "@google-cloud/storage": {"optional": true}}, "homepage": "https://github.com/flydrive-js/core#readme", "repository": {"type": "git", "url": "git+https://github.com/flydrive-js/core.git"}, "bugs": {"url": "https://github.com/flydrive-js/core/issues"}, "keywords": ["filesystem", "flydrive", "s3", "gcs", "r2"], "author": "virk,flydrive", "license": "MIT", "publishConfig": {"access": "public", "provenance": true}, "tsup": {"entry": ["./index.ts", "./src/types.ts", "./drivers/fs/main.ts", "./drivers/fs/types.ts", "./drivers/gcs/main.ts", "./drivers/gcs/types.ts", "./drivers/s3/main.ts", "./drivers/s3/types.ts"], "outDir": "./build", "clean": true, "format": "esm", "dts": false, "sourcemap": false, "target": "esnext"}, "release-it": {"git": {"requireCleanWorkingDir": true, "requireUpstream": true, "commitMessage": "chore(release): ${version}", "tagAnnotation": "v${version}", "push": true, "tagName": "v${version}"}, "github": {"release": true}, "npm": {"publish": true, "skipChecks": true}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "angular"}}}}, "c8": {"reporter": ["text", "html"], "exclude": ["tests/**"]}, "prettier": "@adonisjs/prettier-config"}