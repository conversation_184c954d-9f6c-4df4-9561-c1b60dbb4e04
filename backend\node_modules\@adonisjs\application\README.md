# @adonisjs/application

<br />

[![gh-workflow-image]][gh-workflow-url] [![npm-image]][npm-url] ![][typescript-image] [![license-image]][license-url]

## Introduction
AdonisJS application packages manages the lifecycle of an app in different runtime environments. Also, the app package is responsible for loading the `adonisrc.ts` file and boot the application.

## Official Documentation
The documentation is available on the [AdonisJS website](https://docs.adonisjs.com/guides/application)

## Contributing
One of the primary goals of AdonisJS is to have a vibrant community of users and contributors who believes in the principles of the framework.

We encourage you to read the [contribution guide](https://github.com/adonisjs/.github/blob/main/docs/CONTRIBUTING.md) before contributing to the framework.

## Code of Conduct
In order to ensure that the AdonisJS community is welcoming to all, please review and abide by the [Code of Conduct](https://github.com/adonisjs/.github/blob/main/docs/CODE_OF_CONDUCT.md).

## License
AdonisJS application is open-sourced software licensed under the [MIT license](LICENSE.md).

[gh-workflow-image]: https://img.shields.io/github/actions/workflow/status/adonisjs/application/checks.yml?style=for-the-badge
[gh-workflow-url]: https://github.com/adonisjs/application/actions/workflows/checks.yml "Github action"

[typescript-image]: https://img.shields.io/badge/Typescript-294E80.svg?style=for-the-badge&logo=typescript
[typescript-url]:  "typescript"

[npm-image]: https://img.shields.io/npm/v/@adonisjs/application.svg?style=for-the-badge&logo=npm
[npm-url]: https://npmjs.org/package/@adonisjs/application "npm"

[license-image]: https://img.shields.io/npm/l/@adonisjs/application?color=blueviolet&style=for-the-badge
[license-url]: LICENSE.md "license"
