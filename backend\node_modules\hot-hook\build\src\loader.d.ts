/// <reference types="node" resolution-mode="require"/>
import type { InitializeHook, LoadHook, ResolveHook } from 'node:module';
import { InitializeHookOptions } from './types.js';
export declare class HotHookLoader {
    #private;
    constructor(options: InitializeHookOptions);
    /**
     * The load hook.
     * We use it mainly for adding the import.meta.hot object to the module.
     */
    load: LoadHook;
    /**
     * The resolve hook
     * We use it for :
     * - Adding the hot-hook query parameter to the URL ( to getting a fresh version )
     * - And adding files to the watcher
     */
    resolve: ResolveHook;
}
export declare const initialize: InitializeHook;
export declare const load: LoadHook;
export declare const resolve: ResolveHook;
