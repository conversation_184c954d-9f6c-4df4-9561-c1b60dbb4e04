{"name": "is-builtin-module", "version": "3.2.1", "description": "Check if a string matches the name of a Node.js builtin module", "license": "MIT", "repository": "sindresorhus/is-builtin-module", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["builtin", "built-in", "builtins", "node", "modules", "core", "bundled", "list", "array", "names", "is", "detect", "check", "match"], "dependencies": {"builtin-modules": "^3.3.0"}, "devDependencies": {"ava": "^0.25.0", "tsd": "^0.7.2", "xo": "^0.23.0"}}