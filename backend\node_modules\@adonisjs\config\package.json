{"name": "@adonisjs/config", "version": "5.0.3", "description": "Config collection and loader used by the AdonisJS framework", "main": "build/index.js", "type": "module", "files": ["build", "!build/bin", "!build/tests"], "exports": {".": "./build/index.js"}, "engines": {"node": ">=18.16.0"}, "scripts": {"pretest": "npm run lint", "test": "c8 npm run quick:test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit", "clean": "del-cli build", "precompile": "npm run lint && npm run clean", "compile": "tsup-node && tsc --emitDeclarationOnly --declaration", "build": "npm run compile", "version": "npm run build", "prepublishOnly": "npm run build", "release": "release-it", "quick:test": "node --import=@poppinss/ts-exec --enable-source-maps bin/test.ts"}, "devDependencies": {"@adonisjs/eslint-config": "^2.1.0", "@adonisjs/prettier-config": "^1.4.5", "@adonisjs/tsconfig": "^1.3.1", "@japa/assert": "^4.0.1", "@japa/file-system": "^2.3.2", "@japa/runner": "^4.2.0", "@poppinss/ts-exec": "^1.2.0", "@release-it/conventional-changelog": "^10.0.1", "@types/node": "^22.15.21", "c8": "^10.1.3", "del-cli": "^6.0.0", "eslint": "^9.27.0", "prettier": "^3.5.3", "release-it": "^19.0.2", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "dependencies": {"@poppinss/utils": "^6.9.4"}, "homepage": "https://github.com/adonisjs/config#readme", "repository": {"type": "git", "url": "git+https://github.com/adonisjs/config.git"}, "bugs": {"url": "https://github.com/adonisjs/config/issues"}, "keywords": ["ad<PERSON><PERSON><PERSON><PERSON>", "adonisjs-core", "config"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "publishConfig": {"access": "public", "provenance": true}, "tsup": {"entry": ["./index.ts"], "outDir": "./build", "clean": true, "format": "esm", "dts": false, "sourcemap": false, "target": "esnext"}, "release-it": {"git": {"requireCleanWorkingDir": true, "requireUpstream": true, "commitMessage": "chore(release): ${version}", "tagAnnotation": "v${version}", "push": true, "tagName": "v${version}"}, "github": {"release": true}, "npm": {"publish": true, "skipChecks": true}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "angular"}}}}, "c8": {"reporter": ["text", "html"], "exclude": ["tests/**"]}, "prettier": "@adonisjs/prettier-config"}