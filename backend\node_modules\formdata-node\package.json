{"name": "formdata-node", "version": "6.0.3", "type": "module", "description": "Spec-compliant FormData implementation for Node.js", "repository": "octet-stream/form-data", "sideEffects": false, "keywords": ["form-data", "node", "form", "upload", "files-upload", "ponyfill"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "packageManager": "pnpm@8.5.1", "engines": {"node": ">= 18"}, "main": "./lib/form-data.js", "module": "./lib/browser.js", "browser": "./lib/browser.js", "exports": {"./package.json": "./package.json", ".": {"node": {"import": {"types": "./lib/form-data.d.ts", "default": "./lib/form-data.js"}, "require": {"types": "./lib/form-data.d.cts", "default": "./lib/form-data.cjs"}}, "browser": {"import": {"types": "./lib/browser.d.ts", "default": "./lib/browser.js"}, "require": {"types": "./lib/browser.d.cts", "default": "./lib/browser.cjs"}}, "default": {"types": "./lib/form-data.d.ts", "import": "./lib/form-data.js"}}, "./file-from-path": {"import": {"types": "./@lib/file-from-path.d.ts", "default": "./lib/file-from-path.js"}, "require": {"types": "./@lib/file-from-path.d.cts", "default": "./lib/file-from-path.cjs"}}}, "types": "./lib/form-data.d.ts", "typesVersions": {"*": {"file-from-path": ["./lib/file-from-path.d.ts"]}}, "devDependencies": {"@changesets/changelog-github": "0.4.8", "@changesets/cli": "2.26.2", "@octetstream/eslint-config": "7.2.1", "@types/node": "20.8.8", "@types/sinon": "10.0.20", "@typescript-eslint/eslint-plugin": "6.9.0", "@typescript-eslint/parser": "6.9.0", "ava": "5.3.1", "c8": "8.0.1", "cross-env": "7.0.3", "del-cli": "5.1.0", "eslint": "8.52.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "17.1.0", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-import": "2.29.0", "husky": "8.0.3", "lint-staged": "15.0.2", "node-fetch": "3.3.2", "pinst": "3.0.0", "sinon": "17.0.0", "ts-expect": "1.3.0", "ts-node": "10.9.1", "tsup": "7.2.0", "typescript": "5.2.2"}, "scripts": {"lint:types": "tsc --noEmit", "eslint": "eslint src/**/*.ts", "staged": "lint-staged", "coverage": "c8 pnpm test", "report:html": "c8 -r=html pnpm test", "ci": "c8 pnpm test && c8 report --reporter=json", "build": "pnpm exec del-cli lib && pnpm exec tsup", "test": "cross-env NODE_OPTIONS=\"--no-warnings --loader=ts-node/esm\" ava", "release": "pnpm run build && pnpm exec changeset publish"}}