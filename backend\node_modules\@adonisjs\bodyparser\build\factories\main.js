import {
  defineConfig
} from "../chunk-U2A7JUZH.js";
import {
  BodyParserMiddleware
} from "../chunk-TYDZTRBX.js";
import {
  MultipartFile
} from "../chunk-DKKMQ6FW.js";

// factories/file_factory.ts
var MultipartFileFactory = class {
  #parameters = {};
  /**
   * Merge factory params
   */
  merge(params) {
    this.#parameters = Object.assign(this.#parameters, params);
    return this;
  }
  /**
   * Create an instance of multipart file
   */
  create(validationOptions) {
    const file = new MultipartFile(
      {
        fieldName: this.#parameters.fieldName || "file",
        clientName: this.#parameters.clientName || this.#parameters.extname ? `file.${this.#parameters.extname}` : "file",
        headers: this.#parameters.headers || {}
      },
      validationOptions || {}
    );
    file.size = this.#parameters.size || 0;
    file.extname = this.#parameters.extname;
    file.type = this.#parameters.type;
    file.subtype = this.#parameters.subtype;
    file.state = "consumed";
    file.validate();
    return file;
  }
};

// factories/middleware_factory.ts
import lodash from "@poppinss/utils/lodash";
var BodyParserMiddlewareFactory = class {
  #config = defineConfig({});
  #featureFlags;
  #getConfig() {
    return this.#config;
  }
  merge(config) {
    this.#config = lodash.merge(this.#config, config);
    return this;
  }
  /**
   * Specify the feature flags to share with the bodyparser
   */
  withFeatureFlags(featureFlags) {
    this.#featureFlags = featureFlags;
    return this;
  }
  create() {
    return new BodyParserMiddleware(this.#getConfig(), this.#featureFlags);
  }
};
export {
  BodyParserMiddlewareFactory,
  MultipartFileFactory
};
