{"version": 3, "sources": ["../src/index.ts", "../src/tools.ts", "../src/event.ts", "../src/attendee.ts", "../src/alarm.ts", "../src/category.ts", "../src/types.ts", "../src/calendar.ts"], "sourcesContent": ["/**\n * ical-generator entrypoint\n */\n\n'use strict';\n\nimport ICalCalendar, {ICalCalendarData} from './calendar.ts';\n\n\n/**\n * Create a new, empty calendar and returns it.\n *\n * ```javascript\n * import ical from 'ical-generator';\n *\n * // or use require:\n * // const { default: ical } = require('ical-generator');\n *\n * const cal = ical();\n * ```\n *\n * You can pass options to setup your calendar or use setters to do this.\n *\n * ```javascript\n * import ical from 'ical-generator';\n *\n * // or use require:\n * // const { default: ical } = require('ical-generator');\n * const cal = ical({domain: 'sebbo.net'});\n *\n * // is the same as\n *\n * const cal = ical().domain('sebbo.net');\n *\n * // is the same as\n *\n * const cal = ical();\n * cal.domain('sebbo.net');\n * ```\n *\n * @param data Calendar data\n */\nfunction ical(data?: ICalCalendarData): ICalCalendar {\n    return new ICalCalendar(data);\n}\n\nexport default ical;\n\nexport {\n    default as ICalAlarm,\n    ICalAlarmData,\n    ICalAlarmBaseData,\n    ICalAlarmJSONData,\n    ICalAlarmRelatesTo,\n    ICalAlarmRepeatData,\n    ICalAlarmTriggerData,\n    ICalAlarmTriggerAfterData,\n    ICalAlarmTriggerBeforeData,\n    ICalAlarmType,\n    ICalAlarmTypeValue,\n    ICalAttachment,\n} from './alarm.ts';\n\nexport {\n    default as ICalAttendee,\n    ICalAttendeeData,\n    ICalAttendeeType,\n    ICalAttendeeRole,\n    ICalAttendeeStatus,\n    ICalAttendeeJSONData\n} from './attendee.ts';\n\nexport {\n    default as ICalCalendar,\n    ICalCalendarData,\n    ICalCalendarProdIdData,\n    ICalCalendarMethod,\n    ICalCalendarJSONData\n} from './calendar.ts';\n\nexport {\n    default as ICalCategory,\n    ICalCategoryData,\n    ICalCategoryJSONData\n} from './category.ts';\n\nexport {\n    default as ICalEvent,\n    ICalEventStatus,\n    ICalEventBusyStatus,\n    ICalEventTransparency,\n    ICalEventData,\n    ICalEventJSONData,\n    ICalEventJSONRepeatingData,\n    ICalEventClass,\n} from './event.ts';\n\nexport {\n    ICalDateTimeValue,\n    ICalRepeatingOptions,\n    ICalLocation,\n    ICalLocationWithTitle,\n    ICalLocationWithoutTitle,\n    ICalGeo,\n    ICalOrganizer,\n    ICalDescription,\n    ICalEventRepeatingFreq,\n    ICalWeekday,\n    ICalTimezone,\n    ICalMomentStub,\n    ICalMomentTimezoneStub,\n    ICalMomentDurationStub,\n    ICalLuxonDateTimeStub,\n    ICalDayJsStub,\n    ICalRRuleStub\n} from './types.ts';\n\nexport {\n    formatDate,\n    formatDateTZ,\n    escape,\n    foldLines\n} from './tools.ts';\n", "'use strict';\n\nimport {\n    ICalDateTimeValue,\n    ICalDayJsStub,\n    ICalLuxonDateTimeStub,\n    ICalMomentDurationStub,\n    ICalMomentStub,\n    ICalMomentTimezoneStub,\n    ICalOrganizer, ICalRRuleStub\n} from './types.ts';\n\n/**\n * Converts a valid date/time object supported by this library to a string.\n */\nexport function formatDate (timezone: string | null, d: ICalDateTimeValue, dateonly?: boolean, floating?: boolean): string {\n    if(timezone?.startsWith('/')) {\n        timezone = timezone.substr(1);\n    }\n\n    if(typeof d === 'string' || d instanceof Date) {\n        const m = new Date(d);\n\n        // (!dateonly && !floating) || !timezone => utc\n        let s = m.getUTCFullYear() +\n            String(m.getUTCMonth() + 1).padStart(2, '0') +\n            m.getUTCDate().toString().padStart(2, '0');\n\n        // (dateonly || floating) && timezone => tz\n        if(timezone) {\n            s = m.getFullYear() +\n                String(m.getMonth() + 1).padStart(2, '0') +\n                m.getDate().toString().padStart(2, '0');\n        }\n\n        if(dateonly) {\n            return s;\n        }\n\n        if(timezone) {\n            s += 'T' + m.getHours().toString().padStart(2, '0') +\n                m.getMinutes().toString().padStart(2, '0') +\n                m.getSeconds().toString().padStart(2, '0');\n\n            return s;\n        }\n\n        s += 'T' + m.getUTCHours().toString().padStart(2, '0') +\n            m.getUTCMinutes().toString().padStart(2, '0') +\n            m.getUTCSeconds().toString().padStart(2, '0') +\n            (floating ? '' : 'Z');\n\n        return s;\n    }\n    else if(isMoment(d)) {\n        // @see https://momentjs.com/timezone/docs/#/using-timezones/parsing-in-zone/\n        const m = timezone\n            ? (isMomentTZ(d) && !d.tz() ? d.clone().tz(timezone) : d)\n            : (floating || (dateonly && isMomentTZ(d) && d.tz()) ? d : d.utc());\n\n        return m.format('YYYYMMDD') + (!dateonly ? (\n            'T' + m.format('HHmmss') + (floating || timezone ? '' : 'Z')\n        ) : '');\n    }\n    else if(isLuxonDate(d)) {\n        const m = timezone\n            ? d.setZone(timezone)\n            : (floating || (dateonly && d.zone.type !== 'system') ? d : d.setZone('utc'));\n\n        return m.toFormat('yyyyLLdd') + (!dateonly ? (\n            'T' + m.toFormat('HHmmss') + (floating || timezone ? '' : 'Z')\n        ) : '');\n    }\n    else {\n        // @see https://day.js.org/docs/en/plugin/utc\n\n        let m = d;\n        if(timezone) {\n            m = typeof d.tz === 'function' ? d.tz(timezone) : d;\n        }\n        else if(floating) {\n            // m = d;\n        }\n\n        else if (typeof d.utc === 'function') {\n            m = d.utc();\n        }\n        else {\n            throw new Error('Unable to convert dayjs object to UTC value: UTC plugin is not available!');\n        }\n\n        return m.format('YYYYMMDD') + (!dateonly ? (\n            'T' + m.format('HHmmss') + (floating || timezone ? '' : 'Z')\n        ) : '');\n    }\n}\n\n/**\n * Converts a valid date/time object supported by this library to a string.\n * For information about this format, see RFC 5545, section 3.3.5\n * https://tools.ietf.org/html/rfc5545#section-3.3.5\n */\nexport function formatDateTZ (timezone: string | null, property: string, date: ICalDateTimeValue | Date | string, eventData?: {floating?: boolean | null, timezone?: string | null}): string {\n    let tzParam = '';\n    let floating = eventData?.floating || false;\n\n    if (eventData?.timezone) {\n        tzParam = ';TZID=' + eventData.timezone;\n\n        // This isn't a 'floating' event because it has a timezone;\n        // but we use it to omit the 'Z' UTC specifier in formatDate()\n        floating = true;\n    }\n\n    return property + tzParam + ':' + formatDate(timezone, date, false, floating);\n}\n\n/**\n * Escapes special characters in the given string\n */\nexport function escape (str: string | unknown, inQuotes: boolean): string {\n    return String(str).replace(inQuotes ? /[\\\\\"]/g : /[\\\\;,]/g, function (match) {\n        return '\\\\' + match;\n    }).replace(/(?:\\r\\n|\\r|\\n)/g, '\\\\n');\n}\n\n/**\n * Trim line length of given string\n */\nexport function foldLines (input: string): string {\n    return input.split('\\r\\n').map(function (line) {\n        let result = '';\n        let c = 0;\n        for (let i = 0; i < line.length; i++) {\n            let ch = line.charAt(i);\n\n            // surrogate pair, see https://mathiasbynens.be/notes/javascript-encoding#surrogate-pairs\n            if (ch >= '\\ud800' && ch <= '\\udbff') {\n                ch += line.charAt(++i);\n            }\n\n            // TextEncoder is available in browsers and node.js >= 11.0.0\n            const charsize = new TextEncoder().encode(ch).length;\n            c += charsize;\n            if (c > 74) {\n                result += '\\r\\n ';\n                c = charsize;\n            }\n\n            result += ch;\n        }\n        return result;\n    }).join('\\r\\n');\n}\n\nexport function addOrGetCustomAttributes (data: {x: [string, string][]}, keyOrArray: ({key: string, value: string})[] | [string, string][] | Record<string, string>): void;\nexport function addOrGetCustomAttributes (data: {x: [string, string][]}, keyOrArray: string, value: string): void;\nexport function addOrGetCustomAttributes (data: {x: [string, string][]}): ({key: string, value: string})[];\nexport function addOrGetCustomAttributes (data: {x: [string, string][]}, keyOrArray?: ({key: string, value: string})[] | [string, string][] | Record<string, string> | string  | undefined, value?: string | undefined): void | ({key: string, value: string})[] {\n    if (Array.isArray(keyOrArray)) {\n        data.x = keyOrArray.map((o: {key: string, value: string} | [string, string]) => {\n            if(Array.isArray(o)) {\n                return o;\n            }\n            if (typeof o.key !== 'string' || typeof o.value !== 'string') {\n                throw new Error('Either key or value is not a string!');\n            }\n            if (o.key.substr(0, 2) !== 'X-') {\n                throw new Error('Key has to start with `X-`!');\n            }\n\n            return [o.key, o.value] as [string, string];\n        });\n    }\n    else if (typeof keyOrArray === 'object') {\n        data.x = Object.entries(keyOrArray).map(([key, value]) => {\n            if (typeof key !== 'string' || typeof value !== 'string') {\n                throw new Error('Either key or value is not a string!');\n            }\n            if (key.substr(0, 2) !== 'X-') {\n                throw new Error('Key has to start with `X-`!');\n            }\n\n            return [key, value];\n        });\n    }\n    else if (typeof keyOrArray === 'string' && typeof value === 'string') {\n        if (keyOrArray.substr(0, 2) !== 'X-') {\n            throw new Error('Key has to start with `X-`!');\n        }\n\n        data.x.push([keyOrArray, value]);\n    }\n    else {\n        return data.x.map(a => ({\n            key: a[0],\n            value: a[1]\n        }));\n    }\n}\n\nexport function generateCustomAttributes (data: {x: [string, string][]}): string {\n    const str = data.x\n        .map(([key, value]) => key.toUpperCase() + ':' + escape(value, false))\n        .join('\\r\\n');\n    return str.length ? str + '\\r\\n' : '';\n}\n\n/**\n * Check the given string or ICalOrganizer. Parses\n * the string for name and email address if possible.\n *\n * @param attribute Attribute name for error messages\n * @param value Value to parse name/email from\n */\nexport function checkNameAndMail (attribute: string, value: string | ICalOrganizer): ICalOrganizer {\n    let result: ICalOrganizer | null = null;\n\n    if (typeof value === 'string') {\n        const match = value.match(/^(.+) ?<([^>]+)>$/);\n        if (match) {\n            result = {\n                name: match[1].trim(),\n                email: match[2].trim()\n            };\n        }\n        else if(value.includes('@')) {\n            result = {\n                name: value.trim(),\n                email: value.trim()\n            };\n        }\n    }\n    else if (typeof value === 'object') {\n        result = {\n            name: value.name,\n            email: value.email,\n            mailto: value.mailto,\n            sentBy: value.sentBy\n        };\n    }\n\n    if (!result && typeof value === 'string') {\n        throw new Error(\n            '`' + attribute + '` isn\\'t formated correctly. See https://sebbo2002.github.io/ical-generator/develop/'+\n            'reference/interfaces/ICalOrganizer.html'\n        );\n    }\n    else if (!result) {\n        throw new Error(\n            '`' + attribute + '` needs to be a valid formed string or an object. See https://sebbo2002.github.io/'+\n            'ical-generator/develop/reference/interfaces/ICalOrganizer.html'\n        );\n    }\n\n    if (!result.name) {\n        throw new Error('`' + attribute + '.name` is empty!');\n    }\n\n    return result;\n}\n\n/**\n * Checks if the given string `value` is a\n * valid one for the type `type`\n */\nexport function checkEnum(type: Record<string, string>, value: unknown): unknown {\n    const allowedValues = Object.values(type);\n    const valueStr = String(value).toUpperCase();\n\n    if (!valueStr || !allowedValues.includes(valueStr)) {\n        throw new Error(`Input must be one of the following: ${allowedValues.join(', ')}`);\n    }\n\n    return valueStr;\n}\n\n/**\n * Checks if the given input is a valid date and\n * returns the internal representation (= moment object)\n */\nexport function checkDate(value: ICalDateTimeValue, attribute: string): ICalDateTimeValue {\n\n    // Date & String\n    if(\n        (value instanceof Date && isNaN(value.getTime())) ||\n        (typeof value === 'string' && isNaN(new Date(value).getTime()))\n    ) {\n        throw new Error(`\\`${attribute}\\` has to be a valid date!`);\n    }\n    if(value instanceof Date || typeof value === 'string') {\n        return value;\n    }\n\n    // Luxon\n    if(isLuxonDate(value) && value.isValid === true) {\n        return value;\n    }\n\n    // Moment / Moment Timezone\n    if((isMoment(value) || isDayjs(value)) && value.isValid()) {\n        return value;\n    }\n\n    throw new Error(`\\`${attribute}\\` has to be a valid date!`);\n}\n\nexport function toDate(value: ICalDateTimeValue): Date {\n    if(typeof value === 'string' || value instanceof Date) {\n        return new Date(value);\n    }\n\n    if(isLuxonDate(value)) {\n        return value.toJSDate();\n    }\n\n    return value.toDate();\n}\n\nexport function isMoment(value: ICalDateTimeValue): value is ICalMomentStub {\n\n    // @ts-expect-error _isAMomentObject is a private property\n    return value != null && value._isAMomentObject != null;\n}\nexport function isMomentTZ(value: ICalDateTimeValue): value is ICalMomentTimezoneStub {\n    return isMoment(value) && 'tz' in value && typeof value.tz === 'function';\n}\nexport function isDayjs(value: ICalDateTimeValue): value is ICalDayJsStub {\n    return typeof value === 'object' &&\n        value !== null &&\n        !(value instanceof Date) &&\n        !isMoment(value) &&\n        !isLuxonDate(value);\n}\nexport function isLuxonDate(value: ICalDateTimeValue): value is ICalLuxonDateTimeStub {\n    return typeof value === 'object' && value !== null && 'toJSDate' in value && typeof value.toJSDate === 'function';\n}\n\nexport function isMomentDuration(value: unknown): value is ICalMomentDurationStub {\n    return value !== null && typeof value === 'object' && 'asSeconds' in value && typeof value.asSeconds === 'function';\n}\n\nexport function isRRule(value: unknown): value is ICalRRuleStub {\n    return value !== null && typeof value === 'object' && 'between' in value && typeof value.between === 'function' && typeof value.toString === 'function';\n}\n\nexport function toJSON(value: ICalDateTimeValue | null | undefined): string | null | undefined {\n    if(!value) {\n        return null;\n    }\n    if(typeof value === 'string') {\n        return value;\n    }\n\n    return value.toJSON();\n}\n\nexport function toDurationString(seconds: number): string {\n    let string = '';\n\n    // < 0\n    if(seconds < 0) {\n        string = '-';\n        seconds *= -1;\n    }\n\n    string += 'P';\n\n    // DAYS\n    if(seconds >= 86400) {\n        string += Math.floor(seconds / 86400) + 'D';\n        seconds %= 86400;\n    }\n    if(!seconds && string.length > 1) {\n        return string;\n    }\n\n    string += 'T';\n\n    // HOURS\n    if(seconds >= 3600) {\n        string += Math.floor(seconds / 3600) + 'H';\n        seconds %= 3600;\n    }\n\n    // MINUTES\n    if(seconds >= 60) {\n        string += Math.floor(seconds / 60) + 'M';\n        seconds %= 60;\n    }\n\n    // SECONDS\n    if(seconds > 0) {\n        string += seconds + 'S';\n    }\n    else if(string.length <= 2) {\n        string += '0S';\n    }\n\n    return string;\n}\n", "'use strict';\n\nimport uuid from 'uuid-random';\nimport {\n    addOrGetCustomAttributes,\n    checkDate,\n    checkEnum,\n    checkNameAndMail,\n    escape,\n    formatDate,\n    formatDateTZ,\n    generateCustomAttributes,\n    isRRule,\n    toDate,\n    toJSON\n} from './tools.ts';\nimport ICalAttendee, {ICalAttendeeData} from './attendee.ts';\nimport ICalAlarm, {ICalAlarmData} from './alarm.ts';\nimport ICalCategory, {ICalCategoryData} from './category.ts';\nimport ICalCalendar from './calendar.ts';\nimport {\n    ICalDateTimeValue,\n    ICalDescription,\n    ICalEventRepeatingFreq,\n    ICalLocation,\n    ICalOrganizer,\n    ICalRepeatingOptions,\n    ICalRRuleStub,\n    ICalWeekday\n} from './types.ts';\n\n\nexport enum ICalEventStatus {\n    CONFIRMED = 'CONFIRMED',\n    TENTATIVE = 'TENTATIVE',\n    CANCELLED = 'CANCELLED'\n}\n\nexport enum ICalEventBusyStatus {\n    FREE = 'FREE',\n    TENTATIVE = 'TENTATIVE',\n    BUSY = 'BUSY',\n    OOF = 'OOF'\n}\n\nexport enum ICalEventTransparency {\n    TRANSPARENT = 'TRANSPARENT',\n    OPAQUE = 'OPAQUE'\n}\n\nexport enum ICalEventClass {\n    PUBLIC = 'PUBLIC',\n    PRIVATE = 'PRIVATE',\n    CONFIDENTIAL = 'CONFIDENTIAL'\n}\n\nexport interface ICalEventData {\n    id?: string | number | null,\n    sequence?: number,\n    start: ICalDateTimeValue,\n    end?: ICalDateTimeValue | null,\n    recurrenceId?: ICalDateTimeValue | null,\n    timezone?: string | null,\n    stamp?: ICalDateTimeValue,\n    allDay?: boolean,\n    floating?: boolean,\n    repeating?: ICalRepeatingOptions | ICalRRuleStub | string | null,\n    summary?: string,\n    location?: ICalLocation | string | null,\n    description?: ICalDescription | string | null,\n    organizer?: ICalOrganizer | string | null,\n    attendees?: ICalAttendee[] | ICalAttendeeData[],\n    alarms?: ICalAlarm[] | ICalAlarmData[],\n    categories?: ICalCategory[] | ICalCategoryData[],\n    status?: ICalEventStatus | null,\n    busystatus?: ICalEventBusyStatus | null,\n    priority?: number | null,\n    url?: string | null,\n    attachments?: string[],\n    transparency?: ICalEventTransparency | null,\n    created?: ICalDateTimeValue | null,\n    lastModified?: ICalDateTimeValue | null,\n    class?: ICalEventClass | null;\n    x?: {key: string, value: string}[] | [string, string][] | Record<string, string>;\n}\n\ninterface ICalEventInternalData {\n    id: string,\n    sequence: number,\n    start: ICalDateTimeValue,\n    end: ICalDateTimeValue | null,\n    recurrenceId: ICalDateTimeValue | null,\n    timezone: string | null,\n    stamp: ICalDateTimeValue,\n    allDay: boolean,\n    floating: boolean,\n    repeating: ICalEventJSONRepeatingData | ICalRRuleStub | string | null,\n    summary: string,\n    location: ICalLocation | null,\n    description: ICalDescription | null,\n    organizer: ICalOrganizer | null,\n    attendees: ICalAttendee[],\n    alarms: ICalAlarm[],\n    categories: ICalCategory[],\n    status: ICalEventStatus | null,\n    busystatus: ICalEventBusyStatus | null,\n    priority: number | null,\n    url: string | null,\n    attachments: string[],\n    transparency: ICalEventTransparency | null,\n    created: ICalDateTimeValue | null,\n    lastModified: ICalDateTimeValue | null,\n    class: ICalEventClass | null,\n    x: [string, string][];\n}\n\nexport interface ICalEventJSONData {\n    id: string,\n    sequence: number,\n    start: string,\n    end: string | null,\n    recurrenceId: string | null,\n    timezone: string | null,\n    stamp: string,\n    allDay: boolean,\n    floating: boolean,\n    repeating: ICalEventJSONRepeatingData | string | null,\n    summary: string,\n    location: ICalLocation | null,\n    description: ICalDescription | null,\n    organizer: ICalOrganizer | null,\n    attendees: ICalAttendee[],\n    alarms: ICalAlarm[],\n    categories: ICalCategory[],\n    status: ICalEventStatus | null,\n    busystatus: ICalEventBusyStatus | null,\n    priority?: number | null,\n    url: string | null,\n    attachments: string[],\n    transparency: ICalEventTransparency | null,\n    created: string | null,\n    lastModified: string | null,\n    x: {key: string, value: string}[];\n}\n\nexport interface ICalEventJSONRepeatingData {\n    freq: ICalEventRepeatingFreq;\n    count?: number;\n    interval?: number;\n    until?: ICalDateTimeValue;\n    byDay?: ICalWeekday[];\n    byMonth?: number[];\n    byMonthDay?: number[];\n    bySetPos?: number[];\n    exclude?: ICalDateTimeValue[];\n    startOfWeek?: ICalWeekday;\n}\n\n\n/**\n * Usually you get an {@link ICalEvent} object like this:\n * ```javascript\n * import ical from 'ical-generator';\n * const calendar = ical();\n * const event = calendar.createEvent();\n * ```\n */\nexport default class ICalEvent {\n    private readonly data: ICalEventInternalData;\n    private readonly calendar: ICalCalendar;\n\n    /**\n     * Constructor of [[`ICalEvent`]. The calendar reference is\n     * required to query the calendar's timezone when required.\n     *\n     * @param data Calendar Event Data\n     * @param calendar Reference to ICalCalendar object\n     */\n    constructor(data: ICalEventData, calendar: ICalCalendar) {\n        this.data = {\n            id: uuid(),\n            sequence: 0,\n            start: new Date(),\n            end: null,\n            recurrenceId: null,\n            timezone: null,\n            stamp: new Date(),\n            allDay: false,\n            floating: false,\n            repeating: null,\n            summary: '',\n            location: null,\n            description: null,\n            organizer: null,\n            attendees: [],\n            alarms: [],\n            categories: [],\n            status: null,\n            busystatus: null,\n            priority: null,\n            url: null,\n            attachments: [],\n            transparency: null,\n            created: null,\n            lastModified: null,\n            class: null,\n            x: []\n        };\n\n        this.calendar = calendar;\n        if (!calendar) {\n            throw new Error('`calendar` option required!');\n        }\n\n        if (data.id) this.id(data.id);\n        if (data.sequence !== undefined) this.sequence(data.sequence);\n        if (data.start) this.start(data.start);\n        if (data.end !== undefined) this.end(data.end);\n        if (data.recurrenceId !== undefined) this.recurrenceId(data.recurrenceId);\n        if (data.timezone !== undefined) this.timezone(data.timezone);\n        if (data.stamp !== undefined) this.stamp(data.stamp);\n        if (data.allDay !== undefined) this.allDay(data.allDay);\n        if (data.floating !== undefined) this.floating(data.floating);\n        if (data.repeating !== undefined) this.repeating(data.repeating);\n        if (data.summary !== undefined) this.summary(data.summary);\n        if (data.location !== undefined) this.location(data.location);\n        if (data.description !== undefined) this.description(data.description);\n        if (data.organizer !== undefined) this.organizer(data.organizer);\n        if (data.attendees !== undefined) this.attendees(data.attendees);\n        if (data.alarms !== undefined) this.alarms(data.alarms);\n        if (data.categories !== undefined) this.categories(data.categories);\n        if (data.status !== undefined) this.status(data.status);\n        if (data.busystatus !== undefined) this.busystatus(data.busystatus);\n        if (data.priority !== undefined) this.priority(data.priority);\n        if (data.url !== undefined) this.url(data.url);\n        if (data.attachments !== undefined) this.attachments(data.attachments);\n        if (data.transparency !== undefined) this.transparency(data.transparency);\n        if (data.created !== undefined) this.created(data.created);\n        if (data.lastModified !== undefined) this.lastModified(data.lastModified);\n        if (data.class !== undefined) this.class(data.class);\n        if (data.x !== undefined) this.x(data.x);\n    }\n\n    /**\n     * Get the event's ID\n     * @since 0.2.0\n     */\n    id(): string;\n\n    /**\n     * Use this method to set the event's ID.\n     * If not set, a UUID will be generated randomly.\n     *\n     * @param id Event ID you want to set\n     */\n    id(id: string | number): this;\n    id(id?: string | number): this | string {\n        if (id === undefined) {\n            return this.data.id;\n        }\n\n        this.data.id = String(id);\n        return this;\n    }\n\n    /**\n     * Get the event's ID\n     * @since 0.2.0\n     * @alias id\n     */\n    uid(): string;\n\n    /**\n     * Use this method to set the event's ID.\n     * If not set, a UUID will be generated randomly.\n     *\n     * @param id Event ID you want to set\n     * @alias id\n     */\n    uid(id: string | number): this;\n    uid(id?: string | number): this | string {\n        return id === undefined ? this.id() : this.id(id);\n    }\n\n    /**\n     * Get the event's SEQUENCE number. Use this method to get the event's\n     * revision sequence number of the calendar component within a sequence of revisions.\n     *\n     * @since 0.2.6\n     */\n    sequence(): number;\n\n    /**\n     * Set the event's SEQUENCE number. For a new event, this should be zero.\n     * Each time the organizer  makes a significant revision, the sequence\n     * number should be incremented.\n     *\n     * @param sequence Sequence number or null to unset it\n     */\n    sequence(sequence: number): this;\n    sequence(sequence?: number): this | number {\n        if (sequence === undefined) {\n            return this.data.sequence;\n        }\n\n        const s = parseInt(String(sequence), 10);\n        if (isNaN(s)) {\n            throw new Error('`sequence` must be a number!');\n        }\n\n        this.data.sequence = sequence;\n        return this;\n    }\n\n    /**\n     * Get the event start time which is currently\n     * set. Can be any supported date object.\n     *\n     * @since 0.2.0\n     */\n    start(): ICalDateTimeValue;\n\n    /**\n     * Set the appointment date of beginning, which is required for all events.\n     * You can use any supported date object, see\n     * [Readme](https://github.com/sebbo2002/ical-generator#-date-time--timezones)\n     * for details about supported values and timezone handling.\n     *\n     * ```typescript\n     * import ical from 'ical-generator';\n     *\n     * const cal = ical();\n     *\n     * const event = cal.createEvent({\n     *   start: new Date('2020-01-01')\n     * });\n     *\n     * // overwrites old start date\n     * event.start(new Date('2024-02-01'));\n     *\n     * cal.toString();\n     * ```\n     *\n     * ```text\n     * BEGIN:VCALENDAR\n     * VERSION:2.0\n     * PRODID:-//sebbo.net//ical-generator//EN\n     * BEGIN:VEVENT\n     * UID:7e2aee64-b07a-4256-9b3e-e9eaa452bac8\n     * SEQUENCE:0\n     * DTSTAMP:20240212T190915Z\n     * DTSTART:20240201T000000Z\n     * SUMMARY:\n     * END:VEVENT\n     * END:VCALENDAR\n     * ```\n     *\n     * @since 0.2.0\n     */\n    start(start: ICalDateTimeValue): this;\n    start(start?: ICalDateTimeValue): this | ICalDateTimeValue {\n        if (start === undefined) {\n            this.swapStartAndEndIfRequired();\n            return this.data.start;\n        }\n\n        this.data.start = checkDate(start, 'start');\n        return this;\n    }\n\n    /**\n     * Get the event end time which is currently\n     * set. Can be any supported date object.\n     *\n     * @since 0.2.0\n     */\n    end(): ICalDateTimeValue | null;\n\n    /**\n     * Set the appointment date of end. You can use any supported date object, see\n     * [readme](https://github.com/sebbo2002/ical-generator#-date-time--timezones)\n     * for details about supported values and timezone handling.\n     *\n     * @since 0.2.0\n     */\n    end(end: ICalDateTimeValue | null): this;\n    end(end?: ICalDateTimeValue | null): this | ICalDateTimeValue | null {\n        if (end === undefined) {\n            this.swapStartAndEndIfRequired();\n            return this.data.end;\n        }\n        if (end === null) {\n            this.data.end = null;\n            return this;\n        }\n\n        this.data.end = checkDate(end, 'end');\n        return this;\n    }\n\n    /**\n     * Checks if the start date is after the end date and swaps them if necessary.\n     * @private\n     */\n    private swapStartAndEndIfRequired(): void {\n        if (this.data.start && this.data.end && toDate(this.data.start).getTime() > toDate(this.data.end).getTime()) {\n            const t = this.data.start;\n            this.data.start = this.data.end;\n            this.data.end = t;\n        }\n    }\n\n    /**\n     * Get the event's recurrence id\n     * @since 0.2.0\n     */\n    recurrenceId(): ICalDateTimeValue | null;\n\n    /**\n     * Set the event's recurrence id. You can use any supported date object, see\n     * [readme](https://github.com/sebbo2002/ical-generator#-date-time--timezones)\n     * for details about supported values and timezone handling.\n     *\n     * @since 0.2.0\n     */\n    recurrenceId(recurrenceId: ICalDateTimeValue | null): this;\n    recurrenceId(recurrenceId?: ICalDateTimeValue | null): this | ICalDateTimeValue | null {\n        if (recurrenceId === undefined) {\n            return this.data.recurrenceId;\n        }\n        if (recurrenceId === null) {\n            this.data.recurrenceId = null;\n            return this;\n        }\n\n        this.data.recurrenceId = checkDate(recurrenceId, 'recurrenceId');\n        return this;\n    }\n\n    /**\n     * Get the event's timezone.\n     * @since 0.2.6\n     */\n    timezone(): string | null;\n\n    /**\n     * Sets the time zone to be used for this event. If a time zone has been\n     * defined in both the event and the calendar, the time zone of the event\n     * is used.\n     *\n     * Please note that if the time zone is set, ical-generator assumes\n     * that all times are already in the correct time zone. Alternatively,\n     * a `moment-timezone` or a Luxon object can be passed with `setZone`,\n     * ical-generator will then set the time zone itself.\n     *\n     * This and the 'floating' flag (see below) are mutually exclusive, and setting a timezone will unset the\n     * 'floating' flag.  If neither 'timezone' nor 'floating' are set, the date will be output with in UTC format\n     * (see [date-time form #2 in section 3.3.5 of RFC 554](https://tools.ietf.org/html/rfc5545#section-3.3.5)).\n     *\n     * See [Readme](https://github.com/sebbo2002/ical-generator#-date-time--timezones) for details about\n     * supported values and timezone handling.\n     *\n     * ```javascript\n     * event.timezone('America/New_York');\n     * ```\n     *\n     * @see https://github.com/sebbo2002/ical-generator#-date-time--timezones\n     * @since 0.2.6\n     */\n    timezone(timezone: string | null): this;\n    timezone(timezone?: string | null): this | string | null {\n        if (timezone === undefined && this.data.timezone !== null) {\n            return this.data.timezone;\n        }\n        if (timezone === undefined) {\n            return this.calendar.timezone();\n        }\n\n        this.data.timezone = timezone && timezone !== 'UTC' ? timezone.toString() : null;\n        if (this.data.timezone) {\n            this.data.floating = false;\n        }\n\n        return this;\n    }\n\n    /**\n     * Get the event's timestamp\n     * @since 0.2.0\n     */\n    stamp(): ICalDateTimeValue;\n\n    /**\n     * Set the appointment date of creation. Defaults to the current time and date (`new Date()`). You can use\n     * any supported date object, see [readme](https://github.com/sebbo2002/ical-generator#-date-time--timezones)\n     * for details about supported values and timezone handling.\n     *\n     * @since 0.2.0\n     */\n    stamp(stamp: ICalDateTimeValue): this;\n    stamp(stamp?: ICalDateTimeValue): this | ICalDateTimeValue {\n        if (stamp === undefined) {\n            return this.data.stamp;\n        }\n\n        this.data.stamp = checkDate(stamp, 'stamp');\n        return this;\n    }\n\n    /**\n     * Get the event's timestamp\n     * @since 0.2.0\n     * @alias stamp\n     */\n    timestamp(): ICalDateTimeValue;\n\n    /**\n     * Set the appointment date of creation. Defaults to the current time and date (`new Date()`). You can use\n     * any supported date object, see [readme](https://github.com/sebbo2002/ical-generator#-date-time--timezones)\n     * for details about supported values and timezone handling.\n     *\n     * @since 0.2.0\n     * @alias stamp\n     */\n    timestamp(stamp: ICalDateTimeValue): this;\n    timestamp(stamp?: ICalDateTimeValue): this | ICalDateTimeValue {\n        if (stamp === undefined) {\n            return this.stamp();\n        }\n\n        return this.stamp(stamp);\n    }\n\n    /**\n     * Get the event's allDay flag\n     * @since 0.2.0\n     */\n    allDay(): boolean;\n\n    /**\n     * Set the event's allDay flag.\n     *\n     * ```javascript\n     * event.allDay(true); // → appointment is for the whole day\n     * ```\n     *\n     * ```typescript\n     * import ical from 'ical-generator';\n     *\n     * const cal = ical();\n     *\n     * cal.createEvent({\n     *   start: new Date('2020-01-01'),\n     *   summary: 'Very Important Day',\n     *   allDay: true\n     * });\n     *\n     * cal.toString();\n     * ```\n     *\n     * ```text\n     * BEGIN:VCALENDAR\n     * VERSION:2.0\n     * PRODID:-//sebbo.net//ical-generator//EN\n     * BEGIN:VEVENT\n     * UID:1964fe8d-32c5-4f2a-bd62-7d9d7de5992b\n     * SEQUENCE:0\n     * DTSTAMP:20240212T191956Z\n     * DTSTART;VALUE=DATE:20200101\n     * X-MICROSOFT-CDO-ALLDAYEVENT:TRUE\n     * X-MICROSOFT-MSNCALENDAR-ALLDAYEVENT:TRUE\n     * SUMMARY:Very Important Day\n     * END:VEVENT\n     * END:VCALENDAR\n     * ```\n     *\n     * @since 0.2.0\n     */\n    allDay(allDay: boolean): this;\n    allDay(allDay?: boolean): this | boolean {\n        if (allDay === undefined) {\n            return this.data.allDay;\n        }\n\n        this.data.allDay = Boolean(allDay);\n        return this;\n    }\n\n    /**\n     * Get the event's floating flag.\n     * @since 0.2.0\n     */\n    floating(): boolean;\n    floating(floating: boolean): this;\n\n    /**\n     * Set the event's floating flag. This unsets the event's timezone.\n     * Events whose floating flag is set to true always take place at the\n     * same time, regardless of the time zone.\n     *\n     * ```typescript\n     * import ical from 'ical-generator';\n     *\n     * const cal = ical();\n     *\n     * cal.createEvent({\n     *   start: new Date('2020-01-01T20:00:00Z'),\n     *   summary: 'Always at 20:00 in every <Timezone',\n     *   floating: true\n     * });\n     *\n     * cal.toString();\n     * ```\n     *\n     * ```text\n     * BEGIN:VCALENDAR\n     * VERSION:2.0\n     * PRODID:-//sebbo.net//ical-generator//EN\n     * BEGIN:VEVENT\n     * UID:5d7278f9-ada3-40ef-83d1-23c29ce0a763\n     * SEQUENCE:0\n     * DTSTAMP:20240212T192214Z\n     * DTSTART:20200101T200000\n     * SUMMARY:Always at 20:00 in every <Timezone\n     * END:VEVENT\n     * END:VCALENDAR\n     * ```\n     *\n     * @since 0.2.0\n     */\n    floating(floating?: boolean): this | boolean {\n        if (floating === undefined) {\n            return this.data.floating;\n        }\n\n        this.data.floating = Boolean(floating);\n        if (this.data.floating) {\n            this.data.timezone = null;\n        }\n\n        return this;\n    }\n\n    /**\n     * Get the event's repeating options\n     * @since 0.2.0\n     */\n    repeating(): ICalEventJSONRepeatingData | ICalRRuleStub | string | null;\n\n    /**\n     * Set the event's repeating options by passing an {@link ICalRepeatingOptions} object.\n     *\n     * ```javascript\n     * event.repeating({\n     *    freq: 'MONTHLY', // required\n     *    count: 5,\n     *    interval: 2,\n     *    until: new Date('Jan 01 2014 00:00:00 UTC'),\n     *    byDay: ['su', 'mo'], // repeat only sunday and monday\n     *    byMonth: [1, 2], // repeat only in january and february,\n     *    byMonthDay: [1, 15], // repeat only on the 1st and 15th\n     *    bySetPos: 3, // repeat every 3rd sunday (will take the first element of the byDay array)\n     *    exclude: [new Date('Dec 25 2013 00:00:00 UTC')], // exclude these dates\n     *    excludeTimezone: 'Europe/Berlin', // timezone of exclude\n     *    wkst: 'SU' // Start the week on Sunday, default is Monday\n     * });\n     * ```\n     *\n     * **Example:**\n     *\n     *```typescript\n     * import ical, { ICalEventRepeatingFreq } from 'ical-generator';\n     *\n     * const cal = ical();\n     *\n     * const event = cal.createEvent({\n     *   start: new Date('2020-01-01T20:00:00Z'),\n     *   summary: 'Repeating Event'\n     * });\n     * event.repeating({\n     *   freq: ICalEventRepeatingFreq.WEEKLY,\n     *   count: 4\n     * });\n     *\n     * cal.toString();\n     * ```\n     *\n     * ```text\n     * BEGIN:VCALENDAR\n     * VERSION:2.0\n     * PRODID:-//sebbo.net//ical-generator//EN\n     * BEGIN:VEVENT\n     * UID:b80e6a68-c2cd-48f5-b94d-cecc7ce83871\n     * SEQUENCE:0\n     * DTSTAMP:20240212T193646Z\n     * DTSTART:20200101T200000Z\n     * RRULE:FREQ=WEEKLY;COUNT=4\n     * SUMMARY:Repeating Event\n     * END:VEVENT\n     * END:VCALENDAR\n     * ```\n     *\n     * @since 0.2.0\n     */\n    repeating(repeating: ICalRepeatingOptions | null): this;\n\n    /**\n     * Set the event's repeating options by passing an [RRule object](https://github.com/jakubroztocil/rrule).\n     * @since 2.0.0-develop.5\n     *\n     * ```typescript\n     * import ical from 'ical-generator';\n     * import { datetime, RRule } from 'rrule';\n     *\n     * const cal = ical();\n     *\n     * const event = cal.createEvent({\n     *   start: new Date('2020-01-01T20:00:00Z'),\n     *   summary: 'Repeating Event'\n     * });\n     *\n     * const rule = new RRule({\n     *   freq: RRule.WEEKLY,\n     *   interval: 5,\n     *   byweekday: [RRule.MO, RRule.FR],\n     *   dtstart: datetime(2012, 2, 1, 10, 30),\n     *   until: datetime(2012, 12, 31)\n     * })\n     * event.repeating(rule);\n     *\n     * cal.toString();\n     * ```\n     *\n     * ```text\n     * BEGIN:VCALENDAR\n     * VERSION:2.0\n     * PRODID:-//sebbo.net//ical-generator//EN\n     * BEGIN:VEVENT\n     * UID:36585e40-8fa8-460d-af0c-88b6f434030b\n     * SEQUENCE:0\n     * DTSTAMP:20240212T193827Z\n     * DTSTART:20200101T200000Z\n     * RRULE:FREQ=WEEKLY;INTERVAL=5;BYDAY=MO,FR;UNTIL=20121231T000000Z\n     * SUMMARY:Repeating Event\n     * END:VEVENT\n     * END:VCALENDAR\n     * ```\n     */\n    repeating(repeating: ICalRRuleStub | null): this;\n\n    /**\n     * Set the events repeating options by passing a string which is inserted in the ical file.\n     * @since 2.0.0-develop.5\n     */\n    repeating(repeating: string | null): this;\n\n    /**\n     * @internal\n     */\n    repeating(repeating: ICalRepeatingOptions | ICalRRuleStub | string | null): this;\n    repeating(repeating?: ICalRepeatingOptions | ICalRRuleStub | string | null): this | ICalEventJSONRepeatingData | ICalRRuleStub | string | null {\n        if (repeating === undefined) {\n            return this.data.repeating;\n        }\n        if (!repeating) {\n            this.data.repeating = null;\n            return this;\n        }\n        if(isRRule(repeating) || typeof repeating === 'string') {\n            this.data.repeating = repeating;\n            return this;\n        }\n\n        this.data.repeating = {\n            freq: checkEnum(ICalEventRepeatingFreq, repeating.freq) as ICalEventRepeatingFreq\n        };\n\n        if (repeating.count) {\n            if (!isFinite(repeating.count)) {\n                throw new Error('`repeating.count` must be a finite number!');\n            }\n\n            this.data.repeating.count = repeating.count;\n        }\n\n        if (repeating.interval) {\n            if (!isFinite(repeating.interval)) {\n                throw new Error('`repeating.interval` must be a finite number!');\n            }\n\n            this.data.repeating.interval = repeating.interval;\n        }\n\n        if (repeating.until !== undefined) {\n            this.data.repeating.until = checkDate(repeating.until, 'repeating.until');\n        }\n\n        if (repeating.byDay) {\n            const byDayArray = Array.isArray(repeating.byDay) ? repeating.byDay : [repeating.byDay];\n            this.data.repeating.byDay = byDayArray.map(day => checkEnum(ICalWeekday, day) as ICalWeekday);\n        }\n\n        if (repeating.byMonth) {\n            const byMonthArray = Array.isArray(repeating.byMonth) ? repeating.byMonth : [repeating.byMonth];\n            this.data.repeating.byMonth = byMonthArray.map(month => {\n                if (typeof month !== 'number' || month < 1 || month > 12) {\n                    throw new Error('`repeating.byMonth` contains invalid value `' + month + '`!');\n                }\n\n                return month;\n            });\n        }\n\n        if (repeating.byMonthDay) {\n            const byMonthDayArray = Array.isArray(repeating.byMonthDay) ? repeating.byMonthDay : [repeating.byMonthDay];\n\n\n            this.data.repeating.byMonthDay = byMonthDayArray.map(monthDay => {\n                if (typeof monthDay !== 'number' || monthDay < -31 || monthDay > 31 || monthDay === 0) {\n                    throw new Error('`repeating.byMonthDay` contains invalid value `' + monthDay + '`!');\n                }\n\n                return monthDay;\n            });\n        }\n\n        if (repeating.bySetPos) {\n            if (!this.data.repeating.byDay) {\n                throw '`repeating.bySetPos` must be used along with `repeating.byDay`!';\n            }\n            const bySetPosArray = Array.isArray(repeating.bySetPos) ? repeating.bySetPos : [repeating.bySetPos];\n            this.data.repeating.bySetPos = bySetPosArray.map(bySetPos => {\n                if (typeof bySetPos !== 'number' || bySetPos < -366 || bySetPos > 366 || bySetPos === 0) {\n                    throw '`repeating.bySetPos` contains invalid value `' + bySetPos + '`!';\n                }\n                return bySetPos;\n            });\n        }\n\n        if (repeating.exclude) {\n            const excludeArray = Array.isArray(repeating.exclude) ? repeating.exclude : [repeating.exclude];\n            this.data.repeating.exclude = excludeArray.map((exclude, i) => {\n                return checkDate(exclude, `repeating.exclude[${i}]`);\n            });\n        }\n\n        if (repeating.startOfWeek) {\n            this.data.repeating.startOfWeek = checkEnum(ICalWeekday, repeating.startOfWeek) as ICalWeekday;\n        }\n\n        return this;\n    }\n\n    /**\n     * Get the event's summary\n     * @since 0.2.0\n     */\n    summary(): string;\n\n    /**\n     * Set the event's summary.\n     * Defaults to an empty string if nothing is set.\n     *\n     * @since 0.2.0\n     */\n    summary(summary: string): this;\n    summary(summary?: string): this | string {\n        if (summary === undefined) {\n            return this.data.summary;\n        }\n\n        this.data.summary = summary ? String(summary) : '';\n        return this;\n    }\n\n\n    /**\n     * Get the event's location\n     * @since 0.2.0\n     */\n    location(): ICalLocation | null;\n\n    /**\n     * Set the event's location by passing a string (minimum) or\n     * an {@link ICalLocationWithTitle} object which will also fill the iCal\n     * `GEO` attribute and Apple's `X-APPLE-STRUCTURED-LOCATION`.\n     *\n     * ```javascript\n     * event.location({\n     *    title: 'Apple Store Kurfürstendamm',\n     *    address: 'Kurfürstendamm 26, 10719 Berlin, Deutschland',\n     *    radius: 141.1751386318387,\n     *    geo: {\n     *        lat: 52.503630,\n     *        lon: 13.328650\n     *    }\n     * });\n     * ```\n     *\n     * ```text\n     * LOCATION:Apple Store Kurfürstendamm\\nKurfürstendamm 26\\, 10719 Berlin\\,\n     *  Deutschland\n     * X-APPLE-STRUCTURED-LOCATION;VALUE=URI;X-ADDRESS=Kurfürstendamm 26\\, 10719\n     *   Berlin\\, Deutschland;X-APPLE-RADIUS=141.1751386318387;X-TITLE=Apple Store\n     *   Kurfürstendamm:geo:52.50363,13.32865\n     * GEO:52.50363;13.32865\n     * ```\n     *\n     * Since v6.1.0 you can also pass a {@link ICalLocationWithoutTitle} object to pass\n     * the geolocation only. This will only fill the iCal `GEO` attribute.\n     *\n     * ```javascript\n     * event.location({\n     *    geo: {\n     *        lat: 52.503630,\n     *        lon: 13.328650\n     *    }\n     * });\n     * ```\n     *\n     * ```text\n     * GEO:52.50363;13.32865\n     * ```\n     *\n     * @since 0.2.0\n     */\n    location(location: ICalLocation | string | null): this;\n    location(location?: ICalLocation | string | null): this | ICalLocation | null {\n        if (location === undefined) {\n            return this.data.location;\n        }\n        if (typeof location === 'string') {\n            this.data.location = {\n                title: location\n            };\n            return this;\n        }\n        if (location && (\n            ('title' in location && !location.title) ||\n            (location?.geo && (!isFinite(location.geo.lat) || !isFinite(location.geo.lon))) ||\n            (!('title' in location) && !location?.geo)\n        )) {\n            throw new Error(\n                '`location` isn\\'t formatted correctly. See https://sebbo2002.github.io/ical-generator/'+\n                'develop/reference/classes/ICalEvent.html#location'\n            );\n        }\n\n        this.data.location = location || null;\n        return this;\n    }\n\n\n    /**\n     * Get the event's description as an {@link ICalDescription} object.\n     * @since 0.2.0\n     */\n    description(): ICalDescription | null;\n\n    /**\n     * Set the events description by passing a plaintext string or\n     * an object containing both a plaintext and a html description.\n     * Only a few calendar apps support html descriptions and like in\n     * emails, supported HTML tags and styling is limited.\n     *\n     * ```javascript\n     * event.description({\n     *     plain: 'Hello World!',\n     *     html: '<p>Hello World!</p>'\n     * });\n     * ```\n     *\n     * ```text\n     * DESCRIPTION:Hello World!\n     * X-ALT-DESC;FMTTYPE=text/html:<p>Hello World!</p>\n     * ```\n     *\n     * @since 0.2.0\n     */\n    description(description: ICalDescription | string | null): this;\n    description(description?: ICalDescription | string | null): this | ICalDescription | null {\n        if (description === undefined) {\n            return this.data.description;\n        }\n        if (description === null) {\n            this.data.description = null;\n            return this;\n        }\n\n        if (typeof description === 'string') {\n            this.data.description = {plain: description};\n        }\n        else {\n            this.data.description = description;\n        }\n        return this;\n    }\n\n\n    /**\n     * Get the event's organizer\n     * @since 0.2.0\n     */\n    organizer(): ICalOrganizer | null;\n\n    /**\n     * Set the event's organizer\n     *\n     * ```javascript\n     * event.organizer({\n     *    name: 'Organizer\\'s Name',\n     *    email: '<EMAIL>'\n     * });\n     *\n     * // OR\n     *\n     * event.organizer('Organizer\\'s Name <<EMAIL>>');\n     * ```\n     *\n     * You can also add an explicit `mailto` email address or or the sentBy address.\n     *\n     * ```javascript\n     *     event.organizer({\n     *    name: 'Organizer\\'s Name',\n     *    email: '<EMAIL>',\n     *    mailto: '<EMAIL>',\n     *    sentBy: '<EMAIL>'\n     * })\n     * ```\n     *\n     * @since 0.2.0\n     */\n    organizer(organizer: ICalOrganizer | string | null): this;\n    organizer(organizer?: ICalOrganizer | string | null): this | ICalOrganizer | null {\n        if (organizer === undefined) {\n            return this.data.organizer;\n        }\n        if (organizer === null) {\n            this.data.organizer = null;\n            return this;\n        }\n\n        this.data.organizer = checkNameAndMail('organizer', organizer);\n        return this;\n    }\n\n\n    /**\n     * Creates a new {@link ICalAttendee} and returns it. Use options to prefill\n     * the attendee's attributes. Calling this method without options will create\n     * an empty attendee.\n     *\n     * ```javascript\n     * import ical from 'ical-generator';\n     *\n     * const cal = ical();\n     * const event = cal.createEvent({\n     *   start: new Date()\n     * });\n     *\n     * event.createAttendee({email: '<EMAIL>', name: 'Hui'});\n     *\n     * // add another attendee\n     * event.createAttendee('Buh <<EMAIL>>');\n     * ```\n     *\n     * ```text\n     * BEGIN:VCALENDAR\n     * VERSION:2.0\n     * PRODID:-//sebbo.net//ical-generator//EN\n     * BEGIN:VEVENT\n     * UID:b4944f07-98e4-4581-ac80-2589bb20273d\n     * SEQUENCE:0\n     * DTSTAMP:20240212T194232Z\n     * DTSTART:20240212T194232Z\n     * SUMMARY:\n     * ATTENDEE;ROLE=REQ-PARTICIPANT;CN=\"Hui\":MAILTO:<EMAIL>\n     * ATTENDEE;ROLE=REQ-PARTICIPANT;CN=\"Buh\":MAILTO:<EMAIL>\n     * END:VEVENT\n     * END:VCALENDAR\n     * ```\n     *\n     * As with the organizer, you can also add an explicit `mailto` address.\n     *\n     * ```javascript\n     * event.createAttendee({email: '<EMAIL>', name: 'Hui', mailto: '<EMAIL>'});\n     *\n     * // overwrite an attendee's mailto address\n     * attendee.mailto('<EMAIL>');\n     * ```\n     *\n     * @since 0.2.0\n     */\n    createAttendee(data: ICalAttendee | ICalAttendeeData | string): ICalAttendee {\n        if (data instanceof ICalAttendee) {\n            this.data.attendees.push(data);\n            return data;\n        }\n        if (typeof data === 'string') {\n            data = { email: data, ...checkNameAndMail('data', data) };\n        }\n\n        const attendee = new ICalAttendee(data, this);\n        this.data.attendees.push(attendee);\n        return attendee;\n    }\n\n\n    /**\n     * Get all attendees\n     * @since 0.2.0\n     */\n    attendees(): ICalAttendee[];\n\n    /**\n     * Add multiple attendees to your event\n     *\n     * ```javascript\n     * const event = ical().createEvent();\n     *\n     * cal.attendees([\n     *     {email: '<EMAIL>', name: 'Person A'},\n     *     {email: '<EMAIL>', name: 'Person B'}\n     * ]);\n     *\n     * cal.attendees(); // --> [ICalAttendee, ICalAttendee]\n     * ```\n     *\n     * @since 0.2.0\n     */\n    attendees(attendees: (ICalAttendee | ICalAttendeeData | string)[]): this;\n    attendees(attendees?: (ICalAttendee | ICalAttendeeData | string)[]): this | ICalAttendee[] {\n        if (!attendees) {\n            return this.data.attendees;\n        }\n\n        attendees.forEach(attendee => this.createAttendee(attendee));\n        return this;\n    }\n\n\n    /**\n     * Creates a new {@link ICalAlarm} and returns it. Use options to prefill\n     * the alarm's attributes. Calling this method without options will create\n     * an empty alarm.\n     *\n     * ```javascript\n     * const cal = ical();\n     * const event = cal.createEvent();\n     * const alarm = event.createAlarm({type: ICalAlarmType.display, trigger: 300});\n     *\n     * // add another alarm\n     * event.createAlarm({\n     *     type: ICalAlarmType.audio,\n     *     trigger: 300, // 5min before event\n     * });\n     * ```\n     *\n     * @since 0.2.1\n     */\n    createAlarm(data: ICalAlarm | ICalAlarmData): ICalAlarm {\n        const alarm = data instanceof ICalAlarm ? data : new ICalAlarm(data, this);\n        this.data.alarms.push(alarm);\n        return alarm;\n    }\n\n\n    /**\n     * Get all alarms\n     * @since 0.2.0\n     */\n    alarms(): ICalAlarm[];\n\n    /**\n     * Add one or multiple alarms\n     *\n     * ```javascript\n     * const event = ical().createEvent();\n     *\n     * cal.alarms([\n     *     {type: ICalAlarmType.display, trigger: 600},\n     *     {type: ICalAlarmType.audio, trigger: 300}\n     * ]);\n     *\n     * cal.alarms(); // --> [ICalAlarm, ICalAlarm]\n     ```\n     *\n     * @since 0.2.0\n     */\n    alarms(alarms: ICalAlarm[] | ICalAlarmData[]): this;\n    alarms(alarms?: ICalAlarm[] | ICalAlarmData[]): this | ICalAlarm[] {\n        if (!alarms) {\n            return this.data.alarms;\n        }\n\n        alarms.forEach((alarm: ICalAlarm | ICalAlarmData) => this.createAlarm(alarm));\n        return this;\n    }\n\n\n    /**\n     * Creates a new {@link ICalCategory} and returns it. Use options to prefill the category's attributes.\n     * Calling this method without options will create an empty category.\n     *\n     * ```javascript\n     * const cal = ical();\n     * const event = cal.createEvent();\n     * const category = event.createCategory({name: 'APPOINTMENT'});\n     *\n     * // add another category\n     * event.createCategory({\n     *     name: 'MEETING'\n     * });\n     * ```\n     *\n     * @since 0.3.0\n     */\n    createCategory(data: ICalCategory | ICalCategoryData): ICalCategory {\n        const category = data instanceof ICalCategory ? data : new ICalCategory(data);\n        this.data.categories.push(category);\n        return category;\n    }\n\n\n    /**\n     * Get all categories\n     * @since 0.3.0\n     */\n    categories(): ICalCategory[];\n\n    /**\n     * Add categories to the event or return all selected categories.\n     *\n     * ```javascript\n     * const event = ical().createEvent();\n     *\n     * cal.categories([\n     *     {name: 'APPOINTMENT'},\n     *     {name: 'MEETING'}\n     * ]);\n     *\n     * cal.categories(); // --> [ICalCategory, ICalCategory]\n     * ```\n     *\n     * @since 0.3.0\n     */\n    categories(categories: (ICalCategory | ICalCategoryData)[]): this;\n    categories(categories?: (ICalCategory | ICalCategoryData)[]): this | ICalCategory[] {\n        if (!categories) {\n            return this.data.categories;\n        }\n\n        categories.forEach(category => this.createCategory(category));\n        return this;\n    }\n\n\n    /**\n     * Get the event's status\n     * @since 0.2.0\n     */\n    status(): ICalEventStatus | null;\n\n    /**\n     * Set the event's status\n     *\n     * ```javascript\n     * import ical, {ICalEventStatus} from 'ical-generator';\n     * event.status(ICalEventStatus.CONFIRMED);\n     * ```\n     *\n     * @since 0.2.0\n     */\n    status(status: ICalEventStatus | null): this;\n    status(status?: ICalEventStatus | null): this | ICalEventStatus | null {\n        if (status === undefined) {\n            return this.data.status;\n        }\n        if (status === null) {\n            this.data.status = null;\n            return this;\n        }\n\n        this.data.status = checkEnum(ICalEventStatus, status) as ICalEventStatus;\n        return this;\n    }\n\n\n    /**\n     * Get the event's busy status\n     * @since 1.0.2\n     */\n    busystatus(): ICalEventBusyStatus | null;\n\n    /**\n     * Set the event's busy status. Will add the\n     * [`X-MICROSOFT-CDO-BUSYSTATUS`](https://docs.microsoft.com/en-us/openspecs/exchange_server_protocols/ms-oxcical/cd68eae7-ed65-4dd3-8ea7-ad585c76c736)\n     * attribute to your event.\n     *\n     * ```javascript\n     * import ical, {ICalEventBusyStatus} from 'ical-generator';\n     * event.busystatus(ICalEventBusyStatus.BUSY);\n     * ```\n     *\n     * @since 1.0.2\n     */\n    busystatus(busystatus: ICalEventBusyStatus | null): this;\n    busystatus(busystatus?: ICalEventBusyStatus | null): this | ICalEventBusyStatus | null {\n        if (busystatus === undefined) {\n            return this.data.busystatus;\n        }\n        if (busystatus === null) {\n            this.data.busystatus = null;\n            return this;\n        }\n\n        this.data.busystatus = checkEnum(ICalEventBusyStatus, busystatus) as ICalEventBusyStatus;\n        return this;\n    }\n\n\n    /**\n     * Get the event's priority. A value of 1 represents\n     * the highest priority, 9 the lowest. 0 specifies an undefined\n     * priority.\n     *\n     * @since v2.0.0-develop.7\n     */\n    priority(): number | null;\n\n    /**\n     * Set the event's priority. A value of 1 represents\n     * the highest priority, 9 the lowest. 0 specifies an undefined\n     * priority.\n     *\n     * @since v2.0.0-develop.7\n     */\n    priority(priority: number | null): this;\n    priority(priority?: number | null): this | number | null {\n        if (priority === undefined) {\n            return this.data.priority;\n        }\n        if (priority === null) {\n            this.data.priority = null;\n            return this;\n        }\n\n        if(priority < 0 || priority > 9) {\n            throw new Error('`priority` is invalid, musst be 0 ≤ priority ≤ 9.');\n        }\n\n        this.data.priority = Math.round(priority);\n        return this;\n    }\n\n\n    /**\n     * Get the event's URL\n     * @since 0.2.0\n     */\n    url(): string | null;\n\n    /**\n     * Set the event's URL\n     * @since 0.2.0\n     */\n    url(url: string | null): this;\n    url(url?: string | null): this | string | null {\n        if (url === undefined) {\n            return this.data.url;\n        }\n\n        this.data.url = url ? String(url) : null;\n        return this;\n    }\n\n    /**\n     * Adds an attachment to the event by adding the file URL to the calendar.\n     *\n     * `ical-generator` only supports external attachments. File attachments that\n     * are directly included in the file are not supported, because otherwise the\n     * calendar file could easily become unfavourably large.\n     *\n     * ```javascript\n     * const cal = ical();\n     * const event = cal.createEvent();\n     * event.createAttachment('https://files.sebbo.net/calendar/attachments/foo');\n     * ```\n     *\n     * @since 3.2.0-develop.1\n     */\n    createAttachment(url: string): this {\n        this.data.attachments.push(url);\n        return this;\n    }\n\n\n    /**\n     * Get all attachment urls\n     * @since 3.2.0-develop.1\n     */\n    attachments(): string[];\n\n    /**\n     * Add one or multiple alarms\n     *\n     * ```javascript\n     * const event = ical().createEvent();\n     *\n     * cal.attachments([\n     *     'https://files.sebbo.net/calendar/attachments/foo',\n     *     'https://files.sebbo.net/calendar/attachments/bar'\n     * ]);\n     *\n     * cal.attachments(); // --> [string, string]\n     ```\n     *\n     * 3.2.0-develop.1\n     */\n    attachments(attachments: string[]): this;\n    attachments(attachments?: string[]): this | string[] {\n        if (!attachments) {\n            return this.data.attachments;\n        }\n\n        attachments.forEach((attachment: string) => this.createAttachment(attachment));\n        return this;\n    }\n\n    /**\n     * Get the event's transparency\n     * @since 1.7.3\n     */\n    transparency(): ICalEventTransparency | null;\n\n    /**\n     * Set the event's transparency\n     *\n     * Set the field to `OPAQUE` if the person or resource is no longer\n     * available due to this event. If the calendar entry has no influence\n     * on availability, you can set the field to `TRANSPARENT`. This value\n     * is mostly used to find out if a person has time on a certain date or\n     * not (see `TRANSP` in iCal specification).\n     *\n     * ```javascript\n     * import ical, {ICalEventTransparency} from 'ical-generator';\n     * event.transparency(ICalEventTransparency.OPAQUE);\n     * ```\n     *\n     * @since 1.7.3\n     */\n    transparency(transparency: ICalEventTransparency | null): this;\n    transparency(transparency?: ICalEventTransparency | null): this | ICalEventTransparency | null {\n        if (transparency === undefined) {\n            return this.data.transparency;\n        }\n        if (!transparency) {\n            this.data.transparency = null;\n            return this;\n        }\n\n        this.data.transparency = checkEnum(ICalEventTransparency, transparency) as ICalEventTransparency;\n        return this;\n    }\n\n\n    /**\n     * Get the event's creation date\n     * @since 0.3.0\n     */\n    created(): ICalDateTimeValue | null;\n\n    /**\n     * Set the event's creation date\n     * @since 0.3.0\n     */\n    created(created: ICalDateTimeValue | null): this;\n    created(created?: ICalDateTimeValue | null): this | ICalDateTimeValue | null {\n        if (created === undefined) {\n            return this.data.created;\n        }\n        if (created === null) {\n            this.data.created = null;\n            return this;\n        }\n\n        this.data.created = checkDate(created, 'created');\n        return this;\n    }\n\n\n    /**\n     * Get the event's last modification date\n     * @since 0.3.0\n     */\n    lastModified(): ICalDateTimeValue | null;\n\n    /**\n     * Set the event's last modification date\n     * @since 0.3.0\n     */\n    lastModified(lastModified: ICalDateTimeValue | null): this;\n    lastModified(lastModified?: ICalDateTimeValue | null): this | ICalDateTimeValue | null {\n        if (lastModified === undefined) {\n            return this.data.lastModified;\n        }\n        if (lastModified === null) {\n            this.data.lastModified = null;\n            return this;\n        }\n\n        this.data.lastModified = checkDate(lastModified, 'lastModified');\n        return this;\n    }\n\n    /**\n     * Get the event's class\n     * @since 2.0.0\n     */\n    class(): ICalEventClass | null;\n\n    /**\n     * Set the event's class\n     *\n     * ```javascript\n     * import ical, { ICalEventClass } from 'ical-generator';\n     * event.class(ICalEventClass.PRIVATE);\n     * ```\n     *\n     * @since 2.0.0\n     */\n    class(class_: ICalEventClass | null): this;\n    class(class_?: ICalEventClass | null): this | ICalEventClass | null {\n        if (class_ === undefined) {\n            return this.data.class;\n        }\n        if (class_ === null) {\n            this.data.class = null;\n            return this;\n        }\n\n        this.data.class = checkEnum(ICalEventClass, class_) as ICalEventClass;\n        return this;\n    }\n\n\n    /**\n     * Set X-* attributes. Woun't filter double attributes,\n     * which are also added by another method (e.g. summary),\n     * so these attributes may be inserted twice.\n     *\n     * ```javascript\n     * event.x([\n     *     {\n     *         key: \"X-MY-CUSTOM-ATTR\",\n     *         value: \"1337!\"\n     *     }\n     * ]);\n     *\n     * event.x([\n     *     [\"X-MY-CUSTOM-ATTR\", \"1337!\"]\n     * ]);\n     *\n     * event.x({\n     *     \"X-MY-CUSTOM-ATTR\": \"1337!\"\n     * });\n     * ```\n     *\n     * @since 1.9.0\n     */\n    x (keyOrArray: {key: string, value: string}[] | [string, string][] | Record<string, string>): this;\n\n    /**\n     * Set a X-* attribute. Woun't filter double attributes,\n     * which are also added by another method (e.g. summary),\n     * so these attributes may be inserted twice.\n     *\n     * ```javascript\n     * event.x(\"X-MY-CUSTOM-ATTR\", \"1337!\");\n     * ```\n     *\n     * @since 1.9.0\n     */\n    x (keyOrArray: string, value: string): this;\n\n    /**\n     * Get all custom X-* attributes.\n     * @since 1.9.0\n     */\n    x (): {key: string, value: string}[];\n    x(keyOrArray?: ({ key: string, value: string })[] | [string, string][] | Record<string, string> | string, value?: string): this | void | ({ key: string, value: string })[] {\n        if (keyOrArray === undefined) {\n            return addOrGetCustomAttributes(this.data);\n        }\n\n        if (typeof keyOrArray === 'string' && typeof value === 'string') {\n            addOrGetCustomAttributes(this.data, keyOrArray, value);\n        }\n        if (typeof keyOrArray === 'object') {\n            addOrGetCustomAttributes(this.data, keyOrArray);\n        }\n\n        return this;\n    }\n\n\n    /**\n     * Return a shallow copy of the events's options for JSON stringification.\n     * Third party objects like moment.js values or RRule objects are stringified\n     * as well. Can be used for persistence.\n     *\n     * ```javascript\n     * const event = ical().createEvent();\n     * const json = JSON.stringify(event);\n     *\n     * // later: restore event data\n     * const calendar = ical().createEvent(JSON.parse(json));\n     * ```\n     *\n     * @since 0.2.4\n     */\n    toJSON(): ICalEventJSONData {\n        let repeating: ICalEventJSONRepeatingData | string | null = null;\n        if(isRRule(this.data.repeating) || typeof this.data.repeating === 'string') {\n            repeating = this.data.repeating.toString();\n        }\n        else if(this.data.repeating) {\n            repeating = Object.assign({}, this.data.repeating, {\n                until: toJSON(this.data.repeating.until) || undefined,\n                exclude: this.data.repeating.exclude?.map(d => toJSON(d)),\n            });\n        }\n\n        this.swapStartAndEndIfRequired();\n        return Object.assign({}, this.data, {\n            start: toJSON(this.data.start) || null,\n            end: toJSON(this.data.end) || null,\n            recurrenceId: toJSON(this.data.recurrenceId) || null,\n            stamp: toJSON(this.data.stamp) || null,\n            created: toJSON(this.data.created) || null,\n            lastModified: toJSON(this.data.lastModified) || null,\n            repeating,\n            x: this.x()\n        });\n    }\n\n\n    /**\n     * Return generated event as a string.\n     *\n     * ```javascript\n     * const event = ical().createEvent();\n     * console.log(event.toString()); // → BEGIN:VEVENT…\n     * ```\n     */\n    toString(): string {\n        let g = '';\n\n        // DATE & TIME\n        g += 'BEGIN:VEVENT\\r\\n';\n        g += 'UID:' + this.data.id + '\\r\\n';\n\n        // SEQUENCE\n        g += 'SEQUENCE:' + this.data.sequence + '\\r\\n';\n\n        this.swapStartAndEndIfRequired();\n        g += 'DTSTAMP:' + formatDate(this.calendar.timezone(), this.data.stamp) + '\\r\\n';\n        if (this.data.allDay) {\n            g += 'DTSTART;VALUE=DATE:' + formatDate(this.timezone(), this.data.start, true) + '\\r\\n';\n            if (this.data.end) {\n                g += 'DTEND;VALUE=DATE:' + formatDate(this.timezone(), this.data.end, true) + '\\r\\n';\n            }\n\n            g += 'X-MICROSOFT-CDO-ALLDAYEVENT:TRUE\\r\\n';\n            g += 'X-MICROSOFT-MSNCALENDAR-ALLDAYEVENT:TRUE\\r\\n';\n        }\n        else {\n            g += formatDateTZ(this.timezone(), 'DTSTART', this.data.start, this.data) + '\\r\\n';\n            if (this.data.end) {\n                g += formatDateTZ(this.timezone(), 'DTEND', this.data.end, this.data) + '\\r\\n';\n            }\n        }\n\n        // REPEATING\n        if(isRRule(this.data.repeating) || typeof this.data.repeating === 'string') {\n            let repeating = this.data.repeating\n                .toString()\n                .replace(/\\r\\n/g, '\\n')\n                .split('\\n')\n                .filter(l => l && !l.startsWith('DTSTART:'))\n                .join('\\r\\n');\n\n            if(!repeating.includes('\\r\\n') && !repeating.startsWith('RRULE:')) {\n                repeating = 'RRULE:' + repeating;\n            }\n\n            g += repeating.trim() + '\\r\\n';\n        }\n        else if (this.data.repeating) {\n            g += 'RRULE:FREQ=' + this.data.repeating.freq;\n\n            if (this.data.repeating.count) {\n                g += ';COUNT=' + this.data.repeating.count;\n            }\n\n            if (this.data.repeating.interval) {\n                g += ';INTERVAL=' + this.data.repeating.interval;\n            }\n\n            if (this.data.repeating.until) {\n                g += ';UNTIL=' + formatDate(this.calendar.timezone(), this.data.repeating.until, false, this.floating());\n            }\n\n            if (this.data.repeating.byDay) {\n                g += ';BYDAY=' + this.data.repeating.byDay.join(',');\n            }\n\n            if (this.data.repeating.byMonth) {\n                g += ';BYMONTH=' + this.data.repeating.byMonth.join(',');\n            }\n\n            if (this.data.repeating.byMonthDay) {\n                g += ';BYMONTHDAY=' + this.data.repeating.byMonthDay.join(',');\n            }\n\n            if (this.data.repeating.bySetPos) {\n                g += ';BYSETPOS=' + this.data.repeating.bySetPos.join(',');\n            }\n\n            if (this.data.repeating.startOfWeek) {\n                g += ';WKST=' + this.data.repeating.startOfWeek;\n            }\n\n            g += '\\r\\n';\n\n            // REPEATING EXCLUSION\n            if (this.data.repeating.exclude) {\n                if (this.data.allDay) {\n                    g += 'EXDATE;VALUE=DATE:' + this.data.repeating.exclude.map(excludedDate => {\n                        return formatDate(this.calendar.timezone(), excludedDate, true);\n                    }).join(',') + '\\r\\n';\n                }\n                else {\n                    g += 'EXDATE';\n                    if (this.timezone()) {\n                        g += ';TZID=' + this.timezone() + ':' + this.data.repeating.exclude.map(excludedDate => {\n                            // This isn't a 'floating' event because it has a timezone;\n                            // but we use it to omit the 'Z' UTC specifier in formatDate()\n                            return formatDate(this.timezone(), excludedDate, false, true);\n                        }).join(',') + '\\r\\n';\n                    }\n                    else {\n                        g += ':' + this.data.repeating.exclude.map(excludedDate => {\n                            return formatDate(this.timezone(), excludedDate, false, this.floating());\n                        }).join(',') + '\\r\\n';\n                    }\n                }\n            }\n        }\n\n        // RECURRENCE\n        if (this.data.recurrenceId) {\n            g += formatDateTZ(this.timezone(), 'RECURRENCE-ID', this.data.recurrenceId, this.data) + '\\r\\n';\n        }\n\n        // SUMMARY\n        g += 'SUMMARY:' + escape(this.data.summary, false) + '\\r\\n';\n\n        // TRANSPARENCY\n        if (this.data.transparency) {\n            g += 'TRANSP:' + escape(this.data.transparency, false) + '\\r\\n';\n        }\n\n        // LOCATION\n        if (this.data.location && 'title' in this.data.location && this.data.location.title) {\n            g += 'LOCATION:' + escape(\n                this.data.location.title +\n                (this.data.location.address ? '\\n' + this.data.location.address : ''),\n                false\n            ) + '\\r\\n';\n\n            if (this.data.location.radius && this.data.location.geo) {\n                g += 'X-APPLE-STRUCTURED-LOCATION;VALUE=URI;' +\n                    (this.data.location.address ? 'X-ADDRESS=' + escape(this.data.location.address, false) + ';' : '') +\n                    'X-APPLE-RADIUS=' + escape(this.data.location.radius, false) + ';' +\n                    'X-TITLE=' + escape(this.data.location.title, false) +\n                    ':geo:' + escape(this.data.location.geo?.lat, false) + ',' +\n                    escape(this.data.location.geo?.lon, false) + '\\r\\n';\n            }\n        }\n\n        // GEO\n        if (this.data.location && 'geo' in this.data.location && this.data.location.geo) {\n            g += 'GEO:' + escape(this.data.location.geo?.lat, false) + ';' +\n                escape(this.data.location.geo?.lon, false) + '\\r\\n';\n        }\n        \n        // DESCRIPTION\n        if (this.data.description) {\n            g += 'DESCRIPTION:' + escape(this.data.description.plain, false) + '\\r\\n';\n\n            // HTML DESCRIPTION\n            if (this.data.description.html) {\n                g += 'X-ALT-DESC;FMTTYPE=text/html:' + escape(this.data.description.html, false) + '\\r\\n';\n            }\n        }\n\n        // ORGANIZER\n        if (this.data.organizer) {\n            g += 'ORGANIZER;CN=\"' + escape(this.data.organizer.name, true) + '\"';\n\n            if (this.data.organizer.sentBy) {\n                g += ';SENT-BY=\"mailto:' + escape(this.data.organizer.sentBy, true) + '\"';\n            }\n            if (this.data.organizer.email && this.data.organizer.mailto) {\n                g += ';EMAIL=' + escape(this.data.organizer.email, false);\n            }\n            if(this.data.organizer.email) {\n                g += ':mailto:' + escape(this.data.organizer.mailto || this.data.organizer.email, false);\n            }\n            g += '\\r\\n';\n        }\n\n        // ATTENDEES\n        this.data.attendees.forEach(function (attendee) {\n            g += attendee.toString();\n        });\n\n        // ALARMS\n        this.data.alarms.forEach(function (alarm) {\n            g += alarm.toString();\n        });\n\n        // CATEGORIES\n        if (this.data.categories.length > 0) {\n            g += 'CATEGORIES:' + this.data.categories\n                .map(category => category.toString())\n                .join() + '\\r\\n';\n        }\n\n        // URL\n        if (this.data.url) {\n            g += 'URL;VALUE=URI:' + escape(this.data.url, false) + '\\r\\n';\n        }\n\n        // ATTACHMENT\n        if (this.data.attachments.length > 0) {\n            this.data.attachments.forEach(url => {\n                g += 'ATTACH:' + escape(url, false) + '\\r\\n';\n            });\n        }\n\n        // STATUS\n        if (this.data.status) {\n            g += 'STATUS:' + this.data.status.toUpperCase() + '\\r\\n';\n        }\n\n        // BUSYSTATUS\n        if (this.data.busystatus) {\n            g += 'X-MICROSOFT-CDO-BUSYSTATUS:' + this.data.busystatus.toUpperCase() + '\\r\\n';\n        }\n\n        // PRIORITY\n        if (this.data.priority !== null) {\n            g += 'PRIORITY:' + this.data.priority + '\\r\\n';\n        }\n\n        // CUSTOM X ATTRIBUTES\n        g += generateCustomAttributes(this.data);\n\n        // CREATED\n        if (this.data.created) {\n            g += 'CREATED:' + formatDate(this.calendar.timezone(), this.data.created) + '\\r\\n';\n        }\n\n        // LAST-MODIFIED\n        if (this.data.lastModified) {\n            g += 'LAST-MODIFIED:' + formatDate(this.calendar.timezone(), this.data.lastModified) + '\\r\\n';\n        }\n\n        if (this.data.class) {\n            g+= 'CLASS:' + this.data.class.toUpperCase() + '\\r\\n';\n        }\n\n        g += 'END:VEVENT\\r\\n';\n        return g;\n    }\n}\n", "'use strict';\n\n\nimport {addOrGetCustomAttributes, checkEnum, checkNameAndMail, escape} from './tools.ts';\nimport ICalEvent from './event.ts';\nimport ICalAlarm from './alarm.ts';\n\n\ninterface ICalInternalAttendeeData {\n    name: string | null;\n    email: string;\n    mailto: string | null;\n    sentBy: string | null;\n    status: ICalAttendeeStatus | null;\n    role: ICalAttendeeRole;\n    rsvp: boolean | null;\n    type: ICalAttendeeType | null;\n    delegatedTo: ICalAttendee | null;\n    delegatedFrom: ICalAttendee | null;\n    x: [string, string][];\n}\n\nexport interface ICalAttendeeData {\n    name?: string | null;\n    email: string;\n    mailto?: string | null;\n    sentBy?: string | null;\n    status?: ICalAttendeeStatus | null;\n    role?: ICalAttendeeRole;\n    rsvp?: boolean | null;\n    type?: ICalAttendeeType | null;\n    delegatedTo?: ICalAttendee | ICalAttendeeData | string | null;\n    delegatedFrom?: ICalAttendee | ICalAttendeeData | string | null;\n    delegatesTo?: ICalAttendee | ICalAttendeeData | string | null;\n    delegatesFrom?: ICalAttendee | ICalAttendeeData | string | null;\n    x?: {key: string, value: string}[] | [string, string][] | Record<string, string>;\n}\n\nexport interface ICalAttendeeJSONData {\n    name: string | null;\n    email: string;\n    mailto: string | null;\n    sentBy: string | null;\n    status: ICalAttendeeStatus | null;\n    role: ICalAttendeeRole;\n    rsvp: boolean | null;\n    type: ICalAttendeeType | null;\n    delegatedTo: string | null;\n    delegatedFrom: string | null;\n    x: {key: string, value: string}[];\n}\n\nexport enum ICalAttendeeRole {\n    CHAIR = 'CHAIR',\n    REQ = 'REQ-PARTICIPANT',\n    OPT = 'OPT-PARTICIPANT',\n    NON = 'NON-PARTICIPANT'\n}\n\nexport enum ICalAttendeeStatus {\n    ACCEPTED = 'ACCEPTED',\n    TENTATIVE = 'TENTATIVE',\n    DECLINED = 'DECLINED',\n    DELEGATED = 'DELEGATED',\n    NEEDSACTION = 'NEEDS-ACTION'\n}\n\n// ref: https://tools.ietf.org/html/rfc2445#section-4.2.3\nexport enum ICalAttendeeType {\n    INDIVIDUAL = 'INDIVIDUAL',\n    GROUP = 'GROUP',\n    RESOURCE = 'RESOURCE',\n    ROOM = 'ROOM',\n    UNKNOWN = 'UNKNOWN'\n}\n\n\n/**\n * Usually you get an {@link ICalAttendee} object like this:\n *\n * ```javascript\n * import ical from 'ical-generator';\n * const calendar = ical();\n * const event = calendar.createEvent();\n * const attendee = event.createAttendee({ email: '<EMAIL>' });\n * ```\n *\n * You can also use the {@link ICalAttendee} object directly:\n *\n * ```javascript\n * import ical, {ICalAttendee} from 'ical-generator';\n * const attendee = new ICalAttendee({ email: '<EMAIL>' });\n * event.attendees([attendee]);\n * ```\n */\nexport default class ICalAttendee {\n    private readonly data: ICalInternalAttendeeData;\n    private readonly parent: ICalEvent | ICalAlarm;\n\n    /**\n     * Constructor of {@link ICalAttendee}. The event reference is\n     * required to query the calendar's timezone when required.\n     *\n     * @param data Attendee Data\n     * @param parent Reference to ICalEvent object\n     */\n    constructor(data: ICalAttendeeData, parent: ICalEvent | ICalAlarm) {\n        this.data = {\n            name: null,\n            email: '',\n            mailto: null,\n            sentBy: null,\n            status: null,\n            role: ICalAttendeeRole.REQ,\n            rsvp: null,\n            type: null,\n            delegatedTo: null,\n            delegatedFrom: null,\n            x: []\n        };\n        this.parent = parent;\n        if (!this.parent) {\n            throw new Error('`event` option required!');\n        }\n        if (!data.email) {\n            throw new Error('No value for `email` in ICalAttendee given!');\n        }\n\n        if (data.name !== undefined) this.name(data.name);\n        if (data.email !== undefined) this.email(data.email);\n        if (data.mailto !== undefined) this.mailto(data.mailto);\n        if (data.sentBy !== undefined) this.sentBy(data.sentBy);\n        if (data.status !== undefined) this.status(data.status);\n        if (data.role !== undefined) this.role(data.role);\n        if (data.rsvp !== undefined) this.rsvp(data.rsvp);\n        if (data.type !== undefined) this.type(data.type);\n        if (data.delegatedTo !== undefined) this.delegatedTo(data.delegatedTo);\n        if (data.delegatedFrom !== undefined) this.delegatedFrom(data.delegatedFrom);\n        if (data.delegatesTo) this.delegatesTo(data.delegatesTo);\n        if (data.delegatesFrom) this.delegatesFrom(data.delegatesFrom);\n        if (data.x !== undefined) this.x(data.x);\n    }\n\n\n    /**\n     * Get the attendee's name\n     * @since 0.2.0\n     */\n    name(): string | null;\n\n    /**\n     * Set the attendee's name\n     * @since 0.2.0\n     */\n    name(name: string | null): this;\n    name(name?: string | null): this | string | null {\n        if (name === undefined) {\n            return this.data.name;\n        }\n\n        this.data.name = name || null;\n        return this;\n    }\n\n\n    /**\n     * Get the attendee's email address\n     * @since 0.2.0\n     */\n    email(): string;\n\n    /**\n     * Set the attendee's email address\n     * @since 0.2.0\n     */\n    email(email: string): this;\n    email(email?: string): this | string {\n        if (!email) {\n            return this.data.email;\n        }\n\n        this.data.email = email;\n        return this;\n    }\n\n    /**\n     * Get the attendee's email address\n     * @since 1.3.0\n     */\n    mailto(): string | null;\n\n    /**\n     * Set the attendee's email address\n     * @since 1.3.0\n     */\n    mailto(mailto: string | null): this;\n    mailto(mailto?: string | null): this | string | null {\n        if (mailto === undefined) {\n            return this.data.mailto;\n        }\n\n        this.data.mailto = mailto || null;\n        return this;\n    }\n\n\n    /**\n     * Get the acting user's email adress\n     * @since 3.3.0\n     */\n    sentBy(): string | null;\n\n    /**\n     * Set the acting user's email adress\n     * @since 3.3.0\n     */\n    sentBy(email: string | null): this;\n    sentBy(email?: string | null): this | string | null {\n        if (!email) {\n            return this.data.sentBy;\n        }\n\n        this.data.sentBy = email;\n        return this;\n    }\n\n\n    /**\n     * Get attendee's role\n     * @since 0.2.0\n     */\n    role(): ICalAttendeeRole;\n\n    /**\n     * Set the attendee's role, defaults to `REQ` / `REQ-PARTICIPANT`.\n     * Checkout {@link ICalAttendeeRole} for available roles.\n     *\n     * @since 0.2.0\n     */\n    role(role: ICalAttendeeRole): this;\n    role(role?: ICalAttendeeRole): this | ICalAttendeeRole {\n        if (role === undefined) {\n            return this.data.role;\n        }\n\n        this.data.role = checkEnum(ICalAttendeeRole, role) as ICalAttendeeRole;\n        return this;\n    }\n\n\n    /**\n     * Get attendee's RSVP expectation\n     * @since 0.2.1\n     */\n    rsvp(): boolean | null;\n\n    /**\n     * Set the attendee's RSVP expectation\n     * @since 0.2.1\n     */\n    rsvp(rsvp: boolean | null): this;\n    rsvp(rsvp?: boolean | null): this | boolean | null {\n        if (rsvp === undefined) {\n            return this.data.rsvp;\n        }\n        if (rsvp === null) {\n            this.data.rsvp = null;\n            return this;\n        }\n\n        this.data.rsvp = Boolean(rsvp);\n        return this;\n    }\n\n\n    /**\n     * Get attendee's status\n     * @since 0.2.0\n     */\n    status(): ICalAttendeeStatus | null;\n\n    /**\n     * Set the attendee's status. See {@link ICalAttendeeStatus}\n     * for available status options.\n     *\n     * @since 0.2.0\n     */\n    status(status: ICalAttendeeStatus | null): this;\n    status(status?: ICalAttendeeStatus | null): this | ICalAttendeeStatus | null {\n        if (status === undefined) {\n            return this.data.status;\n        }\n        if (!status) {\n            this.data.status = null;\n            return this;\n        }\n\n        this.data.status = checkEnum(ICalAttendeeStatus, status) as ICalAttendeeStatus;\n        return this;\n    }\n\n\n    /**\n     * Get attendee's type (a.k.a. CUTYPE)\n     * @since 0.2.3\n     */\n    type(): ICalAttendeeType;\n\n    /**\n     * Set attendee's type (a.k.a. CUTYPE).\n     * See {@link ICalAttendeeType} for available status options.\n     *\n     * @since 0.2.3\n     */\n    type(type: ICalAttendeeType | null): this;\n    type(type?: ICalAttendeeType | null): this | ICalAttendeeType | null {\n        if (type === undefined) {\n            return this.data.type;\n        }\n        if (!type) {\n            this.data.type = null;\n            return this;\n        }\n\n        this.data.type = checkEnum(ICalAttendeeType, type) as ICalAttendeeType;\n        return this;\n    }\n\n\n    /**\n     * Get the attendee's delegated-to value.\n     * @since 0.2.0\n     */\n    delegatedTo(): ICalAttendee | null;\n\n    /**\n     * Set the attendee's delegated-to field.\n     *\n     * Creates a new Attendee if the passed object is not already a\n     * {@link ICalAttendee} object. Will set the `delegatedTo` and\n     * `delegatedFrom` attributes.\n     *\n     * Will also set the `status` to `DELEGATED`, if attribute is set.\n     *\n     * ```javascript\n     * const cal = ical();\n     * const event = cal.createEvent();\n     * const attendee = cal.createAttendee();\n     *\n     * attendee.delegatesTo({email: '<EMAIL>', name: 'Foo'});\n     ```\n     *\n     * @since 0.2.0\n     */\n    delegatedTo(delegatedTo: ICalAttendee | ICalAttendeeData | string | null): this;\n    delegatedTo(delegatedTo?: ICalAttendee | ICalAttendeeData | string | null): this | ICalAttendee | null {\n        if (delegatedTo === undefined) {\n            return this.data.delegatedTo;\n        }\n        if (!delegatedTo) {\n            this.data.delegatedTo = null;\n            if (this.data.status === ICalAttendeeStatus.DELEGATED) {\n                this.data.status = null;\n            }\n            return this;\n        }\n\n        if(typeof delegatedTo === 'string') {\n            this.data.delegatedTo = new ICalAttendee(\n                { email: delegatedTo, ...checkNameAndMail('delegatedTo', delegatedTo) },\n                this.parent,\n            );\n        }\n        else if(delegatedTo instanceof ICalAttendee) {\n            this.data.delegatedTo = delegatedTo;\n        }\n        else {\n            this.data.delegatedTo = new ICalAttendee(delegatedTo, this.parent);\n        }\n\n        this.data.status = ICalAttendeeStatus.DELEGATED;\n        return this;\n    }\n\n\n    /**\n     * Get the attendee's delegated-from field\n     * @since 0.2.0\n     */\n    delegatedFrom (): ICalAttendee | null;\n\n    /**\n     * Set the attendee's delegated-from field\n     *\n     * Creates a new Attendee if the passed object is not already a\n     * {@link ICalAttendee} object. Will set the `delegatedTo` and\n     * `delegatedFrom` attributes.\n     *\n     * @param delegatedFrom\n     */\n    delegatedFrom (delegatedFrom: ICalAttendee | ICalAttendeeData | string | null): this;\n    delegatedFrom(delegatedFrom?: ICalAttendee | ICalAttendeeData | string | null): this | ICalAttendee | null {\n        if (delegatedFrom === undefined) {\n            return this.data.delegatedFrom;\n        }\n\n        if (!delegatedFrom) {\n            this.data.delegatedFrom = null;\n        }\n        else if(typeof delegatedFrom === 'string') {\n            this.data.delegatedFrom = new ICalAttendee(\n                { email: delegatedFrom, ...checkNameAndMail('delegatedFrom', delegatedFrom) },\n                this.parent,\n            );\n        }\n        else if(delegatedFrom instanceof ICalAttendee) {\n            this.data.delegatedFrom = delegatedFrom;\n        }\n        else {\n            this.data.delegatedFrom = new ICalAttendee(delegatedFrom, this.parent);\n        }\n\n        return this;\n    }\n\n\n    /**\n     * Create a new attendee this attendee delegates to and returns\n     * this new attendee. Creates a new attendee if the passed object\n     * is not already an {@link ICalAttendee}.\n     *\n     * ```javascript\n     * const cal = ical();\n     * const event = cal.createEvent();\n     * const attendee = cal.createAttendee();\n     *\n     * attendee.delegatesTo({email: '<EMAIL>', name: 'Foo'});\n     * ```\n     *\n     * @since 0.2.0\n     */\n    delegatesTo (options: ICalAttendee | ICalAttendeeData | string): ICalAttendee {\n        const a = options instanceof ICalAttendee ? options : this.parent.createAttendee(options);\n        this.delegatedTo(a);\n        a.delegatedFrom(this);\n        return a;\n    }\n\n\n    /**\n     * Create a new attendee this attendee delegates from and returns\n     * this new attendee. Creates a new attendee if the passed object\n     * is not already an {@link ICalAttendee}.\n     *\n     * ```javascript\n     * const cal = ical();\n     * const event = cal.createEvent();\n     * const attendee = cal.createAttendee();\n     *\n     * attendee.delegatesFrom({email: '<EMAIL>', name: 'Foo'});\n     * ```\n     *\n     * @since 0.2.0\n     */\n    delegatesFrom (options: ICalAttendee | ICalAttendeeData | string): ICalAttendee {\n        const a = options instanceof ICalAttendee ? options : this.parent.createAttendee(options);\n        this.delegatedFrom(a);\n        a.delegatedTo(this);\n        return a;\n    }\n\n    /**\n     * Set X-* attributes. Woun't filter double attributes,\n     * which are also added by another method (e.g. status),\n     * so these attributes may be inserted twice.\n     *\n     * ```javascript\n     * attendee.x([\n     *     {\n     *         key: \"X-MY-CUSTOM-ATTR\",\n     *         value: \"1337!\"\n     *     }\n     * ]);\n     *\n     * attendee.x([\n     *     [\"X-MY-CUSTOM-ATTR\", \"1337!\"]\n     * ]);\n     *\n     * attendee.x({\n     *     \"X-MY-CUSTOM-ATTR\": \"1337!\"\n     * });\n     * ```\n     *\n     * @since 1.9.0\n     */\n    x (keyOrArray: {key: string, value: string}[] | [string, string][] | Record<string, string>): this;\n\n    /**\n     * Set a X-* attribute. Woun't filter double attributes,\n     * which are also added by another method (e.g. status),\n     * so these attributes may be inserted twice.\n     *\n     * ```javascript\n     * attendee.x(\"X-MY-CUSTOM-ATTR\", \"1337!\");\n     * ```\n     *\n     * @since 1.9.0\n     */\n    x (keyOrArray: string, value: string): this;\n\n    /**\n     * Get all custom X-* attributes.\n     * @since 1.9.0\n     */\n    x (): {key: string, value: string}[];\n    x (keyOrArray?: ({key: string, value: string})[] | [string, string][] | Record<string, string> | string, value?: string): this | void | ({key: string, value: string})[] {\n        if(keyOrArray === undefined) {\n            return addOrGetCustomAttributes (this.data);\n        }\n\n        if(typeof keyOrArray === 'string' && typeof value === 'string') {\n            addOrGetCustomAttributes (this.data, keyOrArray, value);\n        }\n        else if(typeof keyOrArray === 'object') {\n            addOrGetCustomAttributes (this.data, keyOrArray);\n        }\n        else {\n            throw new Error('Either key or value is not a string!');\n        }\n\n        return this;\n    }\n\n\n    /**\n     * Return a shallow copy of the attendee's options for JSON stringification.\n     * Can be used for persistence.\n     *\n     * @since 0.2.4\n     */\n    toJSON(): ICalAttendeeJSONData {\n        return Object.assign({}, this.data, {\n            delegatedTo: this.data.delegatedTo?.email() || null,\n            delegatedFrom: this.data.delegatedFrom?.email() || null,\n            x: this.x()\n        });\n    }\n\n\n    /**\n     * Return generated attendee as a string.\n     *\n     * ```javascript\n     * console.log(attendee.toString()); // → ATTENDEE;ROLE=…\n     * ```\n     */\n    toString (): string {\n        let g = 'ATTENDEE';\n\n        if (!this.data.email) {\n            throw new Error('No value for `email` in ICalAttendee given!');\n        }\n\n        // ROLE\n        g += ';ROLE=' + this.data.role;\n\n        // TYPE\n        if (this.data.type) {\n            g += ';CUTYPE=' + this.data.type;\n        }\n\n        // PARTSTAT\n        if (this.data.status) {\n            g += ';PARTSTAT=' + this.data.status;\n        }\n\n        // RSVP\n        if (this.data.rsvp !== null) {\n            g += ';RSVP=' + this.data.rsvp.toString().toUpperCase();\n        }\n\n        // SENT-BY\n        if (this.data.sentBy !== null) {\n            g += ';SENT-BY=\"mailto:' + this.data.sentBy + '\"';\n        }\n\n        // DELEGATED-TO\n        if (this.data.delegatedTo) {\n            g += ';DELEGATED-TO=\"' + this.data.delegatedTo.email() + '\"';\n        }\n\n        // DELEGATED-FROM\n        if (this.data.delegatedFrom) {\n            g += ';DELEGATED-FROM=\"' + this.data.delegatedFrom.email() + '\"';\n        }\n\n        // CN / Name\n        if (this.data.name) {\n            g += ';CN=\"' + escape(this.data.name, true) + '\"';\n        }\n\n        // EMAIL\n        if (this.data.email && this.data.mailto) {\n            g += ';EMAIL=' + escape(this.data.email, false);\n        }\n\n        // CUSTOM X ATTRIBUTES\n        if(this.data.x.length) {\n            g += ';' + this.data.x\n                .map(([key, value]) => key.toUpperCase() + '=' + escape(value, false))\n                .join(';');\n        }\n\n        g += ':MAILTO:' + escape(this.data.mailto || this.data.email, false) + '\\r\\n';\n\n        return g;\n    }\n}\n", "'use strict';\n\nimport ICalEvent from './event.ts';\nimport {\n    addOrGetCustomAttributes,\n    formatDate,\n    escape,\n    generateCustomAttributes,\n    checkDate,\n    toDurationString,\n    toJSON,\n    checkNameAndMail\n} from './tools.ts';\nimport {ICalDateTimeValue} from './types.ts';\nimport ICalAttendee, { ICalAttendeeData } from './attendee.ts';\n\n\nexport enum ICalAlarmType {\n    display = 'display',\n    audio = 'audio',\n    email = 'email'\n}\n\nexport const ICalAlarmRelatesTo = {\n    end: 'END',\n    start: 'START'\n} as const;\n\nexport type ICalAlarmRelatesTo = typeof ICalAlarmRelatesTo[keyof typeof ICalAlarmRelatesTo];\n\nexport type ICalAlarmTypeValue = keyof ICalAlarmType;\n\nexport interface ICalAttachment {\n    uri: string;\n    mime: string | null;\n}\n\nexport type ICalAlarmData = ICalAlarmBaseData |\n    ICalAlarmTriggerData |\n    ICalAlarmTriggerAfterData |\n    ICalAlarmTriggerBeforeData;\n\nexport type ICalAlarmTriggerData = ICalAlarmBaseData & { trigger: number | ICalDateTimeValue };\nexport type ICalAlarmTriggerAfterData = ICalAlarmBaseData & { triggerAfter: number | ICalDateTimeValue };\nexport type ICalAlarmTriggerBeforeData = ICalAlarmBaseData & { triggerBefore: number | ICalDateTimeValue };\n\nexport interface ICalAlarmBaseData {\n    type?: ICalAlarmType;\n    relatesTo?: ICalAlarmRelatesTo | null;\n    repeat?: ICalAlarmRepeatData | null;\n    attach?: string | ICalAttachment | null;\n    description?: string | null;\n    summary?: string | null;\n    attendees?: ICalAttendee[] | ICalAttendeeData[];\n    x?: {key: string, value: string}[] | [string, string][] | Record<string, string>;\n}\n\nexport interface ICalAlarmRepeatData {\n    times: number;\n    interval: number;\n}\n\ninterface ICalInternalAlarmData {\n    type: ICalAlarmType;\n    trigger: ICalDateTimeValue | number;\n    relatesTo: ICalAlarmRelatesTo | null;\n    repeat: ICalAlarmRepeatData | null;\n    interval: number | null;\n    attach: ICalAttachment | null;\n    description: string | null;\n    summary: string | null;\n    attendees: ICalAttendee[];\n    x: [string, string][];\n}\n\nexport interface ICalAlarmJSONData {\n    type: ICalAlarmType;\n    trigger: string | number;\n    relatesTo: ICalAlarmRelatesTo | null;\n    repeat: ICalAlarmRepeatData | null;\n    interval: number | null;\n    attach: ICalAttachment | null;\n    description: string | null;\n    summary: string | null;\n    attendees: ICalAttendee[];\n    x: {key: string, value: string}[];\n}\n\n\n/**\n * Usually you get an {@link ICalAlarm} object like this:\n *\n * ```javascript\n * import ical from 'ical-generator';\n * const calendar = ical();\n * const event = calendar.createEvent();\n * const alarm = event.createAlarm();\n * ```\n *\n * You can also use the {@link ICalAlarm} object directly:\n *\n * ```javascript\n * import ical, {ICalAlarm} from 'ical-generator';\n * const alarm = new ICalAlarm();\n * event.alarms([alarm]);\n * ```\n */\nexport default class ICalAlarm {\n    private readonly data: ICalInternalAlarmData;\n    private readonly event: ICalEvent;\n\n    /**\n     * Constructor of {@link ICalAttendee}. The event reference is required\n     * to query the calendar's timezone and summary when required.\n     *\n     * @param data Alarm Data\n     * @param event Reference to ICalEvent object\n     */\n    constructor (data: ICalAlarmData, event: ICalEvent) {\n        this.data = {\n            type: ICalAlarmType.display,\n            trigger: -600,\n            relatesTo: null,\n            repeat: null,\n            interval: null,\n            attach: null,\n            description: null,\n            summary: null,\n            attendees: [],\n            x: []\n        };\n\n        this.event = event;\n        if (!event) {\n            throw new Error('`event` option required!');\n        }\n\n        if (data.type !== undefined) this.type(data.type);\n        if ('trigger' in data && data.trigger !== undefined) this.trigger(data.trigger);\n        if ('triggerBefore' in data && data.triggerBefore !== undefined) this.triggerBefore(data.triggerBefore);\n        if ('triggerAfter' in data && data.triggerAfter !== undefined) this.triggerAfter(data.triggerAfter);\n        if (data.repeat) this.repeat(data.repeat);\n        if (data.attach !== undefined) this.attach(data.attach);\n        if (data.description !== undefined) this.description(data.description);\n        if (data.summary !== undefined) this.summary(data.summary);\n        if (data.attendees !== undefined) this.attendees(data.attendees);\n        if (data.x !== undefined) this.x(data.x);\n    }\n\n\n    /**\n     * Get the alarm type\n     * @since 0.2.1\n     */\n    type (type: ICalAlarmType): this;\n\n    /**\n     * Set the alarm type. See {@link ICalAlarmType}\n     * for available status options.\n     * @since 0.2.1\n     */\n    type (): ICalAlarmType;\n    type (type?: ICalAlarmType): this | ICalAlarmType {\n        if (type === undefined) {\n            return this.data.type;\n        }\n        if (!type || !Object.keys(ICalAlarmType).includes(type)) {\n            throw new Error('`type` is not correct, must be either `display` or `audio`!');\n        }\n\n        this.data.type = type;\n        return this;\n    }\n\n\n    /**\n     * Get the trigger time for the alarm. Can either\n     * be a date and time value ({@link ICalDateTimeValue}) or\n     * a number, which will represent the seconds between\n     * alarm and event start. The number is negative, if the\n     * alarm is triggered after the event started.\n     *\n     * @since 0.2.1\n     */\n    trigger (): number | ICalDateTimeValue;\n\n    /**\n     * Use this method to set the alarm time.\n     *\n     * ```javascript\n     * const cal = ical();\n     * const event = cal.createEvent();\n     * const alarm = cal.createAlarm();\n     *\n     * alarm.trigger(600); // -> 10 minutes before event starts\n     * alarm.trigger(new Date()); // -> now\n     * ```\n     *\n     * You can use any supported date object, see\n     * [readme](https://github.com/sebbo2002/ical-generator#-date-time--timezones)\n     * for details about supported values and timezone handling.\n     *\n     * @since 0.2.1\n     */\n    trigger (trigger: number | ICalDateTimeValue | Date): this;\n    trigger (trigger?: number | ICalDateTimeValue | Date): this | number | ICalDateTimeValue {\n\n        // Getter\n        if (trigger === undefined && typeof this.data.trigger === 'number') {\n            return -1 * this.data.trigger;\n        }\n        if (trigger === undefined) {\n            return this.data.trigger;\n        }\n\n        // Setter\n        if (typeof trigger === 'number' && isFinite(trigger)) {\n            this.data.trigger = -1 * trigger;\n        }\n        else if(!trigger || typeof trigger === 'number') {\n            throw new Error('`trigger` is not correct, must be a finite number or a supported date!');\n        }\n        else {\n            this.data.trigger = checkDate(trigger, 'trigger');\n        }\n\n        return this;\n    }\n\n    /**\n     * Get to which time alarm trigger relates to.\n     * Can be either `START` or `END`. If the value is\n     * `START` the alarm is triggerd relative to the event start time.\n     * If the value is `END` the alarm is triggerd relative to the event end time\n     * \n     * @since 4.0.1\n     */\n    relatesTo(): ICalAlarmRelatesTo | null;\n\n    /**\n     * Use this method to set to which time alarm trigger relates to.\n     * Works only if trigger is a `number`\n     * \n     * ```javascript\n     * const cal = ical();\n     * const event = cal.createEvent();\n     * const alarm = cal.createAlarm();\n     *\n     * alarm.trigger(600); // -> 10 minutes before event starts\n     * \n     * alarm.relatesTo('START'); // -> 10 minutes before event starts\n     * alarm.relatesTo('END'); // -> 10 minutes before event ends\n     * \n     * alarm.trigger(-600); // -> 10 minutes after event starts\n     * \n     * alarm.relatesTo('START'); // -> 10 minutes after event starts\n     * alarm.relatesTo('END'); // -> 10 minutes after event ends\n     * ```\n     * @since 4.0.1\n     */\n    relatesTo(relatesTo: ICalAlarmRelatesTo | null): this;\n    relatesTo(relatesTo?: ICalAlarmRelatesTo | null): this | ICalAlarmRelatesTo | null {\n        if (relatesTo === undefined) {\n            return this.data.relatesTo;\n        }\n        if (!relatesTo) {\n            this.data.relatesTo = null;\n            return this;\n        }\n\n        if (!Object.values(ICalAlarmRelatesTo).includes(relatesTo)) {\n            throw new Error('`relatesTo` is not correct, must be either `START` or `END`!');\n        }\n\n        this.data.relatesTo = relatesTo;\n        return this;\n    }\n\n\n    /**\n     * Get the trigger time for the alarm. Can either\n     * be a date and time value ({@link ICalDateTimeValue}) or\n     * a number, which will represent the seconds between\n     * alarm and event start. The number is negative, if the\n     * alarm is triggered before the event started.\n     *\n     * @since 0.2.1\n     */\n    triggerAfter (): number | ICalDateTimeValue;\n\n    /**\n     * Use this method to set the alarm time. Unlike `trigger`, this time\n     * the alarm takes place after the event has started.\n     *\n     * ```javascript\n     * const cal = ical();\n     * const event = cal.createEvent();\n     * const alarm = cal.createAlarm();\n     *\n     * alarm.trigger(600); // -> 10 minutes after event starts\n     * ```\n     *\n     * You can use any supported date object, see\n     * [readme](https://github.com/sebbo2002/ical-generator#-date-time--timezones)\n     * for details about supported values and timezone handling.\n     *\n     * @since 0.2.1\n     */\n    triggerAfter (trigger: number | ICalDateTimeValue): this;\n    triggerAfter (trigger?: number | ICalDateTimeValue): this | number | ICalDateTimeValue {\n        if (trigger === undefined) {\n            return this.data.trigger;\n        }\n\n        return this.trigger(typeof trigger === 'number' ? -1 * trigger : trigger);\n    }\n\n\n    /**\n     * Get the trigger time for the alarm. Can either\n     * be a date and time value ({@link ICalDateTimeValue}) or\n     * a number, which will represent the seconds between\n     * alarm and event start. The number is negative, if the\n     * alarm is triggered after the event started.\n     *\n     * @since 0.2.1\n     * @alias trigger\n     */\n    triggerBefore (trigger: number | ICalDateTimeValue): this;\n\n    /**\n     * Use this method to set the alarm time.\n     *\n     * ```javascript\n     * const cal = ical();\n     * const event = cal.createEvent();\n     * const alarm = cal.createAlarm();\n     *\n     * alarm.trigger(600); // -> 10 minutes before event starts\n     * alarm.trigger(new Date()); // -> now\n     * ```\n     *\n     * You can use any supported date object, see\n     * [readme](https://github.com/sebbo2002/ical-generator#-date-time--timezones)\n     * for details about supported values and timezone handling.\n     *\n     * @since 0.2.1\n     * @alias trigger\n     */\n    triggerBefore (): number | ICalDateTimeValue;\n    triggerBefore (trigger?: number | ICalDateTimeValue): this | number | ICalDateTimeValue {\n        if(trigger === undefined) {\n            return this.trigger();\n        }\n\n        return this.trigger(trigger);\n    }\n\n\n    /**\n     * Get Alarm Repetitions\n     * @since 0.2.1\n     */\n    repeat(): ICalAlarmRepeatData | null;\n\n    /**\n     * Set Alarm Repetitions. Use this to repeat the alarm.\n     *\n     * ```javascript\n     * const cal = ical();\n     * const event = cal.createEvent();\n     *\n     * // repeat the alarm 4 times every 5 minutes…\n     * cal.createAlarm({\n     *     repeat: {\n     *         times: 4,\n     *         interval: 300\n     *     }\n     * });\n     * ```\n     *\n     * @since 0.2.1\n     */\n    repeat(repeat: ICalAlarmRepeatData | null): this;\n    repeat (repeat?: ICalAlarmRepeatData | null): this | ICalAlarmRepeatData | null {\n        if (repeat === undefined) {\n            return this.data.repeat;\n        }\n        if (!repeat) {\n            this.data.repeat = null;\n            return this;\n        }\n\n        if (typeof repeat !== 'object') {\n            throw new Error('`repeat` is not correct, must be an object!');\n        }\n        if (typeof repeat.times !== 'number' || !isFinite(repeat.times)) {\n            throw new Error('`repeat.times` is not correct, must be numeric!');\n        }\n        if (typeof repeat.interval !== 'number' || !isFinite(repeat.interval)) {\n            throw new Error('`repeat.interval` is not correct, must be numeric!');\n        }\n\n        this.data.repeat = repeat;\n        return this;\n    }\n\n\n    /**\n     * Get Attachment\n     * @since 0.2.1\n     */\n    attach (): {uri: string, mime: string | null} | null;\n\n    /**\n     * Set Alarm attachment. Used to set the alarm sound\n     * if alarm type is audio. Defaults to \"Basso\".\n     *\n     * ```javascript\n     * const cal = ical();\n     * const event = cal.createEvent();\n     *\n     * event.createAlarm({\n     *     attach: 'https://example.com/notification.aud'\n     * });\n     *\n     * // OR\n     *\n     * event.createAlarm({\n     *     attach: {\n     *         uri: 'https://example.com/notification.aud',\n     *         mime: 'audio/basic'\n     *     }\n     * });\n     * ```\n     *\n     * @since 0.2.1\n     */\n    attach (attachment: {uri: string, mime?: string | null} | string | null): this;\n    attach (attachment?: {uri: string, mime?: string | null} | string | null): this | {uri: string, mime: string | null} | null {\n        if (attachment === undefined) {\n            return this.data.attach;\n        }\n        if (!attachment) {\n            this.data.attach = null;\n            return this;\n        }\n\n        let _attach = null;\n        if (typeof attachment === 'string') {\n            _attach = {\n                uri: attachment,\n                mime: null\n            };\n        }\n        else if (typeof attachment === 'object') {\n            _attach = {\n                uri: attachment.uri,\n                mime: attachment.mime || null\n            };\n        }\n        else {\n            throw new Error(\n                '`attachment` needs to be a valid formed string or an object. See https://sebbo2002.github.io/' +\n                'ical-generator/develop/reference/classes/ICalAlarm.html#attach'\n            );\n        }\n\n        if (!_attach.uri) {\n            throw new Error('`attach.uri` is empty!');\n        }\n\n        this.data.attach = {\n            uri: _attach.uri,\n            mime: _attach.mime\n        };\n        return this;\n    }\n\n\n    /**\n     * Get the alarm description. Used to set the alarm message\n     * if alarm type is `display`. If the alarm type is `email`, it's\n     * used to set the email body. Defaults to the event's summary.\n     *\n     * @since 0.2.1\n     */\n    description (): string | null;\n\n    /**\n     * Set the alarm description. Used to set the alarm message\n     * if alarm type is `display`. If the alarm type is `email`, it's\n     * used to set the email body. Defaults to the event's summary.\n     *\n     * @since 0.2.1\n     */\n    description (description: string | null): this;\n    description (description?: string | null): this | string | null {\n        if (description === undefined) {\n            return this.data.description;\n        }\n        if (!description) {\n            this.data.description = null;\n            return this;\n        }\n\n        this.data.description = description;\n        return this;\n    }\n\n\n    /**\n     * Get the alarm summary. Used to set the email subject\n     * if alarm type is `email`. Defaults to the event's summary.\n     *\n     * @since 7.0.0\n     */\n    summary (): string | null;\n\n    /**\n     * Set the alarm summary. Used to set the email subject\n     * if alarm type is display. Defaults to the event's summary.\n     *\n     * @since 0.2.1\n     */\n    summary (summary: string | null): this;\n    summary (summary?: string | null): this | string | null {\n        if (summary === undefined) {\n            return this.data.summary;\n        }\n        if (!summary) {\n            this.data.summary = null;\n            return this;\n        }\n\n        this.data.summary = summary;\n        return this;\n    }\n\n\n    /**\n     * Creates a new {@link ICalAttendee} and returns it. Use options to prefill\n     * the attendee's attributes. Calling this method without options will create\n     * an empty attendee.\n     *\n     * @since 7.0.0\n     */\n    createAttendee(data: ICalAttendee | ICalAttendeeData | string): ICalAttendee {\n        if (data instanceof ICalAttendee) {\n            this.data.attendees.push(data);\n            return data;\n        }\n        if (typeof data === 'string') {\n            data = { email: data, ...checkNameAndMail('data', data) };\n        }\n\n        const attendee = new ICalAttendee(data, this);\n        this.data.attendees.push(attendee);\n        return attendee;\n    }\n\n\n    /**\n     * Get all attendees\n     * @since 7.0.0\n     */\n    attendees(): ICalAttendee[];\n\n    /**\n     * Add multiple attendees to your event\n     *\n     * @since 7.0.0\n     */\n    attendees(attendees: (ICalAttendee | ICalAttendeeData | string)[]): this;\n    attendees(attendees?: (ICalAttendee | ICalAttendeeData | string)[]): this | ICalAttendee[] {\n        if (!attendees) {\n            return this.data.attendees;\n        }\n\n        attendees.forEach(attendee => this.createAttendee(attendee));\n        return this;\n    }\n\n\n    /**\n     * Set X-* attributes. Woun't filter double attributes,\n     * which are also added by another method (e.g. type),\n     * so these attributes may be inserted twice.\n     *\n     * ```javascript\n     * alarm.x([\n     *     {\n     *         key: \"X-MY-CUSTOM-ATTR\",\n     *         value: \"1337!\"\n     *     }\n     * ]);\n     *\n     * alarm.x([\n     *     [\"X-MY-CUSTOM-ATTR\", \"1337!\"]\n     * ]);\n     *\n     * alarm.x({\n     *     \"X-MY-CUSTOM-ATTR\": \"1337!\"\n     * });\n     * ```\n     *\n     * @since 1.9.0\n     */\n    x (keyOrArray: {key: string, value: string}[] | [string, string][] | Record<string, string>): this;\n\n    /**\n     * Set a X-* attribute. Woun't filter double attributes,\n     * which are also added by another method (e.g. type),\n     * so these attributes may be inserted twice.\n     *\n     * ```javascript\n     * alarm.x(\"X-MY-CUSTOM-ATTR\", \"1337!\");\n     * ```\n     *\n     * @since 1.9.0\n     */\n    x (keyOrArray: string, value: string): this;\n\n    /**\n     * Get all custom X-* attributes.\n     * @since 1.9.0\n     */\n    x (): {key: string, value: string}[];\n    x (keyOrArray?: ({key: string, value: string})[] | [string, string][] | Record<string, string> | string, value?: string): this | void | ({key: string, value: string})[] {\n        if(keyOrArray === undefined) {\n            return addOrGetCustomAttributes (this.data);\n        }\n\n        if(typeof keyOrArray === 'string' && typeof value === 'string') {\n            addOrGetCustomAttributes (this.data, keyOrArray, value);\n        }\n        else if(typeof keyOrArray === 'object') {\n            addOrGetCustomAttributes (this.data, keyOrArray);\n        }\n        else {\n            throw new Error('Either key or value is not a string!');\n        }\n\n        return this;\n    }\n\n\n    /**\n     * Return a shallow copy of the alarm's options for JSON stringification.\n     * Third party objects like moment.js values are stringified as well. Can\n     * be used for persistence.\n     *\n     * @since 0.2.4\n     */\n    toJSON (): ICalAlarmJSONData {\n        const trigger = this.trigger();\n        return Object.assign({}, this.data, {\n            trigger: typeof trigger === 'number' ? trigger : toJSON(trigger),\n            x: this.x()\n        });\n    }\n\n\n    /**\n     * Return generated event as a string.\n     *\n     * ```javascript\n     * const alarm = event.createAlarm();\n     * console.log(alarm.toString()); // → BEGIN:VALARM…\n     * ```\n     */\n    toString (): string {\n        let g = 'BEGIN:VALARM\\r\\n';\n\n        // ACTION\n        g += 'ACTION:' + this.data.type.toUpperCase() + '\\r\\n';\n\n        if (typeof this.data.trigger === 'number' && this.data.relatesTo === null) {\n            if (this.data.trigger > 0) {\n                g += 'TRIGGER;RELATED=END:' + toDurationString(this.data.trigger) + '\\r\\n';\n            }\n            else {\n                g += 'TRIGGER:' + toDurationString(this.data.trigger) + '\\r\\n';\n            }\n        } \n        else if (typeof this.data.trigger === 'number') {\n            g += 'TRIGGER;RELATED=' + this.data.relatesTo?.toUpperCase() + ':' + toDurationString(this.data.trigger) + '\\r\\n';\n        }\n        else {\n            g += 'TRIGGER;VALUE=DATE-TIME:' + formatDate(this.event.timezone(), this.data.trigger) + '\\r\\n';\n        }\n\n        // REPEAT\n        if (this.data.repeat) {\n            if (!this.data.repeat.times) {\n                throw new Error('No value for `repeat.times` in ICalAlarm given, but required for `interval`!');\n            }\n            if (!this.data.repeat.interval) {\n                throw new Error('No value for `repeat.interval` in ICalAlarm given, but required for `repeat`!');\n            }\n\n            g += 'REPEAT:' + this.data.repeat.times + '\\r\\n';\n            g += 'DURATION:' + toDurationString(this.data.repeat.interval) + '\\r\\n';\n        }\n\n        // ATTACH\n        if (this.data.type === 'audio' && this.data.attach && this.data.attach.mime) {\n            g += 'ATTACH;FMTTYPE=' + escape(this.data.attach.mime, false) + ':' + escape(this.data.attach.uri, false) + '\\r\\n';\n        }\n        else if (this.data.type === 'audio' && this.data.attach) {\n            g += 'ATTACH;VALUE=URI:' + escape(this.data.attach.uri, false) + '\\r\\n';\n        }\n        else if (this.data.type === 'audio') {\n            g += 'ATTACH;VALUE=URI:Basso\\r\\n';\n        }\n\n        // DESCRIPTION\n        if (this.data.type !== 'audio' && this.data.description) {\n            g += 'DESCRIPTION:' + escape(this.data.description, false) + '\\r\\n';\n        }\n        else if (this.data.type !== 'audio') {\n            g += 'DESCRIPTION:' + escape(this.event.summary(), false) + '\\r\\n';\n        }\n\n        // SUMMARY\n        if (this.data.type === 'email' && this.data.summary) {\n            g += 'SUMMARY:' + escape(this.data.summary, false) + '\\r\\n';\n        }\n        else if (this.data.type === 'email') {\n            g += 'SUMMARY:' + escape(this.event.summary(), false) + '\\r\\n';\n        }\n\n        // ATTENDEES\n        if (this.data.type === 'email') {\n            this.data.attendees.forEach(attendee => {\n                g += attendee.toString();\n            });\n        }\n\n        // CUSTOM X ATTRIBUTES\n        g += generateCustomAttributes(this.data);\n\n        g += 'END:VALARM\\r\\n';\n        return g;\n    }\n}\n", "'use strict';\n\n\nimport {escape} from './tools.ts';\n\n\nexport interface ICalCategoryData {\n    name: string;\n}\n\n\nexport interface ICalCategoryJSONData {\n    name: string;\n}\n\nexport type ICalCategoryInternalData = ICalCategoryJSONData;\n\n\n/**\n * Usually you get an {@link ICalCategory} object like this:\n *\n * ```javascript\n * import ical from 'ical-generator';\n * const calendar = ical();\n * const event = calendar.createEvent();\n * const category = event.createCategory();\n * ```\n *\n * You can also use the {@link ICalCategory} object directly:\n *\n * ```javascript\n * import ical, {ICalCategory} from 'ical-generator';\n * const category = new ICalCategory();\n * event.categories([category]);\n * ```\n */\nexport default class ICalCategory {\n    private readonly data: ICalCategoryInternalData;\n\n    /**\n     * Constructor of {@link ICalCategory}.\n     * @param data Category Data\n     */\n    constructor(data: ICalCategoryData) {\n        this.data = {\n            name: ''\n        };\n\n        if(!data.name) {\n            throw new Error('No value for `name` in ICalCategory given!');\n        }\n\n        this.name(data.name);\n    }\n\n\n    /**\n     * Get the category name\n     * @since 0.3.0\n     */\n    name(): string;\n\n    /**\n     * Set the category name\n     * @since 0.3.0\n     */\n    name(name: string): this;\n    name(name?: string): this | string {\n        if (name === undefined) {\n            return this.data.name;\n        }\n\n        this.data.name = name;\n        return this;\n    }\n\n\n    /**\n     * Return a shallow copy of the category's options for JSON stringification.\n     * Can be used for persistence.\n     *\n     * @since 0.2.4\n     */\n    toJSON(): ICalCategoryInternalData {\n        return Object.assign({}, this.data);\n    }\n\n\n    /**\n     * Return generated category name as a string.\n     *\n     * ```javascript\n     * console.log(category.toString());\n     * ```\n     */\n    toString(): string {\n\n        // CN / Name\n        return escape(this.data.name, false);\n    }\n}\n", "/**\n * ical-generator supports [native Date](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date),\n * [moment.js](https://momentjs.com/) (and [moment-timezone](https://momentjs.com/timezone/), [Day.js](https://day.js.org/en/) and\n * [<PERSON><PERSON>](https://moment.github.io/luxon/)'s [DateTime](https://moment.github.io/luxon/docs/class/src/datetime.js~DateTime.html)\n * objects. You can also pass a string which is then passed to javascript's Date internally.\n */\nexport type ICalDateTimeValue = Date | ICalMomentStub | ICalMomentTimezoneStub | ICalLuxonDateTimeStub | ICalDayJsStub | string;\n\nexport interface ICalRepeatingOptions {\n    freq: ICalEventRepeatingFreq;\n    count?: number;\n    interval?: number;\n    until?: ICalDateTimeValue;\n    byDay?: ICalWeekday[] | ICalWeekday;\n    byMonth?: number[] | number;\n    byMonthDay?: number[] | number;\n    bySetPos?: number[] | number;\n    exclude?: ICalDateTimeValue[] | ICalDateTimeValue;\n    startOfWeek?: ICalWeekday;\n}\n\nexport type ICalLocation = ICalLocationWithTitle | ICalLocationWithoutTitle;\n\nexport interface ICalLocationWithTitle {\n    title: string;\n    address?: string;\n    radius?: number;\n    geo?: ICalGeo;\n}\n\nexport interface ICalLocationWithoutTitle {\n    geo: ICalGeo;\n}\n\nexport interface ICalGeo {\n    lat: number;\n    lon: number;\n}\n\nexport interface ICalOrganizer {\n    name: string;\n    email?: string;\n    mailto?: string;\n    sentBy?: string;\n}\n\nexport interface ICalDescription {\n    plain: string;\n    html?: string;\n}\n\nexport interface ICalTimezone {\n    name: string | null;\n    generator?: (timezone: string) => string|null;\n}\n\nexport interface ICalMomentStub {\n    format(format?: string): string;\n    clone(): ICalMomentStub;\n    utc(): ICalMomentStub;\n    toDate(): Date;\n    isValid(): boolean;\n    toJSON(): string;\n}\n\nexport interface ICalMomentTimezoneStub extends ICalMomentStub {\n    clone(): ICalMomentTimezoneStub;\n    utc(): ICalMomentTimezoneStub;\n    tz(): string | undefined;\n    tz(timezone: string): ICalMomentTimezoneStub;\n}\n\nexport interface ICalMomentDurationStub {\n    asSeconds(): number;\n}\n\nexport interface ICalLuxonDateTimeStub {\n    setZone(zone?: string): ICalLuxonDateTimeStub;\n    zone: { type: string; };\n    toFormat(fmt: string): string;\n    toJSDate(): Date;\n    get isValid(): boolean;\n    toJSON(): string | null;\n}\n\nexport interface ICalDayJsStub {\n    tz(zone?: string): ICalDayJsStub;\n    utc(): ICalDayJsStub;\n    format(format?: string): string;\n    toDate(): Date;\n    isValid(): boolean;\n    toJSON(): string;\n}\n\nexport interface ICalRRuleStub {\n    between(after: Date, before: Date, inc?: boolean, iterator?: (d: Date, len: number) => boolean): Date[];\n    toString(): string;\n}\n\nexport enum ICalEventRepeatingFreq {\n    SECONDLY = 'SECONDLY',\n    MINUTELY = 'MINUTELY',\n    HOURLY = 'HOURLY',\n    DAILY = 'DAILY',\n    WEEKLY = 'WEEKLY',\n    MONTHLY = 'MONTHLY',\n    YEARLY = 'YEARLY'\n}\n\nexport enum ICalWeekday {\n    SU = 'SU',\n    MO = 'MO',\n    TU = 'TU',\n    WE = 'WE',\n    TH = 'TH',\n    FR = 'FR',\n    SA = 'SA'\n}\n", "'use strict';\n\nimport {\n    addOrGetCustomAttributes,\n    checkEnum,\n    foldLines,\n    generateCustomAttributes,\n    isMomentDuration,\n    toDurationString\n} from './tools.ts';\nimport ICalEvent, {ICalEventData, ICalEventJSONData} from './event.ts';\nimport { ICalMomentDurationStub, ICalTimezone } from './types.ts';\n\n\nexport interface ICalCalendarData {\n    prodId?: ICalCalendarProdIdData | string;\n    method?: ICalCalendarMethod | null;\n    name?: string | null;\n    description?: string | null;\n    timezone?: ICalTimezone | string | null;\n    source?: string | null;\n    url?: string | null;\n    scale?: string | null;\n    ttl?: number | ICalMomentDurationStub | null;\n    events?: (ICalEvent | ICalEventData)[];\n    x?: {key: string, value: string}[] | [string, string][] | Record<string, string>;\n}\n\ninterface ICalCalendarInternalData {\n    prodId: string;\n    method: ICalCalendarMethod | null;\n    name: string | null;\n    description: string | null;\n    timezone: ICalTimezone | null;\n    source: string | null;\n    url: string | null;\n    scale: string | null;\n    ttl: number | null;\n    events: ICalEvent[];\n    x: [string, string][];\n}\n\nexport interface ICalCalendarJSONData {\n    prodId: string;\n    method: ICalCalendarMethod | null;\n    name: string | null;\n    description: string | null;\n    timezone: string | null;\n    source: string | null;\n    url: string | null;\n    scale: string | null;\n    ttl: number | null;\n    events: ICalEventJSONData[];\n    x: {key: string, value: string}[];\n}\n\nexport interface ICalCalendarProdIdData {\n    company: string;\n    product: string;\n    language?: string;\n}\n\nexport enum ICalCalendarMethod {\n    PUBLISH = 'PUBLISH',\n    REQUEST = 'REQUEST',\n    REPLY = 'REPLY',\n    ADD = 'ADD',\n    CANCEL = 'CANCEL',\n    REFRESH = 'REFRESH',\n    COUNTER = 'COUNTER',\n    DECLINECOUNTER = 'DECLINECOUNTER'\n}\n\n\n/**\n * Usually you get an {@link ICalCalendar} object like this:\n * ```javascript\n * import ical from 'ical-generator';\n * const calendar = ical();\n * ```\n *\n * But you can also use the constructor directly like this:\n * ```javascript\n * import {ICalCalendar} from 'ical-generator';\n * const calendar = new ICalCalendar();\n * ```\n */\nexport default class ICalCalendar {\n    private readonly data: ICalCalendarInternalData;\n\n    /**\n     * You can pass options to set up your calendar or use setters to do this.\n     *\n     * ```javascript\n     *  * import ical from 'ical-generator';\n     *\n     * // or use require:\n     * // const { default: ical } = require('ical-generator');\n     *\n     *\n     * const cal = ical({name: 'my first iCal'});\n     *\n     * // is the same as\n     *\n     * const cal = ical().name('my first iCal');\n     *\n     * // is the same as\n     *\n     * const cal = ical();\n     * cal.name('sebbo.net');\n     * ```\n     *\n     * `cal.toString()` would then produce the following string:\n     * ```text\n     * BEGIN:VCALENDAR\n     * VERSION:2.0\n     * PRODID:-//sebbo.net//ical-generator//EN\n     * NAME:sebbo.net\n     * X-WR-CALNAME:sebbo.net\n     * END:VCALENDAR\n     * ```\n     *\n     * @param data Calendar data\n     */\n    constructor(data: ICalCalendarData = {}) {\n        this.data = {\n            prodId: '//sebbo.net//ical-generator//EN',\n            method: null,\n            name: null,\n            description: null,\n            timezone: null,\n            source: null,\n            url: null,\n            scale: null,\n            ttl: null,\n            events: [],\n            x: []\n        };\n\n        if (data.prodId !== undefined) this.prodId(data.prodId);\n        if (data.method !== undefined) this.method(data.method);\n        if (data.name !== undefined) this.name(data.name);\n        if (data.description !== undefined) this.description(data.description);\n        if (data.timezone !== undefined) this.timezone(data.timezone);\n        if (data.source !== undefined) this.source(data.source);\n        if (data.url !== undefined) this.url(data.url);\n        if (data.scale !== undefined) this.scale(data.scale);\n        if (data.ttl !== undefined) this.ttl(data.ttl);\n        if (data.events !== undefined) this.events(data.events);\n        if (data.x !== undefined) this.x(data.x);\n    }\n\n\n    /**\n     * Get your feed's prodid. Will always return a string.\n     * @since 0.2.0\n     */\n    prodId(): string;\n\n    /**\n     * Set your feed's prodid. `prodid` can be either a\n     * string like `//sebbo.net//ical-generator//EN` or a\n     * valid {@link ICalCalendarProdIdData} object. `language`\n     * is optional and defaults to `EN`.\n     *\n     * ```javascript\n     * cal.prodId({\n     *     company: 'My Company',\n     *     product: 'My Product',\n     *     language: 'EN' // optional, defaults to EN\n     * });\n     * ```\n     *\n     * `cal.toString()` would then produce the following string:\n     * ```text\n     * PRODID:-//My Company//My Product//EN\n     * ```\n     *\n     * @since 0.2.0\n     */\n    prodId(prodId: ICalCalendarProdIdData | string): this;\n    prodId(prodId?: ICalCalendarProdIdData | string): this | string {\n        if (!prodId) {\n            return this.data.prodId;\n        }\n\n        if (typeof prodId === 'string') {\n            this.data.prodId = prodId;\n            return this;\n        }\n\n        if (typeof prodId !== 'object') {\n            throw new Error('`prodid` needs to be a string or an object!');\n        }\n\n        if (!prodId.company) {\n            throw new Error('`prodid.company` is a mandatory item!');\n        }\n        if (!prodId.product) {\n            throw new Error('`prodid.product` is a mandatory item!');\n        }\n\n        const language = (prodId.language || 'EN').toUpperCase();\n        this.data.prodId = '//' + prodId.company + '//' + prodId.product + '//' + language;\n        return this;\n    }\n\n\n    /**\n     * Get the feed method attribute.\n     * See {@link ICalCalendarMethod} for possible results.\n     *\n     * @since 0.2.8\n     */\n    method(): ICalCalendarMethod | null;\n\n    /**\n     * Set the feed method attribute.\n     * See {@link ICalCalendarMethod} for available options.\n     *\n     * #### Typescript Example\n     * ```typescript\n     * import {ICalCalendarMethod} from 'ical-generator';\n     *\n     * // METHOD:PUBLISH\n     * calendar.method(ICalCalendarMethod.PUBLISH);\n     * ```\n     *\n     * @since 0.2.8\n     */\n    method(method: ICalCalendarMethod | null): this;\n    method(method?: ICalCalendarMethod | null): this | ICalCalendarMethod | null {\n        if (method === undefined) {\n            return this.data.method;\n        }\n        if (!method) {\n            this.data.method = null;\n            return this;\n        }\n\n        this.data.method = checkEnum(ICalCalendarMethod, method) as ICalCalendarMethod;\n        return this;\n    }\n\n\n    /**\n     * Get your feed's name\n     * @since 0.2.0\n     */\n    name(): string | null;\n\n    /**\n     * Set your feed's name. Is used to fill `NAME`\n     * and `X-WR-CALNAME` in your iCal file.\n     *\n     * ```typescript\n     * import ical from 'ical-generator';\n     *\n     * const cal = ical();\n     * cal.name('Next Arrivals');\n     *\n     * cal.toString();\n     * ```\n     *\n     * ```text\n     * BEGIN:VCALENDAR\n     * VERSION:2.0\n     * PRODID:-//sebbo.net//ical-generator//EN\n     * NAME:Next Arrivals\n     * X-WR-CALNAME:Next Arrivals\n     * END:VCALENDAR\n     * ```\n     *\n     * @since 0.2.0\n     */\n    name(name: string | null): this;\n    name(name?: string | null): this | string | null {\n        if (name === undefined) {\n            return this.data.name;\n        }\n\n        this.data.name = name ? String(name) : null;\n        return this;\n    }\n\n\n    /**\n     * Get your feed's description\n     * @since 0.2.7\n     */\n    description(): string | null;\n\n    /**\n     * Set your feed's description\n     * @since 0.2.7\n     */\n    description(description: string | null): this;\n    description(description?: string | null): this | string | null {\n        if (description === undefined) {\n            return this.data.description;\n        }\n\n        this.data.description = description ? String(description) : null;\n        return this;\n    }\n\n\n    /**\n     * Get the current calendar timezone\n     * @since 0.2.0\n     */\n    timezone(): string | null;\n\n    /**\n     * Use this method to set your feed's timezone. Is used\n     * to fill `TIMEZONE-ID` and `X-WR-TIMEZONE` in your iCal export.\n     * Please not that all date values are treaded differently, if\n     * a timezone was set. See {@link formatDate} for details. If no\n     * time zone is specified, all information is output as UTC.\n     *\n     * ```javascript\n     * cal.timezone('America/New_York');\n     * ```\n     *\n     * @see https://github.com/sebbo2002/ical-generator#-date-time--timezones\n     * @since 0.2.0\n     */\n    timezone(timezone: string | null): this;\n\n    /**\n     * Sets the time zone to be used in this calendar file for all times of all\n     * events. Please note that if the time zone is set, ical-generator assumes\n     * that all times are already in the correct time zone. Alternatively, a\n     * `moment-timezone` or a Luxon object can be passed with `setZone`,\n     * ical-generator will then set the time zone itself.\n     *\n     * For the best support of time zones, a VTimezone entry in the calendar is\n     * recommended, which informs the client about the corresponding time zones\n     * (daylight saving time, deviation from UTC, etc.). `ical-generator` itself\n     * does not have a time zone database, so an external generator is needed here.\n     *\n     * A VTimezone generator is a function that takes a time zone as a string and\n     * returns a VTimezone component according to the ical standard. For example,\n     * ical-timezones can be used for this:\n     *\n     * ```typescript\n     * import ical from 'ical-generator';\n     * import {getVtimezoneComponent} from '@touch4it/ical-timezones';\n     *\n     * const cal = ical();\n     * cal.timezone({\n     *     name: 'FOO',\n     *     generator: getVtimezoneComponent\n     * });\n     * cal.createEvent({\n     *     start: new Date(),\n     *     timezone: 'Europe/London'\n     * });\n     * ```\n     *\n     * @see https://github.com/sebbo2002/ical-generator#-date-time--timezones\n     * @since 2.0.0\n     */\n    timezone(timezone: ICalTimezone | string | null): this;\n    timezone(timezone?: ICalTimezone | string | null): this | string | null {\n        if (timezone === undefined) {\n            return this.data.timezone?.name || null;\n        }\n\n        if(timezone === 'UTC') {\n            this.data.timezone = null;\n        }\n        else if(typeof timezone === 'string') {\n            this.data.timezone = {name: timezone};\n        }\n        else if(timezone === null) {\n            this.data.timezone = null;\n        }\n        else {\n            this.data.timezone = timezone;\n        }\n\n        return this;\n    }\n\n\n    /**\n     * Get current value of the `SOURCE` attribute.\n     * @since 2.2.0-develop.1\n     */\n    source(): string | null;\n\n    /**\n     * Use this method to set your feed's `SOURCE` attribute.\n     * This tells the client where to refresh your feed.\n     *\n     * ```javascript\n     * cal.source('http://example.com/my/original_source.ical');\n     * ```\n     *\n     * ```text\n     * SOURCE;VALUE=URI:http://example.com/my/original_source.ical\n     * ```\n     *\n     * @since 2.2.0-develop.1\n     */\n    source(source: string | null): this;\n    source(source?: string | null): this | string | null {\n        if (source === undefined) {\n            return this.data.source;\n        }\n\n        this.data.source = source || null;\n        return this;\n    }\n\n\n    /**\n     * Get your feed's URL\n     * @since 0.2.5\n     */\n    url(): string | null;\n\n    /**\n     * Set your feed's URL\n     *\n     * ```javascript\n     * calendar.url('http://example.com/my/feed.ical');\n     * ```\n     *\n     * @since 0.2.5\n     */\n    url(url: string | null): this;\n    url(url?: string | null): this | string | null {\n        if (url === undefined) {\n            return this.data.url;\n        }\n\n        this.data.url = url || null;\n        return this;\n    }\n\n\n    /**\n     * Get current value of the `CALSCALE` attribute. It will\n     * return `null` if no value was set. The iCal standard\n     * specifies this as `GREGORIAN` if no value is present.\n     *\n     * @since 1.8.0\n     */\n    scale(): string | null;\n\n    /**\n     * Use this method to set your feed's `CALSCALE` attribute. There is no\n     * default value for this property and it will not appear in your iCal\n     * file unless set. The iCal standard specifies this as `GREGORIAN` if\n     * no value is present.\n     *\n     * ```javascript\n     * cal.scale('gregorian');\n     * ```\n     *\n     * @since 1.8.0\n     */\n    scale(scale: string | null): this;\n    scale(scale?: string | null): this | string | null {\n        if (scale === undefined) {\n            return this.data.scale;\n        }\n\n        if (scale === null) {\n            this.data.scale = null;\n        }\n        else {\n            this.data.scale = scale.toUpperCase();\n        }\n\n        return this;\n    }\n\n\n    /**\n     * Get the current ttl duration in seconds\n     * @since 0.2.5\n     */\n    ttl(): number | null;\n\n    /**\n     * Use this method to set your feed's time to live\n     * (in seconds). Is used to fill `REFRESH-INTERVAL` and\n     * `X-PUBLISHED-TTL` in your iCal.\n     *\n     * ```javascript\n     * const cal = ical().ttl(60 * 60 * 24); // 1 day\n     * ```\n     *\n     * You can also pass a moment.js duration object. Zero, null\n     * or negative numbers will reset the `ttl` attribute.\n     *\n     * @since 0.2.5\n     */\n    ttl(ttl: number | ICalMomentDurationStub | null): this;\n    ttl(ttl?: number | ICalMomentDurationStub | null): this | number | null {\n        if (ttl === undefined) {\n            return this.data.ttl;\n        }\n\n        if (isMomentDuration(ttl)) {\n            this.data.ttl = ttl.asSeconds();\n        }\n        else if (ttl && ttl > 0) {\n            this.data.ttl = ttl;\n        }\n        else {\n            this.data.ttl = null;\n        }\n\n        return this;\n    }\n\n\n    /**\n     * Creates a new {@link ICalEvent} and returns it. Use options to prefill the event's attributes.\n     * Calling this method without options will create an empty event.\n     *\n     * ```javascript\n     * import ical from 'ical-generator';\n     *\n     * // or use require:\n     * // const { default: ical } = require('ical-generator');\n     *\n     * const cal = ical();\n     * const event = cal.createEvent({summary: 'My Event'});\n     *\n     * // overwrite event summary\n     * event.summary('Your Event');\n     * ```\n     *\n     * @since 0.2.0\n     */\n    createEvent(data: ICalEvent | ICalEventData): ICalEvent {\n        const event = data instanceof ICalEvent ? data : new ICalEvent(data, this);\n        this.data.events.push(event);\n        return event;\n    }\n\n\n    /**\n     * Returns all events of this calendar.\n     *\n     * ```javascript\n     * const cal = ical();\n     *\n     * cal.events([\n     *     {\n     *        start: new Date(),\n     *        end: new Date(new Date().getTime() + 3600000),\n     *        summary: 'Example Event',\n     *        description: 'It works ;)',\n     *        url: 'http://sebbo.net/'\n     *     }\n     * ]);\n     *\n     * cal.events(); // --> [ICalEvent]\n     * ```\n     *\n     * @since 0.2.0\n     */\n    events(): ICalEvent[];\n\n    /**\n     * Add multiple events to your calendar.\n     *\n     * ```javascript\n     * const cal = ical();\n     *\n     * cal.events([\n     *     {\n     *        start: new Date(),\n     *        end: new Date(new Date().getTime() + 3600000),\n     *        summary: 'Example Event',\n     *        description: 'It works ;)',\n     *        url: 'http://sebbo.net/'\n     *     }\n     * ]);\n     *\n     * cal.events(); // --> [ICalEvent]\n     * ```\n     *\n     * @since 0.2.0\n     */\n    events(events: (ICalEvent | ICalEventData)[]): this;\n    events(events?: (ICalEvent | ICalEventData)[]): this | ICalEvent[] {\n        if (!events) {\n            return this.data.events;\n        }\n\n        events.forEach((e: ICalEvent | ICalEventData) => this.createEvent(e));\n        return this;\n    }\n\n\n    /**\n     * Remove all events from the calendar without\n     * touching any other data like name or prodId.\n     *\n     * @since 2.0.0-develop.1\n     */\n    clear(): this {\n        this.data.events = [];\n        return this;\n    }\n\n\n    /**\n     * Set X-* attributes. Woun't filter double attributes,\n     * which are also added by another method (e.g. busystatus),\n     * so these attributes may be inserted twice.\n     *\n     * ```javascript\n     * calendar.x([\n     *     {\n     *         key: \"X-MY-CUSTOM-ATTR\",\n     *         value: \"1337!\"\n     *     }\n     * ]);\n     *\n     * calendar.x([\n     *     [\"X-MY-CUSTOM-ATTR\", \"1337!\"]\n     * ]);\n     *\n     * calendar.x({\n     *     \"X-MY-CUSTOM-ATTR\": \"1337!\"\n     * });\n     * ```\n     *\n     * ```text\n     * BEGIN:VCALENDAR\n     * VERSION:2.0\n     * PRODID:-//sebbo.net//ical-generator//EN\n     * X-MY-CUSTOM-ATTR:1337!\n     * END:VCALENDAR\n     * ```\n     *\n     * @since 1.9.0\n     */\n    x (keyOrArray: {key: string, value: string}[] | [string, string][] | Record<string, string>): this;\n\n    /**\n     * Set a X-* attribute. Woun't filter double attributes,\n     * which are also added by another method (e.g. busystatus),\n     * so these attributes may be inserted twice.\n     *\n     * ```javascript\n     * calendar.x(\"X-MY-CUSTOM-ATTR\", \"1337!\");\n     * ```\n     *\n     * ```text\n     * BEGIN:VCALENDAR\n     * VERSION:2.0\n     * PRODID:-//sebbo.net//ical-generator//EN\n     * X-MY-CUSTOM-ATTR:1337!\n     * END:VCALENDAR\n     * ```\n     *\n     * @since 1.9.0\n     */\n    x (keyOrArray: string, value: string): this;\n\n    /**\n     * Get all custom X-* attributes.\n     * @since 1.9.0\n     */\n    x (): {key: string, value: string}[];\n    x (keyOrArray?: {key: string, value: string}[] | [string, string][] | Record<string, string> | string, value?: string): this | void | ({key: string, value: string})[] {\n        if(keyOrArray === undefined) {\n            return addOrGetCustomAttributes (this.data);\n        }\n\n        if(typeof keyOrArray === 'string' && typeof value === 'string') {\n            addOrGetCustomAttributes (this.data, keyOrArray, value);\n        }\n        else if(typeof keyOrArray === 'object') {\n            addOrGetCustomAttributes (this.data, keyOrArray);\n        }\n        else {\n            throw new Error('Either key or value is not a string!');\n        }\n\n        return this;\n    }\n\n\n    /**\n     * Return a shallow copy of the calendar's options for JSON stringification.\n     * Third party objects like moment.js values or RRule objects are stringified\n     * as well. Can be used for persistence.\n     *\n     * ```javascript\n     * const cal = ical();\n     * const json = JSON.stringify(cal);\n     *\n     * // later: restore calendar data\n     * cal = ical(JSON.parse(json));\n     * ```\n     *\n     * @since 0.2.4\n     */\n    toJSON(): ICalCalendarJSONData {\n        return Object.assign({}, this.data, {\n            timezone: this.timezone(),\n            events: this.data.events.map(event => event.toJSON()),\n            x: this.x()\n        });\n    }\n\n\n    /**\n     * Get the number of events added to your calendar\n     */\n    length(): number {\n        return this.data.events.length;\n    }\n\n\n    /**\n     * Return generated calendar as a string.\n     *\n     * ```javascript\n     * const cal = ical();\n     * console.log(cal.toString()); // → BEGIN:VCALENDAR…\n     * ```\n     */\n    toString(): string {\n        let g = '';\n\n        // VCALENDAR and VERSION\n        g = 'BEGIN:VCALENDAR\\r\\nVERSION:2.0\\r\\n';\n\n        // PRODID\n        g += 'PRODID:-' + this.data.prodId + '\\r\\n';\n\n        // URL\n        if (this.data.url) {\n            g += 'URL:' + this.data.url + '\\r\\n';\n        }\n\n        // SOURCE\n        if (this.data.source) {\n            g += 'SOURCE;VALUE=URI:' + this.data.source + '\\r\\n';\n        }\n\n        // CALSCALE\n        if (this.data.scale) {\n            g += 'CALSCALE:' + this.data.scale + '\\r\\n';\n        }\n\n        // METHOD\n        if (this.data.method) {\n            g += 'METHOD:' + this.data.method + '\\r\\n';\n        }\n\n        // NAME\n        if (this.data.name) {\n            g += 'NAME:' + this.data.name + '\\r\\n';\n            g += 'X-WR-CALNAME:' + this.data.name + '\\r\\n';\n        }\n\n        // Description\n        if (this.data.description) {\n            g += 'X-WR-CALDESC:' + this.data.description + '\\r\\n';\n        }\n\n        // Timezone\n        if(this.data.timezone?.generator) {\n            const timezones = [...new Set([\n                this.timezone(),\n                ...this.data.events.map(event => event.timezone())\n            ])].filter(tz => tz !== null && !tz.startsWith('/')) as string[];\n\n            timezones.forEach(tz => {\n                if(!this.data.timezone?.generator) {\n                    return;\n                }\n\n                const s = this.data.timezone.generator(tz);\n                if(!s) {\n                    return;\n                }\n\n                g += s.replace(/\\r\\n/g, '\\n')\n                    .replace(/\\n/g, '\\r\\n')\n                    .trim() + '\\r\\n';\n            });\n        }\n        if (this.data.timezone?.name) {\n            g += 'TIMEZONE-ID:' + this.data.timezone.name + '\\r\\n';\n            g += 'X-WR-TIMEZONE:' + this.data.timezone.name + '\\r\\n';\n        }\n\n        // TTL\n        if (this.data.ttl) {\n            g += 'REFRESH-INTERVAL;VALUE=DURATION:' + toDurationString(this.data.ttl) + '\\r\\n';\n            g += 'X-PUBLISHED-TTL:' + toDurationString(this.data.ttl) + '\\r\\n';\n        }\n\n        // Events\n        this.data.events.forEach(event => g += event.toString());\n\n        // CUSTOM X ATTRIBUTES\n        g += generateCustomAttributes(this.data);\n\n        g += 'END:VCALENDAR';\n\n        return foldLines(g);\n    }\n}\n"], "mappings": "0jBAAA,IAAAA,GAAA,GAAAC,EAAAD,GAAA,eAAAE,EAAA,uBAAAC,EAAA,kBAAAC,EAAA,iBAAAC,EAAA,qBAAAC,EAAA,uBAAAC,EAAA,qBAAAC,EAAA,iBAAAC,EAAA,uBAAAC,EAAA,iBAAAC,EAAA,cAAAC,EAAA,wBAAAC,EAAA,mBAAAC,EAAA,2BAAAC,EAAA,oBAAAC,EAAA,0BAAAC,EAAA,gBAAAC,EAAA,YAAAC,GAAA,WAAAC,EAAA,cAAAC,EAAA,eAAAC,EAAA,iBAAAC,IAAA,eAAAC,EAAAxB,ICeO,SAASyB,EAAYC,EAAyBC,EAAsBC,EAAoBC,EAA4B,CAKvH,GAJGH,GAAU,WAAW,GAAG,IACvBA,EAAWA,EAAS,OAAO,CAAC,GAG7B,OAAOC,GAAM,UAAYA,aAAa,KAAM,CAC3C,IAAMG,EAAI,IAAI,KAAKH,CAAC,EAGhB,EAAIG,EAAE,eAAe,EACrB,OAAOA,EAAE,YAAY,EAAI,CAAC,EAAE,SAAS,EAAG,GAAG,EAC3CA,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAG,GAAG,EAS7C,OANGJ,IACC,EAAII,EAAE,YAAY,EACd,OAAOA,EAAE,SAAS,EAAI,CAAC,EAAE,SAAS,EAAG,GAAG,EACxCA,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAG,GAAG,GAG3CF,EACQ,EAGRF,GACC,GAAK,IAAMI,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAG,GAAG,EAC9CA,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAG,GAAG,EACzCA,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAG,GAAG,EAEtC,IAGX,GAAK,IAAMA,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAG,GAAG,EACjDA,EAAE,cAAc,EAAE,SAAS,EAAE,SAAS,EAAG,GAAG,EAC5CA,EAAE,cAAc,EAAE,SAAS,EAAE,SAAS,EAAG,GAAG,GAC3CD,EAAW,GAAK,KAEd,EACX,SACQE,EAASJ,CAAC,EAAG,CAEjB,IAAMG,EAAIJ,EACHM,EAAWL,CAAC,GAAK,CAACA,EAAE,GAAG,EAAIA,EAAE,MAAM,EAAE,GAAGD,CAAQ,EAAIC,EACpDE,GAAaD,GAAYI,EAAWL,CAAC,GAAKA,EAAE,GAAG,EAAKA,EAAIA,EAAE,IAAI,EAErE,OAAOG,EAAE,OAAO,UAAU,GAAMF,EAE5B,GADA,IAAME,EAAE,OAAO,QAAQ,GAAKD,GAAYH,EAAW,GAAK,KAEhE,SACQO,EAAYN,CAAC,EAAG,CACpB,IAAMG,EAAIJ,EACJC,EAAE,QAAQD,CAAQ,EACjBG,GAAaD,GAAYD,EAAE,KAAK,OAAS,SAAYA,EAAIA,EAAE,QAAQ,KAAK,EAE/E,OAAOG,EAAE,SAAS,UAAU,GAAMF,EAE9B,GADA,IAAME,EAAE,SAAS,QAAQ,GAAKD,GAAYH,EAAW,GAAK,KAElE,KACK,CAGD,IAAII,EAAIH,EACR,GAAGD,EACCI,EAAI,OAAOH,EAAE,IAAO,WAAaA,EAAE,GAAGD,CAAQ,EAAIC,UAE9C,CAAAE,EAIH,GAAI,OAAOF,EAAE,KAAQ,WACtBG,EAAIH,EAAE,IAAI,MAGV,OAAM,IAAI,MAAM,2EAA2E,EAG/F,OAAOG,EAAE,OAAO,UAAU,GAAMF,EAE5B,GADA,IAAME,EAAE,OAAO,QAAQ,GAAKD,GAAYH,EAAW,GAAK,KAEhE,CACJ,CAOO,SAASQ,EAAcR,EAAyBS,EAAkBC,EAAyCC,EAA2E,CACzL,IAAIC,EAAU,GACVT,EAAWQ,GAAW,UAAY,GAEtC,OAAIA,GAAW,WACXC,EAAU,SAAWD,EAAU,SAI/BR,EAAW,IAGRM,EAAWG,EAAU,IAAMb,EAAWC,EAAUU,EAAM,GAAOP,CAAQ,CAChF,CAKO,SAASU,EAAQC,EAAuBC,EAA2B,CACtE,OAAO,OAAOD,CAAG,EAAE,QAAQC,EAAW,SAAW,UAAW,SAAUC,EAAO,CACzE,MAAO,KAAOA,CAClB,CAAC,EAAE,QAAQ,kBAAmB,KAAK,CACvC,CAKO,SAASC,EAAWC,EAAuB,CAC9C,OAAOA,EAAM,MAAM;AAAA,CAAM,EAAE,IAAI,SAAUC,EAAM,CAC3C,IAAIC,EAAS,GACTC,EAAI,EACR,QAAS,EAAI,EAAG,EAAIF,EAAK,OAAQ,IAAK,CAClC,IAAIG,EAAKH,EAAK,OAAO,CAAC,EAGlBG,GAAM,UAAYA,GAAM,WACxBA,GAAMH,EAAK,OAAO,EAAE,CAAC,GAIzB,IAAMI,EAAW,IAAI,YAAY,EAAE,OAAOD,CAAE,EAAE,OAC9CD,GAAKE,EACDF,EAAI,KACJD,GAAU;AAAA,GACVC,EAAIE,GAGRH,GAAUE,CACd,CACA,OAAOF,CACX,CAAC,EAAE,KAAK;AAAA,CAAM,CAClB,CAKO,SAASI,EAA0BC,EAA+BC,EAAmHC,EAAqE,CAC7P,GAAI,MAAM,QAAQD,CAAU,EACxBD,EAAK,EAAIC,EAAW,IAAKE,GAAuD,CAC5E,GAAG,MAAM,QAAQA,CAAC,EACd,OAAOA,EAEX,GAAI,OAAOA,EAAE,KAAQ,UAAY,OAAOA,EAAE,OAAU,SAChD,MAAM,IAAI,MAAM,sCAAsC,EAE1D,GAAIA,EAAE,IAAI,OAAO,EAAG,CAAC,IAAM,KACvB,MAAM,IAAI,MAAM,6BAA6B,EAGjD,MAAO,CAACA,EAAE,IAAKA,EAAE,KAAK,CAC1B,CAAC,UAEI,OAAOF,GAAe,SAC3BD,EAAK,EAAI,OAAO,QAAQC,CAAU,EAAE,IAAI,CAAC,CAACG,EAAKF,CAAK,IAAM,CACtD,GAAI,OAAOE,GAAQ,UAAY,OAAOF,GAAU,SAC5C,MAAM,IAAI,MAAM,sCAAsC,EAE1D,GAAIE,EAAI,OAAO,EAAG,CAAC,IAAM,KACrB,MAAM,IAAI,MAAM,6BAA6B,EAGjD,MAAO,CAACA,EAAKF,CAAK,CACtB,CAAC,UAEI,OAAOD,GAAe,UAAY,OAAOC,GAAU,SAAU,CAClE,GAAID,EAAW,OAAO,EAAG,CAAC,IAAM,KAC5B,MAAM,IAAI,MAAM,6BAA6B,EAGjDD,EAAK,EAAE,KAAK,CAACC,EAAYC,CAAK,CAAC,CACnC,KAEI,QAAOF,EAAK,EAAE,IAAIK,IAAM,CACpB,IAAKA,EAAE,CAAC,EACR,MAAOA,EAAE,CAAC,CACd,EAAE,CAEV,CAEO,SAASC,EAA0BN,EAAuC,CAC7E,IAAMX,EAAMW,EAAK,EACZ,IAAI,CAAC,CAACI,EAAKF,CAAK,IAAME,EAAI,YAAY,EAAI,IAAMhB,EAAOc,EAAO,EAAK,CAAC,EACpE,KAAK;AAAA,CAAM,EAChB,OAAOb,EAAI,OAASA,EAAM;AAAA,EAAS,EACvC,CASO,SAASkB,EAAkBC,EAAmBN,EAA8C,CAC/F,IAAIP,EAA+B,KAEnC,GAAI,OAAOO,GAAU,SAAU,CAC3B,IAAMX,EAAQW,EAAM,MAAM,mBAAmB,EACzCX,EACAI,EAAS,CACL,KAAMJ,EAAM,CAAC,EAAE,KAAK,EACpB,MAAOA,EAAM,CAAC,EAAE,KAAK,CACzB,EAEIW,EAAM,SAAS,GAAG,IACtBP,EAAS,CACL,KAAMO,EAAM,KAAK,EACjB,MAAOA,EAAM,KAAK,CACtB,EAER,MACS,OAAOA,GAAU,WACtBP,EAAS,CACL,KAAMO,EAAM,KACZ,MAAOA,EAAM,MACb,OAAQA,EAAM,OACd,OAAQA,EAAM,MAClB,GAGJ,GAAI,CAACP,GAAU,OAAOO,GAAU,SAC5B,MAAM,IAAI,MACN,IAAMM,EAAY,4HAEtB,EAEC,GAAI,CAACb,EACN,MAAM,IAAI,MACN,IAAMa,EAAY,kJAEtB,EAGJ,GAAI,CAACb,EAAO,KACR,MAAM,IAAI,MAAM,IAAMa,EAAY,kBAAkB,EAGxD,OAAOb,CACX,CAMO,SAASc,EAAUC,EAA8BR,EAAyB,CAC7E,IAAMS,EAAgB,OAAO,OAAOD,CAAI,EAClCE,EAAW,OAAOV,CAAK,EAAE,YAAY,EAE3C,GAAI,CAACU,GAAY,CAACD,EAAc,SAASC,CAAQ,EAC7C,MAAM,IAAI,MAAM,uCAAuCD,EAAc,KAAK,IAAI,CAAC,EAAE,EAGrF,OAAOC,CACX,CAMO,SAASC,EAAUX,EAA0BM,EAAsC,CAGtF,GACKN,aAAiB,MAAQ,MAAMA,EAAM,QAAQ,CAAC,GAC9C,OAAOA,GAAU,UAAY,MAAM,IAAI,KAAKA,CAAK,EAAE,QAAQ,CAAC,EAE7D,MAAM,IAAI,MAAM,KAAKM,CAAS,4BAA4B,EAY9D,GAVGN,aAAiB,MAAQ,OAAOA,GAAU,UAK1CpB,EAAYoB,CAAK,GAAKA,EAAM,UAAY,KAKvCtB,EAASsB,CAAK,GAAKY,GAAQZ,CAAK,IAAMA,EAAM,QAAQ,EACpD,OAAOA,EAGX,MAAM,IAAI,MAAM,KAAKM,CAAS,4BAA4B,CAC9D,CAEO,SAASO,EAAOb,EAAgC,CACnD,OAAG,OAAOA,GAAU,UAAYA,aAAiB,KACtC,IAAI,KAAKA,CAAK,EAGtBpB,EAAYoB,CAAK,EACTA,EAAM,SAAS,EAGnBA,EAAM,OAAO,CACxB,CAEO,SAAStB,EAASsB,EAAmD,CAGxE,OAAOA,GAAS,MAAQA,EAAM,kBAAoB,IACtD,CACO,SAASrB,EAAWqB,EAA2D,CAClF,OAAOtB,EAASsB,CAAK,GAAK,OAAQA,GAAS,OAAOA,EAAM,IAAO,UACnE,CACO,SAASY,GAAQZ,EAAkD,CACtE,OAAO,OAAOA,GAAU,UACpBA,IAAU,MACV,EAAEA,aAAiB,OACnB,CAACtB,EAASsB,CAAK,GACf,CAACpB,EAAYoB,CAAK,CAC1B,CACO,SAASpB,EAAYoB,EAA0D,CAClF,OAAO,OAAOA,GAAU,UAAYA,IAAU,MAAQ,aAAcA,GAAS,OAAOA,EAAM,UAAa,UAC3G,CAEO,SAASc,EAAiBd,EAAiD,CAC9E,OAAOA,IAAU,MAAQ,OAAOA,GAAU,UAAY,cAAeA,GAAS,OAAOA,EAAM,WAAc,UAC7G,CAEO,SAASe,EAAQf,EAAwC,CAC5D,OAAOA,IAAU,MAAQ,OAAOA,GAAU,UAAY,YAAaA,GAAS,OAAOA,EAAM,SAAY,YAAc,OAAOA,EAAM,UAAa,UACjJ,CAEO,SAASgB,EAAOhB,EAAwE,CAC3F,OAAIA,EAGD,OAAOA,GAAU,SACTA,EAGJA,EAAM,OAAO,EANT,IAOf,CAEO,SAASiB,EAAiBC,EAAyB,CACtD,IAAIC,EAAS,GAeb,OAZGD,EAAU,IACTC,EAAS,IACTD,GAAW,IAGfC,GAAU,IAGPD,GAAW,QACVC,GAAU,KAAK,MAAMD,EAAU,KAAK,EAAI,IACxCA,GAAW,OAEZ,CAACA,GAAWC,EAAO,OAAS,IAI/BA,GAAU,IAGPD,GAAW,OACVC,GAAU,KAAK,MAAMD,EAAU,IAAI,EAAI,IACvCA,GAAW,MAIZA,GAAW,KACVC,GAAU,KAAK,MAAMD,EAAU,EAAE,EAAI,IACrCA,GAAW,IAIZA,EAAU,EACTC,GAAUD,EAAU,IAEhBC,EAAO,QAAU,IACrBA,GAAU,OAGPA,CACX,CC9YA,IAAAC,EAAiB,4BCkDV,IAAKC,OACRA,EAAA,MAAQ,QACRA,EAAA,IAAM,kBACNA,EAAA,IAAM,kBACNA,EAAA,IAAM,kBAJEA,OAAA,IAOAC,OACRA,EAAA,SAAW,WACXA,EAAA,UAAY,YACZA,EAAA,SAAW,WACXA,EAAA,UAAY,YACZA,EAAA,YAAc,eALNA,OAAA,IASAC,OACRA,EAAA,WAAa,aACbA,EAAA,MAAQ,QACRA,EAAA,SAAW,WACXA,EAAA,KAAO,OACPA,EAAA,QAAU,UALFA,OAAA,IA2BSC,EAArB,MAAqBC,CAAa,CACb,KACA,OASjB,YAAYC,EAAwBC,EAA+B,CAe/D,GAdA,KAAK,KAAO,CACR,KAAM,KACN,MAAO,GACP,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,KAAM,kBACN,KAAM,KACN,KAAM,KACN,YAAa,KACb,cAAe,KACf,EAAG,CAAC,CACR,EACA,KAAK,OAASA,EACV,CAAC,KAAK,OACN,MAAM,IAAI,MAAM,0BAA0B,EAE9C,GAAI,CAACD,EAAK,MACN,MAAM,IAAI,MAAM,6CAA6C,EAG7DA,EAAK,OAAS,QAAW,KAAK,KAAKA,EAAK,IAAI,EAC5CA,EAAK,QAAU,QAAW,KAAK,MAAMA,EAAK,KAAK,EAC/CA,EAAK,SAAW,QAAW,KAAK,OAAOA,EAAK,MAAM,EAClDA,EAAK,SAAW,QAAW,KAAK,OAAOA,EAAK,MAAM,EAClDA,EAAK,SAAW,QAAW,KAAK,OAAOA,EAAK,MAAM,EAClDA,EAAK,OAAS,QAAW,KAAK,KAAKA,EAAK,IAAI,EAC5CA,EAAK,OAAS,QAAW,KAAK,KAAKA,EAAK,IAAI,EAC5CA,EAAK,OAAS,QAAW,KAAK,KAAKA,EAAK,IAAI,EAC5CA,EAAK,cAAgB,QAAW,KAAK,YAAYA,EAAK,WAAW,EACjEA,EAAK,gBAAkB,QAAW,KAAK,cAAcA,EAAK,aAAa,EACvEA,EAAK,aAAa,KAAK,YAAYA,EAAK,WAAW,EACnDA,EAAK,eAAe,KAAK,cAAcA,EAAK,aAAa,EACzDA,EAAK,IAAM,QAAW,KAAK,EAAEA,EAAK,CAAC,CAC3C,CAcA,KAAKE,EAA4C,CAC7C,OAAIA,IAAS,OACF,KAAK,KAAK,MAGrB,KAAK,KAAK,KAAOA,GAAQ,KAClB,KACX,CAcA,MAAMC,EAA+B,CACjC,OAAKA,GAIL,KAAK,KAAK,MAAQA,EACX,MAJI,KAAK,KAAK,KAKzB,CAaA,OAAOC,EAA8C,CACjD,OAAIA,IAAW,OACJ,KAAK,KAAK,QAGrB,KAAK,KAAK,OAASA,GAAU,KACtB,KACX,CAcA,OAAOD,EAA6C,CAChD,OAAKA,GAIL,KAAK,KAAK,OAASA,EACZ,MAJI,KAAK,KAAK,MAKzB,CAgBA,KAAKE,EAAkD,CACnD,OAAIA,IAAS,OACF,KAAK,KAAK,MAGrB,KAAK,KAAK,KAAOC,EAAUX,EAAkBU,CAAI,EAC1C,KACX,CAcA,KAAKE,EAA8C,CAC/C,OAAIA,IAAS,OACF,KAAK,KAAK,KAEjBA,IAAS,MACT,KAAK,KAAK,KAAO,KACV,OAGX,KAAK,KAAK,KAAO,EAAQA,EAClB,KACX,CAgBA,OAAOC,EAAsE,CACzE,OAAIA,IAAW,OACJ,KAAK,KAAK,OAEhBA,GAKL,KAAK,KAAK,OAASF,EAAUV,EAAoBY,CAAM,EAChD,OALH,KAAK,KAAK,OAAS,KACZ,KAKf,CAgBA,KAAKC,EAAgE,CACjE,OAAIA,IAAS,OACF,KAAK,KAAK,KAEhBA,GAKL,KAAK,KAAK,KAAOH,EAAUT,EAAkBY,CAAI,EAC1C,OALH,KAAK,KAAK,KAAO,KACV,KAKf,CA6BA,YAAYC,EAA2F,CACnG,OAAIA,IAAgB,OACT,KAAK,KAAK,YAEhBA,GAQF,OAAOA,GAAgB,SACtB,KAAK,KAAK,YAAc,IAAIX,EACxB,CAAE,MAAOW,EAAa,GAAGC,EAAiB,cAAeD,CAAW,CAAE,EACtE,KAAK,MACT,EAEIA,aAAuBX,EAC3B,KAAK,KAAK,YAAcW,EAGxB,KAAK,KAAK,YAAc,IAAIX,EAAaW,EAAa,KAAK,MAAM,EAGrE,KAAK,KAAK,OAAS,YACZ,OArBH,KAAK,KAAK,YAAc,KACpB,KAAK,KAAK,SAAW,cACrB,KAAK,KAAK,OAAS,MAEhB,KAkBf,CAmBA,cAAcE,EAA6F,CACvG,OAAIA,IAAkB,OACX,KAAK,KAAK,eAGhBA,EAGG,OAAOA,GAAkB,SAC7B,KAAK,KAAK,cAAgB,IAAIb,EAC1B,CAAE,MAAOa,EAAe,GAAGD,EAAiB,gBAAiBC,CAAa,CAAE,EAC5E,KAAK,MACT,EAEIA,aAAyBb,EAC7B,KAAK,KAAK,cAAgBa,EAG1B,KAAK,KAAK,cAAgB,IAAIb,EAAaa,EAAe,KAAK,MAAM,EAZrE,KAAK,KAAK,cAAgB,KAevB,KACX,CAkBA,YAAaC,EAAiE,CAC1E,IAAMC,EAAID,aAAmBd,EAAec,EAAU,KAAK,OAAO,eAAeA,CAAO,EACxF,YAAK,YAAYC,CAAC,EAClBA,EAAE,cAAc,IAAI,EACbA,CACX,CAkBA,cAAeD,EAAiE,CAC5E,IAAMC,EAAID,aAAmBd,EAAec,EAAU,KAAK,OAAO,eAAeA,CAAO,EACxF,YAAK,cAAcC,CAAC,EACpBA,EAAE,YAAY,IAAI,EACXA,CACX,CA8CA,EAAGC,EAAsGC,EAAgE,CACrK,GAAGD,IAAe,OACd,OAAOE,EAA0B,KAAK,IAAI,EAG9C,GAAG,OAAOF,GAAe,UAAY,OAAOC,GAAU,SAClDC,EAA0B,KAAK,KAAMF,EAAYC,CAAK,UAElD,OAAOD,GAAe,SAC1BE,EAA0B,KAAK,KAAMF,CAAU,MAG/C,OAAM,IAAI,MAAM,sCAAsC,EAG1D,OAAO,IACX,CASA,QAA+B,CAC3B,OAAO,OAAO,OAAO,CAAC,EAAG,KAAK,KAAM,CAChC,YAAa,KAAK,KAAK,aAAa,MAAM,GAAK,KAC/C,cAAe,KAAK,KAAK,eAAe,MAAM,GAAK,KACnD,EAAG,KAAK,EAAE,CACd,CAAC,CACL,CAUA,UAAoB,CAChB,IAAIG,EAAI,WAER,GAAI,CAAC,KAAK,KAAK,MACX,MAAM,IAAI,MAAM,6CAA6C,EAIjE,OAAAA,GAAK,SAAW,KAAK,KAAK,KAGtB,KAAK,KAAK,OACVA,GAAK,WAAa,KAAK,KAAK,MAI5B,KAAK,KAAK,SACVA,GAAK,aAAe,KAAK,KAAK,QAI9B,KAAK,KAAK,OAAS,OACnBA,GAAK,SAAW,KAAK,KAAK,KAAK,SAAS,EAAE,YAAY,GAItD,KAAK,KAAK,SAAW,OACrBA,GAAK,oBAAsB,KAAK,KAAK,OAAS,KAI9C,KAAK,KAAK,cACVA,GAAK,kBAAoB,KAAK,KAAK,YAAY,MAAM,EAAI,KAIzD,KAAK,KAAK,gBACVA,GAAK,oBAAsB,KAAK,KAAK,cAAc,MAAM,EAAI,KAI7D,KAAK,KAAK,OACVA,GAAK,QAAUC,EAAO,KAAK,KAAK,KAAM,EAAI,EAAI,KAI9C,KAAK,KAAK,OAAS,KAAK,KAAK,SAC7BD,GAAK,UAAYC,EAAO,KAAK,KAAK,MAAO,EAAK,GAI/C,KAAK,KAAK,EAAE,SACXD,GAAK,IAAM,KAAK,KAAK,EAChB,IAAI,CAAC,CAACE,EAAKJ,CAAK,IAAMI,EAAI,YAAY,EAAI,IAAMD,EAAOH,EAAO,EAAK,CAAC,EACpE,KAAK,GAAG,GAGjBE,GAAK,WAAaC,EAAO,KAAK,KAAK,QAAU,KAAK,KAAK,MAAO,EAAK,EAAI;AAAA,EAEhED,CACX,CACJ,ECxlBO,IAAKG,OACRA,EAAA,QAAU,UACVA,EAAA,MAAQ,QACRA,EAAA,MAAQ,QAHAA,OAAA,IAMCC,EAAqB,CAC9B,IAAK,MACL,MAAO,OACX,EAiFqBC,EAArB,KAA+B,CACV,KACA,MASjB,YAAaC,EAAqBC,EAAkB,CAehD,GAdA,KAAK,KAAO,CACR,KAAM,UACN,QAAS,KACT,UAAW,KACX,OAAQ,KACR,SAAU,KACV,OAAQ,KACR,YAAa,KACb,QAAS,KACT,UAAW,CAAC,EACZ,EAAG,CAAC,CACR,EAEA,KAAK,MAAQA,EACT,CAACA,EACD,MAAM,IAAI,MAAM,0BAA0B,EAG1CD,EAAK,OAAS,QAAW,KAAK,KAAKA,EAAK,IAAI,EAC5C,YAAaA,GAAQA,EAAK,UAAY,QAAW,KAAK,QAAQA,EAAK,OAAO,EAC1E,kBAAmBA,GAAQA,EAAK,gBAAkB,QAAW,KAAK,cAAcA,EAAK,aAAa,EAClG,iBAAkBA,GAAQA,EAAK,eAAiB,QAAW,KAAK,aAAaA,EAAK,YAAY,EAC9FA,EAAK,QAAQ,KAAK,OAAOA,EAAK,MAAM,EACpCA,EAAK,SAAW,QAAW,KAAK,OAAOA,EAAK,MAAM,EAClDA,EAAK,cAAgB,QAAW,KAAK,YAAYA,EAAK,WAAW,EACjEA,EAAK,UAAY,QAAW,KAAK,QAAQA,EAAK,OAAO,EACrDA,EAAK,YAAc,QAAW,KAAK,UAAUA,EAAK,SAAS,EAC3DA,EAAK,IAAM,QAAW,KAAK,EAAEA,EAAK,CAAC,CAC3C,CAeA,KAAME,EAA4C,CAC9C,GAAIA,IAAS,OACT,OAAO,KAAK,KAAK,KAErB,GAAI,CAACA,GAAQ,CAAC,OAAO,KAAKL,CAAa,EAAE,SAASK,CAAI,EAClD,MAAM,IAAI,MAAM,6DAA6D,EAGjF,YAAK,KAAK,KAAOA,EACV,IACX,CAiCA,QAASC,EAAgF,CAGrF,GAAIA,IAAY,QAAa,OAAO,KAAK,KAAK,SAAY,SACtD,MAAO,GAAK,KAAK,KAAK,QAE1B,GAAIA,IAAY,OACZ,OAAO,KAAK,KAAK,QAIrB,GAAI,OAAOA,GAAY,UAAY,SAASA,CAAO,EAC/C,KAAK,KAAK,QAAU,GAAKA,MAExB,IAAG,CAACA,GAAW,OAAOA,GAAY,SACnC,MAAM,IAAI,MAAM,wEAAwE,EAGxF,KAAK,KAAK,QAAUC,EAAUD,EAAS,SAAS,EAGpD,OAAO,IACX,CAkCA,UAAUE,EAAyE,CAC/E,GAAIA,IAAc,OACd,OAAO,KAAK,KAAK,UAErB,GAAI,CAACA,EACD,YAAK,KAAK,UAAY,KACf,KAGX,GAAI,CAAC,OAAO,OAAOP,CAAkB,EAAE,SAASO,CAAS,EACrD,MAAM,IAAI,MAAM,8DAA8D,EAGlF,YAAK,KAAK,UAAYA,EACf,IACX,CAiCA,aAAcF,EAAyE,CACnF,OAAIA,IAAY,OACL,KAAK,KAAK,QAGd,KAAK,QAAQ,OAAOA,GAAY,SAAW,GAAKA,EAAUA,CAAO,CAC5E,CAmCA,cAAeA,EAAyE,CACpF,OAAGA,IAAY,OACJ,KAAK,QAAQ,EAGjB,KAAK,QAAQA,CAAO,CAC/B,CA4BA,OAAQG,EAAwE,CAC5E,GAAIA,IAAW,OACX,OAAO,KAAK,KAAK,OAErB,GAAI,CAACA,EACD,YAAK,KAAK,OAAS,KACZ,KAGX,GAAI,OAAOA,GAAW,SAClB,MAAM,IAAI,MAAM,6CAA6C,EAEjE,GAAI,OAAOA,EAAO,OAAU,UAAY,CAAC,SAASA,EAAO,KAAK,EAC1D,MAAM,IAAI,MAAM,iDAAiD,EAErE,GAAI,OAAOA,EAAO,UAAa,UAAY,CAAC,SAASA,EAAO,QAAQ,EAChE,MAAM,IAAI,MAAM,oDAAoD,EAGxE,YAAK,KAAK,OAASA,EACZ,IACX,CAkCA,OAAQC,EAAoH,CACxH,GAAIA,IAAe,OACf,OAAO,KAAK,KAAK,OAErB,GAAI,CAACA,EACD,YAAK,KAAK,OAAS,KACZ,KAGX,IAAIC,EAAU,KACd,GAAI,OAAOD,GAAe,SACtBC,EAAU,CACN,IAAKD,EACL,KAAM,IACV,UAEK,OAAOA,GAAe,SAC3BC,EAAU,CACN,IAAKD,EAAW,IAChB,KAAMA,EAAW,MAAQ,IAC7B,MAGA,OAAM,IAAI,MACN,6JAEJ,EAGJ,GAAI,CAACC,EAAQ,IACT,MAAM,IAAI,MAAM,wBAAwB,EAG5C,YAAK,KAAK,OAAS,CACf,IAAKA,EAAQ,IACb,KAAMA,EAAQ,IAClB,EACO,IACX,CAoBA,YAAaC,EAAmD,CAC5D,OAAIA,IAAgB,OACT,KAAK,KAAK,YAEhBA,GAKL,KAAK,KAAK,YAAcA,EACjB,OALH,KAAK,KAAK,YAAc,KACjB,KAKf,CAkBA,QAASC,EAA+C,CACpD,OAAIA,IAAY,OACL,KAAK,KAAK,QAEhBA,GAKL,KAAK,KAAK,QAAUA,EACb,OALH,KAAK,KAAK,QAAU,KACb,KAKf,CAUA,eAAeV,EAA8D,CACzE,GAAIA,aAAgBW,EAChB,YAAK,KAAK,UAAU,KAAKX,CAAI,EACtBA,EAEP,OAAOA,GAAS,WAChBA,EAAO,CAAE,MAAOA,EAAM,GAAGY,EAAiB,OAAQZ,CAAI,CAAE,GAG5D,IAAMa,EAAW,IAAIF,EAAaX,EAAM,IAAI,EAC5C,YAAK,KAAK,UAAU,KAAKa,CAAQ,EAC1BA,CACX,CAeA,UAAUC,EAAiF,CACvF,OAAKA,GAILA,EAAU,QAAQD,GAAY,KAAK,eAAeA,CAAQ,CAAC,EACpD,MAJI,KAAK,KAAK,SAKzB,CA+CA,EAAGE,EAAsGC,EAAgE,CACrK,GAAGD,IAAe,OACd,OAAOE,EAA0B,KAAK,IAAI,EAG9C,GAAG,OAAOF,GAAe,UAAY,OAAOC,GAAU,SAClDC,EAA0B,KAAK,KAAMF,EAAYC,CAAK,UAElD,OAAOD,GAAe,SAC1BE,EAA0B,KAAK,KAAMF,CAAU,MAG/C,OAAM,IAAI,MAAM,sCAAsC,EAG1D,OAAO,IACX,CAUA,QAA6B,CACzB,IAAMZ,EAAU,KAAK,QAAQ,EAC7B,OAAO,OAAO,OAAO,CAAC,EAAG,KAAK,KAAM,CAChC,QAAS,OAAOA,GAAY,SAAWA,EAAUe,EAAOf,CAAO,EAC/D,EAAG,KAAK,EAAE,CACd,CAAC,CACL,CAWA,UAAoB,CAChB,IAAIgB,EAAI;AAAA,EAqBR,GAlBAA,GAAK,UAAY,KAAK,KAAK,KAAK,YAAY,EAAI;AAAA,EAE5C,OAAO,KAAK,KAAK,SAAY,UAAY,KAAK,KAAK,YAAc,KAC7D,KAAK,KAAK,QAAU,EACpBA,GAAK,uBAAyBC,EAAiB,KAAK,KAAK,OAAO,EAAI;AAAA,EAGpED,GAAK,WAAaC,EAAiB,KAAK,KAAK,OAAO,EAAI;AAAA,EAGvD,OAAO,KAAK,KAAK,SAAY,SAClCD,GAAK,mBAAqB,KAAK,KAAK,WAAW,YAAY,EAAI,IAAMC,EAAiB,KAAK,KAAK,OAAO,EAAI;AAAA,EAG3GD,GAAK,2BAA6BE,EAAW,KAAK,MAAM,SAAS,EAAG,KAAK,KAAK,OAAO,EAAI;AAAA,EAIzF,KAAK,KAAK,OAAQ,CAClB,GAAI,CAAC,KAAK,KAAK,OAAO,MAClB,MAAM,IAAI,MAAM,8EAA8E,EAElG,GAAI,CAAC,KAAK,KAAK,OAAO,SAClB,MAAM,IAAI,MAAM,+EAA+E,EAGnGF,GAAK,UAAY,KAAK,KAAK,OAAO,MAAQ;AAAA,EAC1CA,GAAK,YAAcC,EAAiB,KAAK,KAAK,OAAO,QAAQ,EAAI;AAAA,CACrE,CAGA,OAAI,KAAK,KAAK,OAAS,SAAW,KAAK,KAAK,QAAU,KAAK,KAAK,OAAO,KACnED,GAAK,kBAAoBG,EAAO,KAAK,KAAK,OAAO,KAAM,EAAK,EAAI,IAAMA,EAAO,KAAK,KAAK,OAAO,IAAK,EAAK,EAAI;AAAA,EAEvG,KAAK,KAAK,OAAS,SAAW,KAAK,KAAK,OAC7CH,GAAK,oBAAsBG,EAAO,KAAK,KAAK,OAAO,IAAK,EAAK,EAAI;AAAA,EAE5D,KAAK,KAAK,OAAS,UACxBH,GAAK;AAAA,GAIL,KAAK,KAAK,OAAS,SAAW,KAAK,KAAK,YACxCA,GAAK,eAAiBG,EAAO,KAAK,KAAK,YAAa,EAAK,EAAI;AAAA,EAExD,KAAK,KAAK,OAAS,UACxBH,GAAK,eAAiBG,EAAO,KAAK,MAAM,QAAQ,EAAG,EAAK,EAAI;AAAA,GAI5D,KAAK,KAAK,OAAS,SAAW,KAAK,KAAK,QACxCH,GAAK,WAAaG,EAAO,KAAK,KAAK,QAAS,EAAK,EAAI;AAAA,EAEhD,KAAK,KAAK,OAAS,UACxBH,GAAK,WAAaG,EAAO,KAAK,MAAM,QAAQ,EAAG,EAAK,EAAI;AAAA,GAIxD,KAAK,KAAK,OAAS,SACnB,KAAK,KAAK,UAAU,QAAQT,GAAY,CACpCM,GAAKN,EAAS,SAAS,CAC3B,CAAC,EAILM,GAAKI,EAAyB,KAAK,IAAI,EAEvCJ,GAAK;AAAA,EACEA,CACX,CACJ,ECrsBA,IAAqBK,EAArB,KAAkC,CACb,KAMjB,YAAYC,EAAwB,CAKhC,GAJA,KAAK,KAAO,CACR,KAAM,EACV,EAEG,CAACA,EAAK,KACL,MAAM,IAAI,MAAM,4CAA4C,EAGhE,KAAK,KAAKA,EAAK,IAAI,CACvB,CAcA,KAAKC,EAA8B,CAC/B,OAAIA,IAAS,OACF,KAAK,KAAK,MAGrB,KAAK,KAAK,KAAOA,EACV,KACX,CASA,QAAmC,CAC/B,OAAO,OAAO,OAAO,CAAC,EAAG,KAAK,IAAI,CACtC,CAUA,UAAmB,CAGf,OAAOC,EAAO,KAAK,KAAK,KAAM,EAAK,CACvC,CACJ,ECDO,IAAKC,OACRA,EAAA,SAAW,WACXA,EAAA,SAAW,WACXA,EAAA,OAAS,SACTA,EAAA,MAAQ,QACRA,EAAA,OAAS,SACTA,EAAA,QAAU,UACVA,EAAA,OAAS,SAPDA,OAAA,IAUAC,OACRA,EAAA,GAAK,KACLA,EAAA,GAAK,KACLA,EAAA,GAAK,KACLA,EAAA,GAAK,KACLA,EAAA,GAAK,KACLA,EAAA,GAAK,KACLA,EAAA,GAAK,KAPGA,OAAA,IJ7EL,IAAKC,OACRA,EAAA,UAAY,YACZA,EAAA,UAAY,YACZA,EAAA,UAAY,YAHJA,OAAA,IAMAC,OACRA,EAAA,KAAO,OACPA,EAAA,UAAY,YACZA,EAAA,KAAO,OACPA,EAAA,IAAM,MAJEA,OAAA,IAOAC,OACRA,EAAA,YAAc,cACdA,EAAA,OAAS,SAFDA,OAAA,IAKAC,OACRA,EAAA,OAAS,SACTA,EAAA,QAAU,UACVA,EAAA,aAAe,eAHPA,OAAA,IAqHSC,EAArB,KAA+B,CACV,KACA,SASjB,YAAYC,EAAqBC,EAAwB,CAgCrD,GA/BA,KAAK,KAAO,CACR,MAAI,EAAAC,SAAK,EACT,SAAU,EACV,MAAO,IAAI,KACX,IAAK,KACL,aAAc,KACd,SAAU,KACV,MAAO,IAAI,KACX,OAAQ,GACR,SAAU,GACV,UAAW,KACX,QAAS,GACT,SAAU,KACV,YAAa,KACb,UAAW,KACX,UAAW,CAAC,EACZ,OAAQ,CAAC,EACT,WAAY,CAAC,EACb,OAAQ,KACR,WAAY,KACZ,SAAU,KACV,IAAK,KACL,YAAa,CAAC,EACd,aAAc,KACd,QAAS,KACT,aAAc,KACd,MAAO,KACP,EAAG,CAAC,CACR,EAEA,KAAK,SAAWD,EACZ,CAACA,EACD,MAAM,IAAI,MAAM,6BAA6B,EAG7CD,EAAK,IAAI,KAAK,GAAGA,EAAK,EAAE,EACxBA,EAAK,WAAa,QAAW,KAAK,SAASA,EAAK,QAAQ,EACxDA,EAAK,OAAO,KAAK,MAAMA,EAAK,KAAK,EACjCA,EAAK,MAAQ,QAAW,KAAK,IAAIA,EAAK,GAAG,EACzCA,EAAK,eAAiB,QAAW,KAAK,aAAaA,EAAK,YAAY,EACpEA,EAAK,WAAa,QAAW,KAAK,SAASA,EAAK,QAAQ,EACxDA,EAAK,QAAU,QAAW,KAAK,MAAMA,EAAK,KAAK,EAC/CA,EAAK,SAAW,QAAW,KAAK,OAAOA,EAAK,MAAM,EAClDA,EAAK,WAAa,QAAW,KAAK,SAASA,EAAK,QAAQ,EACxDA,EAAK,YAAc,QAAW,KAAK,UAAUA,EAAK,SAAS,EAC3DA,EAAK,UAAY,QAAW,KAAK,QAAQA,EAAK,OAAO,EACrDA,EAAK,WAAa,QAAW,KAAK,SAASA,EAAK,QAAQ,EACxDA,EAAK,cAAgB,QAAW,KAAK,YAAYA,EAAK,WAAW,EACjEA,EAAK,YAAc,QAAW,KAAK,UAAUA,EAAK,SAAS,EAC3DA,EAAK,YAAc,QAAW,KAAK,UAAUA,EAAK,SAAS,EAC3DA,EAAK,SAAW,QAAW,KAAK,OAAOA,EAAK,MAAM,EAClDA,EAAK,aAAe,QAAW,KAAK,WAAWA,EAAK,UAAU,EAC9DA,EAAK,SAAW,QAAW,KAAK,OAAOA,EAAK,MAAM,EAClDA,EAAK,aAAe,QAAW,KAAK,WAAWA,EAAK,UAAU,EAC9DA,EAAK,WAAa,QAAW,KAAK,SAASA,EAAK,QAAQ,EACxDA,EAAK,MAAQ,QAAW,KAAK,IAAIA,EAAK,GAAG,EACzCA,EAAK,cAAgB,QAAW,KAAK,YAAYA,EAAK,WAAW,EACjEA,EAAK,eAAiB,QAAW,KAAK,aAAaA,EAAK,YAAY,EACpEA,EAAK,UAAY,QAAW,KAAK,QAAQA,EAAK,OAAO,EACrDA,EAAK,eAAiB,QAAW,KAAK,aAAaA,EAAK,YAAY,EACpEA,EAAK,QAAU,QAAW,KAAK,MAAMA,EAAK,KAAK,EAC/CA,EAAK,IAAM,QAAW,KAAK,EAAEA,EAAK,CAAC,CAC3C,CAeA,GAAGG,EAAqC,CACpC,OAAIA,IAAO,OACA,KAAK,KAAK,IAGrB,KAAK,KAAK,GAAK,OAAOA,CAAE,EACjB,KACX,CAiBA,IAAIA,EAAqC,CACrC,OAAOA,IAAO,OAAY,KAAK,GAAG,EAAI,KAAK,GAAGA,CAAE,CACpD,CAkBA,SAASC,EAAkC,CACvC,GAAIA,IAAa,OACb,OAAO,KAAK,KAAK,SAGrB,IAAMC,EAAI,SAAS,OAAOD,CAAQ,EAAG,EAAE,EACvC,GAAI,MAAMC,CAAC,EACP,MAAM,IAAI,MAAM,8BAA8B,EAGlD,YAAK,KAAK,SAAWD,EACd,IACX,CAgDA,MAAME,EAAqD,CACvD,OAAIA,IAAU,QACV,KAAK,0BAA0B,EACxB,KAAK,KAAK,QAGrB,KAAK,KAAK,MAAQC,EAAUD,EAAO,OAAO,EACnC,KACX,CAkBA,IAAIE,EAAiE,CACjE,OAAIA,IAAQ,QACR,KAAK,0BAA0B,EACxB,KAAK,KAAK,KAEjBA,IAAQ,MACR,KAAK,KAAK,IAAM,KACT,OAGX,KAAK,KAAK,IAAMD,EAAUC,EAAK,KAAK,EAC7B,KACX,CAMQ,2BAAkC,CACtC,GAAI,KAAK,KAAK,OAAS,KAAK,KAAK,KAAOC,EAAO,KAAK,KAAK,KAAK,EAAE,QAAQ,EAAIA,EAAO,KAAK,KAAK,GAAG,EAAE,QAAQ,EAAG,CACzG,IAAM,EAAI,KAAK,KAAK,MACpB,KAAK,KAAK,MAAQ,KAAK,KAAK,IAC5B,KAAK,KAAK,IAAM,CACpB,CACJ,CAgBA,aAAaC,EAA0E,CACnF,OAAIA,IAAiB,OACV,KAAK,KAAK,aAEjBA,IAAiB,MACjB,KAAK,KAAK,aAAe,KAClB,OAGX,KAAK,KAAK,aAAeH,EAAUG,EAAc,cAAc,EACxD,KACX,CAiCA,SAASC,EAAgD,CACrD,OAAIA,IAAa,QAAa,KAAK,KAAK,WAAa,KAC1C,KAAK,KAAK,SAEjBA,IAAa,OACN,KAAK,SAAS,SAAS,GAGlC,KAAK,KAAK,SAAWA,GAAYA,IAAa,MAAQA,EAAS,SAAS,EAAI,KACxE,KAAK,KAAK,WACV,KAAK,KAAK,SAAW,IAGlB,KACX,CAgBA,MAAMC,EAAqD,CACvD,OAAIA,IAAU,OACH,KAAK,KAAK,OAGrB,KAAK,KAAK,MAAQL,EAAUK,EAAO,OAAO,EACnC,KACX,CAkBA,UAAUA,EAAqD,CAC3D,OAAIA,IAAU,OACH,KAAK,MAAM,EAGf,KAAK,MAAMA,CAAK,CAC3B,CAgDA,OAAOC,EAAkC,CACrC,OAAIA,IAAW,OACJ,KAAK,KAAK,QAGrB,KAAK,KAAK,OAAS,EAAQA,EACpB,KACX,CA4CA,SAASC,EAAoC,CACzC,OAAIA,IAAa,OACN,KAAK,KAAK,UAGrB,KAAK,KAAK,SAAW,EAAQA,EACzB,KAAK,KAAK,WACV,KAAK,KAAK,SAAW,MAGlB,KACX,CAuHA,UAAUC,EAAqI,CAC3I,GAAIA,IAAc,OACd,OAAO,KAAK,KAAK,UAErB,GAAI,CAACA,EACD,YAAK,KAAK,UAAY,KACf,KAEX,GAAGC,EAAQD,CAAS,GAAK,OAAOA,GAAc,SAC1C,YAAK,KAAK,UAAYA,EACf,KAOX,GAJA,KAAK,KAAK,UAAY,CAClB,KAAME,EAAUC,EAAwBH,EAAU,IAAI,CAC1D,EAEIA,EAAU,MAAO,CACjB,GAAI,CAAC,SAASA,EAAU,KAAK,EACzB,MAAM,IAAI,MAAM,4CAA4C,EAGhE,KAAK,KAAK,UAAU,MAAQA,EAAU,KAC1C,CAEA,GAAIA,EAAU,SAAU,CACpB,GAAI,CAAC,SAASA,EAAU,QAAQ,EAC5B,MAAM,IAAI,MAAM,+CAA+C,EAGnE,KAAK,KAAK,UAAU,SAAWA,EAAU,QAC7C,CAMA,GAJIA,EAAU,QAAU,SACpB,KAAK,KAAK,UAAU,MAAQR,EAAUQ,EAAU,MAAO,iBAAiB,GAGxEA,EAAU,MAAO,CACjB,IAAMI,EAAa,MAAM,QAAQJ,EAAU,KAAK,EAAIA,EAAU,MAAQ,CAACA,EAAU,KAAK,EACtF,KAAK,KAAK,UAAU,MAAQI,EAAW,IAAIC,GAAOH,EAAUI,EAAaD,CAAG,CAAgB,CAChG,CAEA,GAAIL,EAAU,QAAS,CACnB,IAAMO,EAAe,MAAM,QAAQP,EAAU,OAAO,EAAIA,EAAU,QAAU,CAACA,EAAU,OAAO,EAC9F,KAAK,KAAK,UAAU,QAAUO,EAAa,IAAIC,GAAS,CACpD,GAAI,OAAOA,GAAU,UAAYA,EAAQ,GAAKA,EAAQ,GAClD,MAAM,IAAI,MAAM,+CAAiDA,EAAQ,IAAI,EAGjF,OAAOA,CACX,CAAC,CACL,CAEA,GAAIR,EAAU,WAAY,CACtB,IAAMS,EAAkB,MAAM,QAAQT,EAAU,UAAU,EAAIA,EAAU,WAAa,CAACA,EAAU,UAAU,EAG1G,KAAK,KAAK,UAAU,WAAaS,EAAgB,IAAIC,GAAY,CAC7D,GAAI,OAAOA,GAAa,UAAYA,EAAW,KAAOA,EAAW,IAAMA,IAAa,EAChF,MAAM,IAAI,MAAM,kDAAoDA,EAAW,IAAI,EAGvF,OAAOA,CACX,CAAC,CACL,CAEA,GAAIV,EAAU,SAAU,CACpB,GAAI,CAAC,KAAK,KAAK,UAAU,MACrB,KAAM,kEAEV,IAAMW,EAAgB,MAAM,QAAQX,EAAU,QAAQ,EAAIA,EAAU,SAAW,CAACA,EAAU,QAAQ,EAClG,KAAK,KAAK,UAAU,SAAWW,EAAc,IAAIC,GAAY,CACzD,GAAI,OAAOA,GAAa,UAAYA,EAAW,MAAQA,EAAW,KAAOA,IAAa,EAClF,KAAM,gDAAkDA,EAAW,KAEvE,OAAOA,CACX,CAAC,CACL,CAEA,GAAIZ,EAAU,QAAS,CACnB,IAAMa,EAAe,MAAM,QAAQb,EAAU,OAAO,EAAIA,EAAU,QAAU,CAACA,EAAU,OAAO,EAC9F,KAAK,KAAK,UAAU,QAAUa,EAAa,IAAI,CAACC,EAAS,IAC9CtB,EAAUsB,EAAS,qBAAqB,CAAC,GAAG,CACtD,CACL,CAEA,OAAId,EAAU,cACV,KAAK,KAAK,UAAU,YAAcE,EAAUI,EAAaN,EAAU,WAAW,GAG3E,IACX,CAeA,QAAQe,EAAiC,CACrC,OAAIA,IAAY,OACL,KAAK,KAAK,SAGrB,KAAK,KAAK,QAAUA,EAAU,OAAOA,CAAO,EAAI,GACzC,KACX,CAsDA,SAASC,EAAqE,CAC1E,GAAIA,IAAa,OACb,OAAO,KAAK,KAAK,SAErB,GAAI,OAAOA,GAAa,SACpB,YAAK,KAAK,SAAW,CACjB,MAAOA,CACX,EACO,KAEX,GAAIA,IACC,UAAWA,GAAY,CAACA,EAAS,OACjCA,GAAU,MAAQ,CAAC,SAASA,EAAS,IAAI,GAAG,GAAK,CAAC,SAASA,EAAS,IAAI,GAAG,IAC3E,EAAE,UAAWA,IAAa,CAACA,GAAU,KAEtC,MAAM,IAAI,MACN,wIAEJ,EAGJ,YAAK,KAAK,SAAWA,GAAY,KAC1B,IACX,CA8BA,YAAYC,EAA8E,CACtF,OAAIA,IAAgB,OACT,KAAK,KAAK,YAEjBA,IAAgB,MAChB,KAAK,KAAK,YAAc,KACjB,OAGP,OAAOA,GAAgB,SACvB,KAAK,KAAK,YAAc,CAAC,MAAOA,CAAW,EAG3C,KAAK,KAAK,YAAcA,EAErB,KACX,CAqCA,UAAUC,EAAwE,CAC9E,OAAIA,IAAc,OACP,KAAK,KAAK,UAEjBA,IAAc,MACd,KAAK,KAAK,UAAY,KACf,OAGX,KAAK,KAAK,UAAYC,EAAiB,YAAaD,CAAS,EACtD,KACX,CAiDA,eAAejC,EAA8D,CACzE,GAAIA,aAAgBmC,EAChB,YAAK,KAAK,UAAU,KAAKnC,CAAI,EACtBA,EAEP,OAAOA,GAAS,WAChBA,EAAO,CAAE,MAAOA,EAAM,GAAGkC,EAAiB,OAAQlC,CAAI,CAAE,GAG5D,IAAMoC,EAAW,IAAID,EAAanC,EAAM,IAAI,EAC5C,YAAK,KAAK,UAAU,KAAKoC,CAAQ,EAC1BA,CACX,CA0BA,UAAUC,EAAiF,CACvF,OAAKA,GAILA,EAAU,QAAQD,GAAY,KAAK,eAAeA,CAAQ,CAAC,EACpD,MAJI,KAAK,KAAK,SAKzB,CAsBA,YAAYpC,EAA4C,CACpD,IAAMsC,EAAQtC,aAAgBuC,EAAYvC,EAAO,IAAIuC,EAAUvC,EAAM,IAAI,EACzE,YAAK,KAAK,OAAO,KAAKsC,CAAK,EACpBA,CACX,CA0BA,OAAOE,EAA4D,CAC/D,OAAKA,GAILA,EAAO,QAASF,GAAqC,KAAK,YAAYA,CAAK,CAAC,EACrE,MAJI,KAAK,KAAK,MAKzB,CAoBA,eAAetC,EAAqD,CAChE,IAAMyC,EAAWzC,aAAgB0C,EAAe1C,EAAO,IAAI0C,EAAa1C,CAAI,EAC5E,YAAK,KAAK,WAAW,KAAKyC,CAAQ,EAC3BA,CACX,CA0BA,WAAWE,EAAyE,CAChF,OAAKA,GAILA,EAAW,QAAQF,GAAY,KAAK,eAAeA,CAAQ,CAAC,EACrD,MAJI,KAAK,KAAK,UAKzB,CAoBA,OAAOG,EAAgE,CACnE,OAAIA,IAAW,OACJ,KAAK,KAAK,OAEjBA,IAAW,MACX,KAAK,KAAK,OAAS,KACZ,OAGX,KAAK,KAAK,OAAS3B,EAAUtB,EAAiBiD,CAAM,EAC7C,KACX,CAsBA,WAAWC,EAA4E,CACnF,OAAIA,IAAe,OACR,KAAK,KAAK,WAEjBA,IAAe,MACf,KAAK,KAAK,WAAa,KAChB,OAGX,KAAK,KAAK,WAAa5B,EAAUrB,EAAqBiD,CAAU,EACzD,KACX,CAoBA,SAASC,EAAgD,CACrD,GAAIA,IAAa,OACb,OAAO,KAAK,KAAK,SAErB,GAAIA,IAAa,KACb,YAAK,KAAK,SAAW,KACd,KAGX,GAAGA,EAAW,GAAKA,EAAW,EAC1B,MAAM,IAAI,MAAM,6DAAmD,EAGvE,YAAK,KAAK,SAAW,KAAK,MAAMA,CAAQ,EACjC,IACX,CAcA,IAAIC,EAA2C,CAC3C,OAAIA,IAAQ,OACD,KAAK,KAAK,KAGrB,KAAK,KAAK,IAAMA,EAAM,OAAOA,CAAG,EAAI,KAC7B,KACX,CAiBA,iBAAiBA,EAAmB,CAChC,YAAK,KAAK,YAAY,KAAKA,CAAG,EACvB,IACX,CA0BA,YAAYC,EAAyC,CACjD,OAAKA,GAILA,EAAY,QAASC,GAAuB,KAAK,iBAAiBA,CAAU,CAAC,EACtE,MAJI,KAAK,KAAK,WAKzB,CAyBA,aAAaC,EAAkF,CAC3F,OAAIA,IAAiB,OACV,KAAK,KAAK,aAEhBA,GAKL,KAAK,KAAK,aAAejC,EAAUpB,EAAuBqD,CAAY,EAC/D,OALH,KAAK,KAAK,aAAe,KAClB,KAKf,CAcA,QAAQC,EAAqE,CACzE,OAAIA,IAAY,OACL,KAAK,KAAK,QAEjBA,IAAY,MACZ,KAAK,KAAK,QAAU,KACb,OAGX,KAAK,KAAK,QAAU5C,EAAU4C,EAAS,SAAS,EACzC,KACX,CAcA,aAAaC,EAA0E,CACnF,OAAIA,IAAiB,OACV,KAAK,KAAK,aAEjBA,IAAiB,MACjB,KAAK,KAAK,aAAe,KAClB,OAGX,KAAK,KAAK,aAAe7C,EAAU6C,EAAc,cAAc,EACxD,KACX,CAmBA,MAAMC,EAA8D,CAChE,OAAIA,IAAW,OACJ,KAAK,KAAK,MAEjBA,IAAW,MACX,KAAK,KAAK,MAAQ,KACX,OAGX,KAAK,KAAK,MAAQpC,EAAUnB,EAAgBuD,CAAM,EAC3C,KACX,CA+CA,EAAEC,EAAwGC,EAAkE,CACxK,OAAID,IAAe,OACRE,EAAyB,KAAK,IAAI,GAGzC,OAAOF,GAAe,UAAY,OAAOC,GAAU,UACnDC,EAAyB,KAAK,KAAMF,EAAYC,CAAK,EAErD,OAAOD,GAAe,UACtBE,EAAyB,KAAK,KAAMF,CAAU,EAG3C,KACX,CAkBA,QAA4B,CACxB,IAAIvC,EAAwD,KAC5D,OAAGC,EAAQ,KAAK,KAAK,SAAS,GAAK,OAAO,KAAK,KAAK,WAAc,SAC9DD,EAAY,KAAK,KAAK,UAAU,SAAS,EAErC,KAAK,KAAK,YACdA,EAAY,OAAO,OAAO,CAAC,EAAG,KAAK,KAAK,UAAW,CAC/C,MAAO0C,EAAO,KAAK,KAAK,UAAU,KAAK,GAAK,OAC5C,QAAS,KAAK,KAAK,UAAU,SAAS,IAAIC,GAAKD,EAAOC,CAAC,CAAC,CAC5D,CAAC,GAGL,KAAK,0BAA0B,EACxB,OAAO,OAAO,CAAC,EAAG,KAAK,KAAM,CAChC,MAAOD,EAAO,KAAK,KAAK,KAAK,GAAK,KAClC,IAAKA,EAAO,KAAK,KAAK,GAAG,GAAK,KAC9B,aAAcA,EAAO,KAAK,KAAK,YAAY,GAAK,KAChD,MAAOA,EAAO,KAAK,KAAK,KAAK,GAAK,KAClC,QAASA,EAAO,KAAK,KAAK,OAAO,GAAK,KACtC,aAAcA,EAAO,KAAK,KAAK,YAAY,GAAK,KAChD,UAAA1C,EACA,EAAG,KAAK,EAAE,CACd,CAAC,CACL,CAWA,UAAmB,CACf,IAAI4C,EAAI,GA4BR,GAzBAA,GAAK;AAAA,EACLA,GAAK,OAAS,KAAK,KAAK,GAAK;AAAA,EAG7BA,GAAK,YAAc,KAAK,KAAK,SAAW;AAAA,EAExC,KAAK,0BAA0B,EAC/BA,GAAK,WAAaC,EAAW,KAAK,SAAS,SAAS,EAAG,KAAK,KAAK,KAAK,EAAI;AAAA,EACtE,KAAK,KAAK,QACVD,GAAK,sBAAwBC,EAAW,KAAK,SAAS,EAAG,KAAK,KAAK,MAAO,EAAI,EAAI;AAAA,EAC9E,KAAK,KAAK,MACVD,GAAK,oBAAsBC,EAAW,KAAK,SAAS,EAAG,KAAK,KAAK,IAAK,EAAI,EAAI;AAAA,GAGlFD,GAAK;AAAA,EACLA,GAAK;AAAA,IAGLA,GAAKE,EAAa,KAAK,SAAS,EAAG,UAAW,KAAK,KAAK,MAAO,KAAK,IAAI,EAAI;AAAA,EACxE,KAAK,KAAK,MACVF,GAAKE,EAAa,KAAK,SAAS,EAAG,QAAS,KAAK,KAAK,IAAK,KAAK,IAAI,EAAI;AAAA,IAK7E7C,EAAQ,KAAK,KAAK,SAAS,GAAK,OAAO,KAAK,KAAK,WAAc,SAAU,CACxE,IAAID,EAAY,KAAK,KAAK,UACrB,SAAS,EACT,QAAQ,QAAS;AAAA,CAAI,EACrB,MAAM;AAAA,CAAI,EACV,OAAO+C,GAAKA,GAAK,CAACA,EAAE,WAAW,UAAU,CAAC,EAC1C,KAAK;AAAA,CAAM,EAEb,CAAC/C,EAAU,SAAS;AAAA,CAAM,GAAK,CAACA,EAAU,WAAW,QAAQ,IAC5DA,EAAY,SAAWA,GAG3B4C,GAAK5C,EAAU,KAAK,EAAI;AAAA,CAC5B,MACS,KAAK,KAAK,YACf4C,GAAK,cAAgB,KAAK,KAAK,UAAU,KAErC,KAAK,KAAK,UAAU,QACpBA,GAAK,UAAY,KAAK,KAAK,UAAU,OAGrC,KAAK,KAAK,UAAU,WACpBA,GAAK,aAAe,KAAK,KAAK,UAAU,UAGxC,KAAK,KAAK,UAAU,QACpBA,GAAK,UAAYC,EAAW,KAAK,SAAS,SAAS,EAAG,KAAK,KAAK,UAAU,MAAO,GAAO,KAAK,SAAS,CAAC,GAGvG,KAAK,KAAK,UAAU,QACpBD,GAAK,UAAY,KAAK,KAAK,UAAU,MAAM,KAAK,GAAG,GAGnD,KAAK,KAAK,UAAU,UACpBA,GAAK,YAAc,KAAK,KAAK,UAAU,QAAQ,KAAK,GAAG,GAGvD,KAAK,KAAK,UAAU,aACpBA,GAAK,eAAiB,KAAK,KAAK,UAAU,WAAW,KAAK,GAAG,GAG7D,KAAK,KAAK,UAAU,WACpBA,GAAK,aAAe,KAAK,KAAK,UAAU,SAAS,KAAK,GAAG,GAGzD,KAAK,KAAK,UAAU,cACpBA,GAAK,SAAW,KAAK,KAAK,UAAU,aAGxCA,GAAK;AAAA,EAGD,KAAK,KAAK,UAAU,UAChB,KAAK,KAAK,OACVA,GAAK,qBAAuB,KAAK,KAAK,UAAU,QAAQ,IAAII,GACjDH,EAAW,KAAK,SAAS,SAAS,EAAGG,EAAc,EAAI,CACjE,EAAE,KAAK,GAAG,EAAI;AAAA,GAGfJ,GAAK,SACD,KAAK,SAAS,EACdA,GAAK,SAAW,KAAK,SAAS,EAAI,IAAM,KAAK,KAAK,UAAU,QAAQ,IAAII,GAG7DH,EAAW,KAAK,SAAS,EAAGG,EAAc,GAAO,EAAI,CAC/D,EAAE,KAAK,GAAG,EAAI;AAAA,EAGfJ,GAAK,IAAM,KAAK,KAAK,UAAU,QAAQ,IAAII,GAChCH,EAAW,KAAK,SAAS,EAAGG,EAAc,GAAO,KAAK,SAAS,CAAC,CAC1E,EAAE,KAAK,GAAG,EAAI;AAAA,KAO/B,OAAI,KAAK,KAAK,eACVJ,GAAKE,EAAa,KAAK,SAAS,EAAG,gBAAiB,KAAK,KAAK,aAAc,KAAK,IAAI,EAAI;AAAA,GAI7FF,GAAK,WAAaK,EAAO,KAAK,KAAK,QAAS,EAAK,EAAI;AAAA,EAGjD,KAAK,KAAK,eACVL,GAAK,UAAYK,EAAO,KAAK,KAAK,aAAc,EAAK,EAAI;AAAA,GAIzD,KAAK,KAAK,UAAY,UAAW,KAAK,KAAK,UAAY,KAAK,KAAK,SAAS,QAC1EL,GAAK,YAAcK,EACf,KAAK,KAAK,SAAS,OAClB,KAAK,KAAK,SAAS,QAAU;AAAA,EAAO,KAAK,KAAK,SAAS,QAAU,IAClE,EACJ,EAAI;AAAA,EAEA,KAAK,KAAK,SAAS,QAAU,KAAK,KAAK,SAAS,MAChDL,GAAK,0CACA,KAAK,KAAK,SAAS,QAAU,aAAeK,EAAO,KAAK,KAAK,SAAS,QAAS,EAAK,EAAI,IAAM,IAC/F,kBAAoBA,EAAO,KAAK,KAAK,SAAS,OAAQ,EAAK,EAAI,YAClDA,EAAO,KAAK,KAAK,SAAS,MAAO,EAAK,EACnD,QAAUA,EAAO,KAAK,KAAK,SAAS,KAAK,IAAK,EAAK,EAAI,IACvDA,EAAO,KAAK,KAAK,SAAS,KAAK,IAAK,EAAK,EAAI;AAAA,IAKrD,KAAK,KAAK,UAAY,QAAS,KAAK,KAAK,UAAY,KAAK,KAAK,SAAS,MACxEL,GAAK,OAASK,EAAO,KAAK,KAAK,SAAS,KAAK,IAAK,EAAK,EAAI,IACvDA,EAAO,KAAK,KAAK,SAAS,KAAK,IAAK,EAAK,EAAI;AAAA,GAIjD,KAAK,KAAK,cACVL,GAAK,eAAiBK,EAAO,KAAK,KAAK,YAAY,MAAO,EAAK,EAAI;AAAA,EAG/D,KAAK,KAAK,YAAY,OACtBL,GAAK,gCAAkCK,EAAO,KAAK,KAAK,YAAY,KAAM,EAAK,EAAI;AAAA,IAKvF,KAAK,KAAK,YACVL,GAAK,iBAAmBK,EAAO,KAAK,KAAK,UAAU,KAAM,EAAI,EAAI,IAE7D,KAAK,KAAK,UAAU,SACpBL,GAAK,oBAAsBK,EAAO,KAAK,KAAK,UAAU,OAAQ,EAAI,EAAI,KAEtE,KAAK,KAAK,UAAU,OAAS,KAAK,KAAK,UAAU,SACjDL,GAAK,UAAYK,EAAO,KAAK,KAAK,UAAU,MAAO,EAAK,GAEzD,KAAK,KAAK,UAAU,QACnBL,GAAK,WAAaK,EAAO,KAAK,KAAK,UAAU,QAAU,KAAK,KAAK,UAAU,MAAO,EAAK,GAE3FL,GAAK;AAAA,GAIT,KAAK,KAAK,UAAU,QAAQ,SAAUvB,EAAU,CAC5CuB,GAAKvB,EAAS,SAAS,CAC3B,CAAC,EAGD,KAAK,KAAK,OAAO,QAAQ,SAAUE,EAAO,CACtCqB,GAAKrB,EAAM,SAAS,CACxB,CAAC,EAGG,KAAK,KAAK,WAAW,OAAS,IAC9BqB,GAAK,cAAgB,KAAK,KAAK,WAC1B,IAAIlB,GAAYA,EAAS,SAAS,CAAC,EACnC,KAAK,EAAI;AAAA,GAId,KAAK,KAAK,MACVkB,GAAK,iBAAmBK,EAAO,KAAK,KAAK,IAAK,EAAK,EAAI;AAAA,GAIvD,KAAK,KAAK,YAAY,OAAS,GAC/B,KAAK,KAAK,YAAY,QAAQjB,GAAO,CACjCY,GAAK,UAAYK,EAAOjB,EAAK,EAAK,EAAI;AAAA,CAC1C,CAAC,EAID,KAAK,KAAK,SACVY,GAAK,UAAY,KAAK,KAAK,OAAO,YAAY,EAAI;AAAA,GAIlD,KAAK,KAAK,aACVA,GAAK,8BAAgC,KAAK,KAAK,WAAW,YAAY,EAAI;AAAA,GAI1E,KAAK,KAAK,WAAa,OACvBA,GAAK,YAAc,KAAK,KAAK,SAAW;AAAA,GAI5CA,GAAKM,EAAyB,KAAK,IAAI,EAGnC,KAAK,KAAK,UACVN,GAAK,WAAaC,EAAW,KAAK,SAAS,SAAS,EAAG,KAAK,KAAK,OAAO,EAAI;AAAA,GAI5E,KAAK,KAAK,eACVD,GAAK,iBAAmBC,EAAW,KAAK,SAAS,SAAS,EAAG,KAAK,KAAK,YAAY,EAAI;AAAA,GAGvF,KAAK,KAAK,QACVD,GAAI,SAAW,KAAK,KAAK,MAAM,YAAY,EAAI;AAAA,GAGnDA,GAAK;AAAA,EACEA,CACX,CACJ,EKjyDO,IAAKO,OACRA,EAAA,QAAU,UACVA,EAAA,QAAU,UACVA,EAAA,MAAQ,QACRA,EAAA,IAAM,MACNA,EAAA,OAAS,SACTA,EAAA,QAAU,UACVA,EAAA,QAAU,UACVA,EAAA,eAAiB,iBARTA,OAAA,IAyBSC,EAArB,KAAkC,CACb,KAoCjB,YAAYC,EAAyB,CAAC,EAAG,CACrC,KAAK,KAAO,CACR,OAAQ,kCACR,OAAQ,KACR,KAAM,KACN,YAAa,KACb,SAAU,KACV,OAAQ,KACR,IAAK,KACL,MAAO,KACP,IAAK,KACL,OAAQ,CAAC,EACT,EAAG,CAAC,CACR,EAEIA,EAAK,SAAW,QAAW,KAAK,OAAOA,EAAK,MAAM,EAClDA,EAAK,SAAW,QAAW,KAAK,OAAOA,EAAK,MAAM,EAClDA,EAAK,OAAS,QAAW,KAAK,KAAKA,EAAK,IAAI,EAC5CA,EAAK,cAAgB,QAAW,KAAK,YAAYA,EAAK,WAAW,EACjEA,EAAK,WAAa,QAAW,KAAK,SAASA,EAAK,QAAQ,EACxDA,EAAK,SAAW,QAAW,KAAK,OAAOA,EAAK,MAAM,EAClDA,EAAK,MAAQ,QAAW,KAAK,IAAIA,EAAK,GAAG,EACzCA,EAAK,QAAU,QAAW,KAAK,MAAMA,EAAK,KAAK,EAC/CA,EAAK,MAAQ,QAAW,KAAK,IAAIA,EAAK,GAAG,EACzCA,EAAK,SAAW,QAAW,KAAK,OAAOA,EAAK,MAAM,EAClDA,EAAK,IAAM,QAAW,KAAK,EAAEA,EAAK,CAAC,CAC3C,CA+BA,OAAOC,EAAyD,CAC5D,GAAI,CAACA,EACD,OAAO,KAAK,KAAK,OAGrB,GAAI,OAAOA,GAAW,SAClB,YAAK,KAAK,OAASA,EACZ,KAGX,GAAI,OAAOA,GAAW,SAClB,MAAM,IAAI,MAAM,6CAA6C,EAGjE,GAAI,CAACA,EAAO,QACR,MAAM,IAAI,MAAM,uCAAuC,EAE3D,GAAI,CAACA,EAAO,QACR,MAAM,IAAI,MAAM,uCAAuC,EAG3D,IAAMC,GAAYD,EAAO,UAAY,MAAM,YAAY,EACvD,YAAK,KAAK,OAAS,KAAOA,EAAO,QAAU,KAAOA,EAAO,QAAU,KAAOC,EACnE,IACX,CA0BA,OAAOC,EAAsE,CACzE,OAAIA,IAAW,OACJ,KAAK,KAAK,OAEhBA,GAKL,KAAK,KAAK,OAASC,EAAUN,EAAoBK,CAAM,EAChD,OALH,KAAK,KAAK,OAAS,KACZ,KAKf,CAkCA,KAAKE,EAA4C,CAC7C,OAAIA,IAAS,OACF,KAAK,KAAK,MAGrB,KAAK,KAAK,KAAOA,EAAO,OAAOA,CAAI,EAAI,KAChC,KACX,CAcA,YAAYC,EAAmD,CAC3D,OAAIA,IAAgB,OACT,KAAK,KAAK,aAGrB,KAAK,KAAK,YAAcA,EAAc,OAAOA,CAAW,EAAI,KACrD,KACX,CA4DA,SAASC,EAA+D,CACpE,OAAIA,IAAa,OACN,KAAK,KAAK,UAAU,MAAQ,MAGpCA,IAAa,MACZ,KAAK,KAAK,SAAW,KAEjB,OAAOA,GAAa,SACxB,KAAK,KAAK,SAAW,CAAC,KAAMA,CAAQ,EAEhCA,IAAa,KACjB,KAAK,KAAK,SAAW,KAGrB,KAAK,KAAK,SAAWA,EAGlB,KACX,CAwBA,OAAOC,EAA8C,CACjD,OAAIA,IAAW,OACJ,KAAK,KAAK,QAGrB,KAAK,KAAK,OAASA,GAAU,KACtB,KACX,CAmBA,IAAIC,EAA2C,CAC3C,OAAIA,IAAQ,OACD,KAAK,KAAK,KAGrB,KAAK,KAAK,IAAMA,GAAO,KAChB,KACX,CAyBA,MAAMC,EAA6C,CAC/C,OAAIA,IAAU,OACH,KAAK,KAAK,OAGjBA,IAAU,KACV,KAAK,KAAK,MAAQ,KAGlB,KAAK,KAAK,MAAQA,EAAM,YAAY,EAGjC,KACX,CAwBA,IAAIC,EAAoE,CACpE,OAAIA,IAAQ,OACD,KAAK,KAAK,KAGjBC,EAAiBD,CAAG,EACpB,KAAK,KAAK,IAAMA,EAAI,UAAU,EAEzBA,GAAOA,EAAM,EAClB,KAAK,KAAK,IAAMA,EAGhB,KAAK,KAAK,IAAM,KAGb,KACX,CAsBA,YAAYX,EAA4C,CACpD,IAAMa,EAAQb,aAAgBc,EAAYd,EAAO,IAAIc,EAAUd,EAAM,IAAI,EACzE,YAAK,KAAK,OAAO,KAAKa,CAAK,EACpBA,CACX,CAgDA,OAAOE,EAA4D,CAC/D,OAAKA,GAILA,EAAO,QAAS,GAAiC,KAAK,YAAY,CAAC,CAAC,EAC7D,MAJI,KAAK,KAAK,MAKzB,CASA,OAAc,CACV,YAAK,KAAK,OAAS,CAAC,EACb,IACX,CA+DA,EAAGC,EAAoGC,EAAgE,CACnK,GAAGD,IAAe,OACd,OAAOE,EAA0B,KAAK,IAAI,EAG9C,GAAG,OAAOF,GAAe,UAAY,OAAOC,GAAU,SAClDC,EAA0B,KAAK,KAAMF,EAAYC,CAAK,UAElD,OAAOD,GAAe,SAC1BE,EAA0B,KAAK,KAAMF,CAAU,MAG/C,OAAM,IAAI,MAAM,sCAAsC,EAG1D,OAAO,IACX,CAkBA,QAA+B,CAC3B,OAAO,OAAO,OAAO,CAAC,EAAG,KAAK,KAAM,CAChC,SAAU,KAAK,SAAS,EACxB,OAAQ,KAAK,KAAK,OAAO,IAAIH,GAASA,EAAM,OAAO,CAAC,EACpD,EAAG,KAAK,EAAE,CACd,CAAC,CACL,CAMA,QAAiB,CACb,OAAO,KAAK,KAAK,OAAO,MAC5B,CAWA,UAAmB,CACf,IAAIM,EAAI,GAGR,OAAAA,EAAI;AAAA;AAAA,EAGJA,GAAK,WAAa,KAAK,KAAK,OAAS;AAAA,EAGjC,KAAK,KAAK,MACVA,GAAK,OAAS,KAAK,KAAK,IAAM;AAAA,GAI9B,KAAK,KAAK,SACVA,GAAK,oBAAsB,KAAK,KAAK,OAAS;AAAA,GAI9C,KAAK,KAAK,QACVA,GAAK,YAAc,KAAK,KAAK,MAAQ;AAAA,GAIrC,KAAK,KAAK,SACVA,GAAK,UAAY,KAAK,KAAK,OAAS;AAAA,GAIpC,KAAK,KAAK,OACVA,GAAK,QAAU,KAAK,KAAK,KAAO;AAAA,EAChCA,GAAK,gBAAkB,KAAK,KAAK,KAAO;AAAA,GAIxC,KAAK,KAAK,cACVA,GAAK,gBAAkB,KAAK,KAAK,YAAc;AAAA,GAIhD,KAAK,KAAK,UAAU,WACD,CAAC,GAAG,IAAI,IAAI,CAC1B,KAAK,SAAS,EACd,GAAG,KAAK,KAAK,OAAO,IAAIN,GAASA,EAAM,SAAS,CAAC,CACrD,CAAC,CAAC,EAAE,OAAOO,GAAMA,IAAO,MAAQ,CAACA,EAAG,WAAW,GAAG,CAAC,EAEzC,QAAQA,GAAM,CACpB,GAAG,CAAC,KAAK,KAAK,UAAU,UACpB,OAGJ,IAAMC,EAAI,KAAK,KAAK,SAAS,UAAUD,CAAE,EACrCC,IAIJF,GAAKE,EAAE,QAAQ,QAAS;AAAA,CAAI,EACvB,QAAQ,MAAO;AAAA,CAAM,EACrB,KAAK,EAAI;AAAA,EAClB,CAAC,EAED,KAAK,KAAK,UAAU,OACpBF,GAAK,eAAiB,KAAK,KAAK,SAAS,KAAO;AAAA,EAChDA,GAAK,iBAAmB,KAAK,KAAK,SAAS,KAAO;AAAA,GAIlD,KAAK,KAAK,MACVA,GAAK,mCAAqCG,EAAiB,KAAK,KAAK,GAAG,EAAI;AAAA,EAC5EH,GAAK,mBAAqBG,EAAiB,KAAK,KAAK,GAAG,EAAI;AAAA,GAIhE,KAAK,KAAK,OAAO,QAAQT,GAASM,GAAKN,EAAM,SAAS,CAAC,EAGvDM,GAAKI,EAAyB,KAAK,IAAI,EAEvCJ,GAAK,gBAEEK,EAAUL,CAAC,CACtB,CACJ,EPtwBA,SAASM,GAAKC,EAAuC,CACjD,OAAO,IAAIC,EAAaD,CAAI,CAChC,CAEA,IAAOE,GAAQH", "names": ["src_exports", "__export", "ICalAlarm", "ICalAlarmRelatesTo", "ICalAlarmType", "ICalAttendee", "ICalAttendeeRole", "ICalAttendeeStatus", "ICalAttendeeType", "ICalCalendar", "ICalCalendarMethod", "ICalCategory", "ICalEvent", "ICalEventBusyStatus", "ICalEventClass", "ICalEventRepeatingFreq", "ICalEventStatus", "ICalEventTransparency", "ICalWeekday", "src_default", "escape", "foldLines", "formatDate", "formatDateTZ", "__toCommonJS", "formatDate", "timezone", "d", "dateonly", "floating", "m", "isMoment", "isMomentTZ", "isLuxonDate", "formatDateTZ", "property", "date", "eventData", "tzParam", "escape", "str", "inQuotes", "match", "foldLines", "input", "line", "result", "c", "ch", "charsize", "addOrGetCustomAttributes", "data", "key<PERSON>r<PERSON><PERSON><PERSON>", "value", "o", "key", "a", "generateCustomAttributes", "checkNameAndMail", "attribute", "checkEnum", "type", "<PERSON><PERSON><PERSON><PERSON>", "valueStr", "checkDate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toDate", "isMomentDuration", "isRRule", "toJSON", "toDurationString", "seconds", "string", "import_uuid_random", "ICalAttendeeRole", "ICalAttendeeStatus", "ICalAttendeeType", "ICalAttendee", "_ICalAttendee", "data", "parent", "name", "email", "mailto", "role", "checkEnum", "rsvp", "status", "type", "delegatedTo", "checkNameAndMail", "delegatedFrom", "options", "a", "key<PERSON>r<PERSON><PERSON><PERSON>", "value", "addOrGetCustomAttributes", "g", "escape", "key", "ICalAlarmType", "ICalAlarmRelatesTo", "ICalAlarm", "data", "event", "type", "trigger", "checkDate", "relatesTo", "repeat", "attachment", "_attach", "description", "summary", "ICalAttendee", "checkNameAndMail", "attendee", "attendees", "key<PERSON>r<PERSON><PERSON><PERSON>", "value", "addOrGetCustomAttributes", "toJSON", "g", "toDurationString", "formatDate", "escape", "generateCustomAttributes", "ICalCategory", "data", "name", "escape", "ICalEventRepeatingFreq", "ICalWeekday", "ICalEventStatus", "ICalEventBusyStatus", "ICalEventTransparency", "ICalEventClass", "ICalEvent", "data", "calendar", "uuid", "id", "sequence", "s", "start", "checkDate", "end", "toDate", "recurrenceId", "timezone", "stamp", "allDay", "floating", "repeating", "isRRule", "checkEnum", "ICalEventRepeatingFreq", "byDayArray", "day", "ICalWeekday", "byMonthArray", "month", "byMonthDayArray", "monthDay", "bySetPosArray", "bySetPos", "excludeArray", "exclude", "summary", "location", "description", "organizer", "checkNameAndMail", "ICalAttendee", "attendee", "attendees", "alarm", "ICalAlarm", "alarms", "category", "ICalCategory", "categories", "status", "busystatus", "priority", "url", "attachments", "attachment", "transparency", "created", "lastModified", "class_", "key<PERSON>r<PERSON><PERSON><PERSON>", "value", "addOrGetCustomAttributes", "toJSON", "d", "g", "formatDate", "formatDateTZ", "l", "excludedDate", "escape", "generateCustomAttributes", "ICalCalendarMethod", "ICalCalendar", "data", "prodId", "language", "method", "checkEnum", "name", "description", "timezone", "source", "url", "scale", "ttl", "isMomentDuration", "event", "ICalEvent", "events", "key<PERSON>r<PERSON><PERSON><PERSON>", "value", "addOrGetCustomAttributes", "g", "tz", "s", "toDurationString", "generateCustomAttributes", "foldLines", "ical", "data", "ICalCalendar", "src_default"]}