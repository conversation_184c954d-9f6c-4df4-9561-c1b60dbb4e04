{"version": 3, "sources": ["../src/parser.ts", "../src/yars_config.ts", "../src/kernel.ts", "../src/debug.ts", "../src/errors.ts", "../src/commands/base.ts", "../src/decorators/args.ts", "../src/decorators/flags.ts", "../src/formatters/flag.ts", "../src/formatters/list.ts", "../src/helpers.ts", "../schemas/main.ts", "../src/formatters/command.ts", "../src/formatters/argument.ts", "../src/commands/list.ts", "../src/loaders/list_loader.ts", "../src/exception_handler.ts", "../index.ts", "../src/commands/help.ts", "../src/loaders/fs_loader.ts", "../src/generators/index_generator.ts", "../stubs/main.ts"], "sourcesContent": ["/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport yargsParser from 'yargs-parser'\nimport { yarsConfig } from './yars_config.js'\n\nimport type {\n  YargsOutput,\n  ParsedOutput,\n  FlagsParserOptions,\n  ArgumentsParserOptions,\n} from './types.js'\n\n/**\n * Parses the command line arguments. The flags are parsed\n * using yargs-parser\n */\nexport class Parser {\n  /**\n   * Parser options\n   */\n  #options: {\n    flagsParserOptions: FlagsParserOptions\n    argumentsParserOptions: ArgumentsParserOptions[]\n  }\n\n  constructor(options: {\n    flagsParserOptions: FlagsParserOptions\n    argumentsParserOptions: ArgumentsParserOptions[]\n  }) {\n    this.#options = options\n  }\n\n  /**\n   * Parsers flags using yargs\n   */\n  #parseFlags(argv: string | string[]) {\n    return yargsParser(argv, { ...this.#options.flagsParserOptions, configuration: yarsConfig })\n  }\n\n  /**\n   * Scans for unknown flags in yargs output.\n   */\n  #scanUnknownFlags(parsed: { [key: string]: any }): string[] {\n    const unknownFlags: string[] = []\n\n    for (let key of Object.keys(parsed)) {\n      if (!this.#options.flagsParserOptions.all.includes(key)) {\n        unknownFlags.push(key)\n      }\n    }\n\n    return unknownFlags\n  }\n\n  /**\n   * Parsers arguments by mimicking the yargs behavior\n   */\n  #parseArguments(parsedOutput: YargsOutput): ParsedOutput {\n    let lastParsedIndex = -1\n\n    const output = this.#options.argumentsParserOptions.map((option, index) => {\n      if (option.type === 'spread') {\n        let value: any[] | undefined = parsedOutput._.slice(index)\n        lastParsedIndex = parsedOutput._.length\n\n        /**\n         * Step 1\n         *\n         * Use default value when original value is not defined.\n         */\n        if (!value.length) {\n          value = Array.isArray(option.default)\n            ? option.default\n            : option.default === undefined\n              ? undefined\n              : [option.default]\n        }\n\n        /**\n         * Step 2\n         *\n         * Call parse method when value is not undefined\n         */\n        if (value !== undefined && option.parse) {\n          value = option.parse(value)\n        }\n\n        return value\n      }\n\n      let value = parsedOutput._[index]\n      lastParsedIndex = index + 1\n\n      /**\n       * Step 1:\n       *\n       * Use default value when original value is undefined\n       * Original value set to empty string will be used\n       * as real value. The behavior is same as yargs\n       * flags parser `--connection=`\n       */\n      if (value === undefined) {\n        value = option.default\n      }\n\n      /**\n       * Step 2\n       *\n       * Call parse method when value is not undefined\n       */\n      if (value !== undefined && option.parse) {\n        value = option.parse(value)\n      }\n\n      return value\n    })\n\n    const { '_': args, '--': o, ...rest } = parsedOutput\n\n    return {\n      args: output,\n      nodeArgs: [],\n      _: args.slice(lastParsedIndex === -1 ? 0 : lastParsedIndex),\n      unknownFlags: this.#scanUnknownFlags(rest),\n      flags: rest,\n    }\n  }\n\n  /**\n   * Parse commandline arguments\n   */\n  parse(argv: string | string[]) {\n    return this.#parseArguments(this.#parseFlags(argv))\n  }\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport { Configuration } from 'yargs-parser'\n\n/**\n * The fixed config used to parse command line arguments using yargs. We\n * do not allow changing these options, since some of the internal\n * checks and features rely on this specific config\n */\nexport const yarsConfig: Partial<Configuration> = {\n  'camel-case-expansion': false,\n  'combine-arrays': true,\n  'short-option-groups': true,\n  'dot-notation': false,\n  'parse-numbers': true,\n  'parse-positional-numbers': false,\n  'boolean-negation': true,\n  'flatten-duplicate-arrays': true,\n  'greedy-arrays': false,\n  'strip-aliased': true,\n  'nargs-eats-options': false,\n  'unknown-options-as-args': false,\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport Hooks from '@poppinss/hooks'\nimport { cliui } from '@poppinss/cliui'\nimport { Prompt } from '@poppinss/prompts'\nimport { distance } from 'fastest-levenshtein'\nimport { RuntimeException } from '@poppinss/utils'\n\nimport debug from './debug.js'\nimport { Parser } from './parser.js'\nimport * as errors from './errors.js'\nimport { ListCommand } from './commands/list.js'\nimport { BaseCommand } from './commands/base.js'\nimport { sortAlphabetically } from './helpers.js'\nimport { ListLoader } from './loaders/list_loader.js'\nimport { ExceptionHandler } from './exception_handler.js'\n\nimport type {\n  Flag,\n  UIPrimitives,\n  FlagListener,\n  LoadedHookArgs,\n  CommandMetaData,\n  LoadersContract,\n  LoadingHookArgs,\n  FindingHookArgs,\n  ExecutedHookArgs,\n  ExecutorContract,\n  LoadedHookHandler,\n  AllowedInfoValues,\n  ExecutingHookArgs,\n  LoadingHookHandler,\n  FindingHookHandler,\n  AbstractBaseCommand,\n  ExecutedHookHandler,\n  ExecutingHookHandler,\n} from './types.js'\n\n/**\n * The Ace kernel manages the registration and execution of commands.\n *\n * The kernel is the main entry point of a console application, and\n * is tailored for a standard CLI environment.\n */\nexport class Kernel<Command extends AbstractBaseCommand> {\n  errorHandler: {\n    render(error: unknown, kernel: Kernel<any>): Promise<any>\n  } = new ExceptionHandler()\n\n  /**\n   * The default executor for creating command's instance\n   * and running them\n   */\n  static commandExecutor: ExecutorContract<typeof BaseCommand> = {\n    create(command, parsedArgs, kernel) {\n      return new command(kernel, parsedArgs, kernel.ui, kernel.prompt)\n    },\n    run(command) {\n      return command.exec()\n    },\n  }\n\n  /**\n   * The default command to use when creating kernel instance\n   * via \"static create\" method.\n   */\n  static defaultCommand: typeof BaseCommand = ListCommand\n\n  /**\n   * Creates an instance of kernel with the default executor\n   * and default command\n   */\n  static create() {\n    return new Kernel<typeof BaseCommand>(this.defaultCommand, this.commandExecutor)\n  }\n\n  /**\n   * Listeners for CLI options. Executed for the main command\n   * only\n   */\n  #optionListeners: Map<string, FlagListener<Command>> = new Map()\n\n  /**\n   * The global command is used to register global flags applicable\n   * on all the commands\n   */\n  #globalCommand: typeof BaseCommand = class extends BaseCommand {\n    static options = {\n      allowUnknownFlags: true,\n    }\n  }\n\n  /**\n   * The default command to run when no command is mentioned. The default\n   * command will also run when only flags are mentioned.\n   */\n  #defaultCommand: Command\n\n  /**\n   * Available hooks\n   */\n  #hooks: Hooks<{\n    finding: FindingHookArgs\n    loading: LoadingHookArgs\n    loaded: LoadedHookArgs<Command>\n    executing: ExecutingHookArgs<InstanceType<Command>>\n    executed: ExecutedHookArgs<InstanceType<Command>>\n  }> = new Hooks()\n\n  /**\n   * Executors are used to instantiate a command and execute\n   * the run method.\n   */\n  #executor: ExecutorContract<Command>\n\n  /**\n   * Keeping track of the main command. There are some action (like termination)\n   * that only the main command can perform\n   */\n  #mainCommand?: InstanceType<Command>\n\n  /**\n   * The current state of kernel. The `running` and `terminated`\n   * states are only set when kernel takes over the process.\n   */\n  #state: 'idle' | 'booted' | 'running' | 'completed' = 'idle'\n\n  /**\n   * Collection of loaders to use for loading commands\n   */\n  #loaders: (LoadersContract<Command> | (() => Promise<LoadersContract<Command>>))[] = []\n\n  /**\n   * An array of registered namespaces. Sorted alphabetically\n   */\n  #namespaces: string[] = []\n\n  /**\n   * A collection of aliases for the commands. The key is the alias name\n   * and the value is the command name.\n   *\n   * In case of duplicate aliases, the most recent alias will override\n   * the previous existing alias.\n   */\n  #aliases: Map<string, string> = new Map()\n\n  /**\n   * An collection of expansion arguments and flags set on\n   * an alias. The key is the alias name and the value is\n   * everything after it.\n   */\n  #aliasExpansions: Map<string, string[]> = new Map()\n\n  /**\n   * A collection of commands by the command name. This allows us to keep only\n   * the unique commands and also keep the loader reference to know which\n   * loader to ask for loading the command.\n   */\n  #commands: Map<string, { metaData: CommandMetaData; loader: LoadersContract<Command> }> =\n    new Map()\n\n  /**\n   * The exit code for the kernel. The exit code is inferred\n   * from the main code when not set explicitly.\n   */\n  exitCode?: number\n\n  /**\n   * The UI primitives to use within commands\n   */\n  ui: UIPrimitives = cliui()\n\n  /**\n   * Instance of prompt to display CLI prompts. We share\n   * a single instance with all the commands. This\n   * allows trapping prompts for commands executed\n   * internally.\n   */\n  prompt = new Prompt()\n\n  /**\n   * CLI info map\n   */\n  info: Map<string, AllowedInfoValues> = new Map()\n\n  /**\n   * List of global flags\n   */\n  get flags(): ({ name: string } & Flag)[] {\n    return this.#globalCommand.flags\n  }\n\n  constructor(defaultCommand: Command, executor: ExecutorContract<Command>) {\n    this.#defaultCommand = defaultCommand\n    this.#executor = executor\n  }\n\n  /**\n   * Process command line arguments. All flags before the command\n   * name are considered as Node.js argv and all flags after\n   * the command name are considered as command argv.\n   *\n   * The behavior is same as Node.js CLI, where all flags before the\n   * script file name are Node.js argv.\n   */\n  #processArgv(argv: string[]) {\n    const commandNameIndex = argv.findIndex((value) => !value.startsWith('-'))\n    if (commandNameIndex === -1) {\n      return {\n        nodeArgv: [],\n        commandName: null,\n        commandArgv: argv,\n      }\n    }\n\n    return {\n      nodeArgv: argv.slice(0, commandNameIndex),\n      commandName: argv[commandNameIndex],\n      commandArgv: argv.slice(commandNameIndex + 1),\n    }\n  }\n\n  /**\n   * Creates an instance of a command by parsing and validating\n   * the command line arguments.\n   */\n  async #create<T extends Command>(Command: T, argv: string | string[]): Promise<InstanceType<T>> {\n    /**\n     * Parse CLI argv without global flags. When running commands directly, we\n     * should not be using global flags anyways\n     */\n    const parsed = new Parser(Command.getParserOptions()).parse(argv)\n\n    /**\n     * Validate the parsed output\n     */\n    Command.validate(parsed)\n\n    /**\n     * Construct command instance using the executor\n     */\n    const commandInstance = await this.#executor.create(Command, parsed, this)\n    commandInstance.hydrate()\n\n    return commandInstance as InstanceType<T>\n  }\n\n  /**\n   * Executes a given command. The main commands are executed using the\n   * \"execMain\" method.\n   */\n  async #exec<T extends Command>(commandName: string, argv: string[]): Promise<InstanceType<T>> {\n    const Command = await this.find<T>(commandName)\n\n    /**\n     * Expand aliases\n     */\n    const aliasExpansions = this.#aliasExpansions.get(commandName)\n    if (aliasExpansions) {\n      argv = aliasExpansions.concat(argv)\n      debug('expanding alias %O, cli args %O', commandName, argv)\n    }\n\n    const commandInstance = await this.#create<T>(Command, argv)\n\n    /**\n     * Execute the command using the executor\n     */\n    await this.#hooks.runner('executing').run(commandInstance, false)\n    await this.#executor.run(commandInstance, this)\n    await this.#hooks.runner('executed').run(commandInstance, false)\n\n    return commandInstance\n  }\n\n  /**\n   * Executes the main command and handles the exceptions by\n   * reporting them\n   */\n  async #execMain(commandName: string, nodeArgv: string[], argv: string[]) {\n    try {\n      const Command = await this.find(commandName)\n\n      /**\n       * Expand aliases\n       */\n      const aliasExpansions = this.#aliasExpansions.get(commandName)\n      if (aliasExpansions) {\n        argv = aliasExpansions.concat(argv)\n        debug('expanding alias %O, cli args %O', commandName, argv)\n      }\n\n      /**\n       * Parse CLI argv and also merge global flags parser options.\n       */\n      const parsed = new Parser(\n        Command.getParserOptions(this.#globalCommand.getParserOptions().flagsParserOptions)\n      ).parse(argv)\n\n      /**\n       * Defined only for the main command\n       */\n      parsed.nodeArgs = nodeArgv\n\n      /**\n       * Validate the flags against the global list as well\n       */\n      this.#globalCommand.validate(parsed)\n\n      /**\n       * Run options listeners. Option listeners can terminate\n       * the process early\n       */\n      let shortcircuit = false\n      for (let [option, listener] of this.#optionListeners) {\n        if (parsed.flags[option] !== undefined) {\n          debug('running listener for \"%s\" flag', option)\n          shortcircuit = await listener(Command, this, parsed)\n          if (shortcircuit) {\n            break\n          }\n        }\n      }\n\n      /**\n       * Validate the parsed output\n       */\n      Command.validate(parsed)\n\n      /**\n       * Return early if a flag listener shortcircuits\n       */\n      if (shortcircuit) {\n        debug('short circuiting from flag listener')\n        this.exitCode = this.exitCode ?? 0\n        this.#state = 'completed'\n        return\n      }\n\n      /**\n       * Keep a note of the main command\n       */\n      this.#mainCommand = await this.#executor.create(Command, parsed, this)\n      this.#mainCommand.hydrate()\n\n      /**\n       * Execute the command using the executor\n       */\n      await this.#hooks.runner('executing').run(this.#mainCommand!, true)\n      await this.#executor.run(this.#mainCommand!, this)\n      await this.#hooks.runner('executed').run(this.#mainCommand!, true)\n      this.exitCode = this.exitCode ?? this.#mainCommand!.exitCode\n      this.#state = 'completed'\n    } catch (error) {\n      this.exitCode = 1\n      this.#state = 'completed'\n      await this.errorHandler.render(error, this)\n    }\n  }\n\n  /**\n   * Listen for CLI options and execute an action. Only one listener\n   * can be defined per option.\n   *\n   * The callbacks are only executed for the main command\n   */\n  on(option: string, callback: FlagListener<Command>): this {\n    debug('registering flag listener for \"%s\" flag', option)\n    this.#optionListeners.set(option, callback)\n    return this\n  }\n\n  /**\n   * Define a global flag that is applicable for all the\n   * commands.\n   */\n  defineFlag(\n    name: string,\n    options: Partial<Flag> & { type: 'string' | 'boolean' | 'array' | 'number' }\n  ) {\n    if (this.#state !== 'idle') {\n      throw new RuntimeException(`Cannot register global flag in \"${this.#state}\" state`)\n    }\n\n    this.#globalCommand.defineFlag(name, options)\n  }\n\n  /**\n   * Register a commands loader. The commands will be collected by\n   * all the loaders.\n   *\n   * Incase multiple loaders returns a single command, the command from the\n   * most recent loader will be used.\n   */\n  addLoader(loader: LoadersContract<Command> | (() => Promise<LoadersContract<Command>>)): this {\n    if (this.#state !== 'idle') {\n      throw new RuntimeException(`Cannot add loader in \"${this.#state}\" state`)\n    }\n\n    this.#loaders.push(loader)\n    return this\n  }\n\n  /**\n   * Register alias for a comamnd name.\n   */\n  addAlias(alias: string, command: string): this {\n    const [commandName, ...expansions] = command.split(' ')\n    this.#aliases.set(alias, commandName)\n\n    if (expansions.length) {\n      debug('registering alias %O for command %O with options %O', alias, commandName, expansions)\n      this.#aliasExpansions.set(alias, expansions)\n    } else {\n      debug('registering alias %O for command %O', alias, commandName)\n    }\n\n    return this\n  }\n\n  /**\n   * Check if a command or an alias is registered with kernel\n   */\n  hasCommand(commandName: string): boolean {\n    commandName = this.#aliases.get(commandName) || commandName\n    return this.#commands.has(commandName)\n  }\n\n  /**\n   * Get the current state of the kernel.\n   */\n  getState() {\n    return this.#state\n  }\n\n  /**\n   * Returns a flat list of commands metadata registered with the kernel.\n   * The list is sorted alphabetically by the command name.\n   */\n  getCommands(): CommandMetaData[] {\n    return [...this.#commands.keys()]\n      .sort(sortAlphabetically)\n      .map((name) => this.#commands.get(name)!.metaData)\n  }\n\n  /**\n   * Get a list of commands for a specific namespace. All non-namespaces\n   * commands will be returned if no namespace is defined.\n   */\n  getNamespaceCommands(namespace?: string) {\n    let commandNames = [...this.#commands.keys()]\n\n    /**\n     * Filter a list of commands by the namespace\n     */\n    if (namespace) {\n      commandNames = commandNames.filter(\n        (name) => this.#commands.get(name)!.metaData.namespace === namespace\n      )\n    } else {\n      commandNames = commandNames.filter((name) => !this.#commands.get(name)!.metaData.namespace)\n    }\n\n    return commandNames.sort(sortAlphabetically).map((name) => this.#commands.get(name)!.metaData)\n  }\n\n  /**\n   * Returns the command metadata by its name. Returns null when the\n   * command is missing.\n   */\n  getCommand(commandName: string): CommandMetaData | null {\n    return this.#commands.get(commandName)?.metaData || null\n  }\n\n  /**\n   * Returns a reference for the default command. The return value\n   * is the default command constructor\n   */\n  getDefaultCommand() {\n    return this.#defaultCommand\n  }\n\n  /**\n   * Returns reference to the main command\n   */\n  getMainCommand() {\n    return this.#mainCommand\n  }\n\n  /**\n   * Returns an array of aliases registered.\n   *\n   * - Call `getCommandAliases` method to get aliases for a given command\n   * - Call `getAliasCommand` to get the command or a given alias\n   */\n  getAliases() {\n    return [...this.#aliases.keys()]\n  }\n\n  /**\n   * Returns the command metata for a given alias. Returns null\n   * if alias is not recognized.\n   */\n  getAliasCommand(alias: string): CommandMetaData | null {\n    const aliasCommand = this.#aliases.get(alias)\n    if (!aliasCommand) {\n      return null\n    }\n\n    return this.#commands.get(aliasCommand)?.metaData || null\n  }\n\n  /**\n   * Returns an array of aliases for a given command\n   */\n  getCommandAliases(commandName: string) {\n    return [...this.#aliases.entries()]\n      .filter(([, command]) => {\n        return command === commandName\n      })\n      .map(([alias]) => alias)\n  }\n\n  /**\n   * Returns a list of namespaces. The list is sorted alphabetically\n   * by the namespace name\n   */\n  getNamespaces(): string[] {\n    return this.#namespaces\n  }\n\n  /**\n   * Returns an array of command and aliases name suggestions for\n   * a given keyword.\n   */\n  getCommandSuggestions(keyword: string): string[] {\n    /**\n     * Priortize namespace commands when the keyword matches the\n     * namespace\n     */\n    if (this.#namespaces.includes(keyword)) {\n      return this.getNamespaceCommands(keyword).map((command) => command.commandName)\n    }\n\n    const commandsAndAliases = [...this.#commands.keys()].concat([...this.#aliases.keys()])\n\n    return commandsAndAliases\n      .map((value) => {\n        return {\n          value,\n          distance: distance(keyword, value),\n        }\n      })\n      .sort((current, next) => next.distance - current.distance)\n      .filter((rating) => {\n        return rating.distance <= 3\n      })\n      .map((rating) => rating.value)\n  }\n\n  /**\n   * Returns an array of namespaces suggestions for a given keyword.\n   */\n  getNamespaceSuggestions(keyword: string): string[] {\n    return this.#namespaces\n      .map((value) => {\n        return {\n          value,\n          distance: distance(keyword, value),\n        }\n      })\n      .sort((current, next) => next.distance - current.distance)\n      .filter((rating) => {\n        return rating.distance <= 3\n      })\n      .map((rating) => rating.value)\n  }\n\n  /**\n   * Listen for the event before we begin the process of finding\n   * the command.\n   */\n  finding(callback: FindingHookHandler) {\n    this.#hooks.add('finding', callback)\n    return this\n  }\n\n  /**\n   * Listen for the event when importing the command\n   */\n  loading(callback: LoadingHookHandler) {\n    this.#hooks.add('loading', callback)\n    return this\n  }\n\n  /**\n   * Listen for the event when the command has been imported\n   */\n  loaded(callback: LoadedHookHandler<Command>) {\n    this.#hooks.add('loaded', callback)\n    return this\n  }\n\n  /**\n   * Listen for the event before we start to execute the command.\n   */\n  executing(callback: ExecutingHookHandler<InstanceType<Command>>) {\n    this.#hooks.add('executing', callback)\n    return this\n  }\n\n  /**\n   * Listen for the event after the command has been executed\n   */\n  executed(callback: ExecutedHookHandler<InstanceType<Command>>) {\n    this.#hooks.add('executed', callback)\n    return this\n  }\n\n  /**\n   * Loads commands from all the registered loaders. The \"addLoader\" method\n   * must be called before calling the \"load\" method.\n   */\n  async boot() {\n    if (this.#state !== 'idle') {\n      return\n    }\n\n    /**\n     * Boot global command is not already booted\n     */\n    this.#globalCommand.boot()\n\n    /**\n     * Registering the default command\n     */\n    this.addLoader(new ListLoader([this.#defaultCommand]))\n\n    /**\n     * Set state to booted\n     */\n    this.#state = 'booted'\n\n    /**\n     * A set of unique namespaces. Later, we will store them on kernel\n     * directly as an alphabetically sorted array.\n     */\n    const namespaces: Set<string> = new Set()\n\n    /**\n     * Load metadata for all commands using the loaders\n     */\n    for (let loader of this.#loaders) {\n      let loaderInstance: LoadersContract<Command>\n\n      /**\n       * A loader can be a function that lazily imports and instantiates\n       * a loader\n       */\n      if (typeof loader === 'function') {\n        loaderInstance = await loader()\n      } else {\n        loaderInstance = loader\n      }\n\n      const commands = await loaderInstance.getMetaData()\n\n      commands.forEach((command) => {\n        this.#commands.set(command.commandName, { metaData: command, loader: loaderInstance })\n        command.aliases.forEach((alias) => this.addAlias(alias, command.commandName))\n        command.namespace && namespaces.add(command.namespace)\n      })\n    }\n\n    this.#namespaces = [...namespaces].sort(sortAlphabetically)\n  }\n\n  /**\n   * Find a command by its name\n   */\n  async find<T extends Command>(commandName: string): Promise<T> {\n    /**\n     * Get command name from the alias (if one exists)\n     */\n    commandName = this.#aliases.get(commandName) || commandName\n    await this.#hooks.runner('finding').run(commandName)\n\n    /**\n     * Find if we have a command registered\n     */\n    const command = this.#commands.get(commandName)\n    if (!command) {\n      throw new errors.E_COMMAND_NOT_FOUND([commandName])\n    }\n\n    await this.#hooks.runner('loading').run(command.metaData)\n\n    /**\n     * Find if the loader is able to load the command\n     */\n    const commandConstructor = await command.loader.getCommand(command.metaData)\n    if (!commandConstructor) {\n      throw new errors.E_COMMAND_NOT_FOUND([commandName])\n    }\n\n    await this.#hooks.runner('loaded').run(commandConstructor)\n    return commandConstructor as T\n  }\n\n  /**\n   * Execute a command. The second argument is an array of commandline\n   * arguments (without the command name)\n   */\n  async exec<T extends Command>(commandName: string, argv: string[]) {\n    /**\n     * Boot if not already booted\n     */\n    if (this.#state === 'idle') {\n      await this.boot()\n    }\n\n    /**\n     * Cannot execute commands after the main command has exited\n     */\n    if (this.#state === 'completed') {\n      throw new RuntimeException(\n        'The kernel has been terminated. Create a fresh instance to execute commands'\n      )\n    }\n\n    return this.#exec<T>(commandName, argv)\n  }\n\n  /**\n   * Creates a command instance by parsing and validating\n   * the command-line arguments.\n   */\n  async create<T extends Command>(command: T, argv: string | string[]): Promise<InstanceType<T>> {\n    /**\n     * Boot if not already booted\n     */\n    if (this.#state === 'idle') {\n      await this.boot()\n    }\n\n    return this.#create(command, argv)\n  }\n\n  /**\n   * Handle process argv and execute the command. Calling this method\n   * makes kernel own the process and register SIGNAL listeners\n   */\n  async handle(argv: string[]) {\n    /**\n     * Cannot run multiple main commands from a single process\n     */\n    if (this.#state === 'running') {\n      throw new RuntimeException('Cannot run multiple main commands from a single process')\n    }\n\n    /**\n     * Cannot run multiple main commands from the same instance\n     */\n    if (this.#state === 'completed') {\n      throw new RuntimeException(\n        'The kernel has been terminated. Create a fresh instance to execute commands'\n      )\n    }\n\n    /**\n     * Boot kernel\n     */\n    if (this.#state === 'idle') {\n      await this.boot()\n    }\n\n    this.#state = 'running'\n    const { commandName, nodeArgv, commandArgv } = this.#processArgv(argv)\n\n    /**\n     * Run the default command\n     */\n    if (!commandName) {\n      debug('running default command \"%s\"', this.#defaultCommand.commandName)\n      return this.#execMain(this.#defaultCommand.commandName, nodeArgv, commandArgv)\n    }\n\n    /**\n     * Run the mentioned command as the main command\n     */\n    debug('running main command \"%s\"', commandName)\n    return this.#execMain(commandName, nodeArgv, commandArgv)\n  }\n\n  /**\n   * A named function that returns true. To be used\n   * by flag listeners\n   */\n  shortcircuit() {\n    return true\n  }\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport { debuglog } from 'node:util'\n\nexport default debuglog('adonisjs:ace')\n", "/*\n * @adonisjs/ace\n *\n * (c) Ad<PERSON>sJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport { errors } from '@poppinss/prompts'\nimport { createError, Exception } from '@poppinss/utils'\n\nexport const E_PROMPT_CANCELLED = errors.E_PROMPT_CANCELLED\n\n/**\n * Command is missing the static property command name\n */\nexport const E_MISSING_COMMAND_NAME = createError<[command: string]>(\n  'Cannot serialize command \"%s\". Missing static property \"commandName\"',\n  'E_MISSING_COMMAND_NAME'\n)\n\n/**\n * Cannot find a command for the given name\n */\nexport const E_COMMAND_NOT_FOUND = class CommandNotFound extends Exception {\n  static status: number = 404\n  commandName: string\n  constructor(args: [command: string]) {\n    super(`Command \"${args[0]}\" is not defined`, { code: 'E_COMMAND_NOT_FOUND' })\n    this.commandName = args[0]\n  }\n}\n\n/**\n * Missing a required flag when running the command\n */\nexport const E_MISSING_FLAG = createError<[flag: string]>(\n  'Missing required option \"%s\"',\n  'E_MISSING_FLAG'\n)\n\n/**\n * Missing value for a flag that accepts values\n */\nexport const E_MISSING_FLAG_VALUE = createError<[flag: string]>(\n  'Missing value for option \"%s\"',\n  'E_MISSING_FLAG_VALUE'\n)\n\n/**\n * Missing a required argument when running the command\n */\nexport const E_MISSING_ARG = createError<[arg: string]>(\n  'Missing required argument \"%s\"',\n  'E_MISSING_ARG'\n)\n\n/**\n * Missing value for an argument\n */\nexport const E_MISSING_ARG_VALUE = createError<[arg: string]>(\n  'Missing value for argument \"%s\"',\n  'E_MISSING_ARG_VALUE'\n)\n\n/**\n * An unknown flag was mentioned\n */\nexport const E_UNKNOWN_FLAG = createError<[flag: string]>(\n  'Unknown flag \"%s\". The mentioned flag is not accepted by the command',\n  'E_UNKNOWN_FLAG'\n)\n\n/**\n * Invalid value provided for the flag\n */\nexport const E_INVALID_FLAG = createError<[flag: string, expectedDataType: string]>(\n  'Invalid value. The \"%s\" flag accepts a \"%s\" value',\n  'E_INVALID_FLAG'\n)\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport { inspect } from 'node:util'\nimport string from '@poppinss/utils/string'\nimport Macroable from '@poppinss/macroable'\nimport lodash from '@poppinss/utils/lodash'\nimport { AssertionError } from 'node:assert'\nimport type { Prompt } from '@poppinss/prompts'\nimport type { Colors } from '@poppinss/cliui/types'\nimport { defineStaticProperty, InvalidArgumentsException } from '@poppinss/utils'\n\nimport debug from '../debug.js'\nimport * as errors from '../errors.js'\nimport type { Kernel } from '../kernel.js'\nimport type {\n  Flag,\n  Argument,\n  ParsedOutput,\n  UIPrimitives,\n  CommandOptions,\n  CommandMetaData,\n  FlagsParserOptions,\n  ArgumentsParserOptions,\n} from '../types.js'\n\n/**\n * The base command sets the foundation for defining ace commands.\n * Every command should inherit from the base command.\n */\nexport class BaseCommand extends Macroable {\n  static booted: boolean = false\n\n  /**\n   * Configuration options accepted by the command\n   */\n  static options: CommandOptions\n\n  /**\n   * A collection of aliases for the command\n   */\n  static aliases: string[]\n\n  /**\n   * The command name one can type to run the command\n   */\n  static commandName: string\n\n  /**\n   * The command description\n   */\n  static description: string\n\n  /**\n   * The help text for the command. Help text can be a multiline\n   * string explaining the usage of command\n   */\n  static help?: string | string[]\n\n  /**\n   * Registered arguments\n   */\n  static args: Argument[]\n\n  /**\n   * Registered flags\n   */\n  static flags: Flag[]\n\n  /**\n   * Define static properties on the class. During inheritance, certain\n   * properties must inherit from the parent.\n   */\n  static boot() {\n    if (Object.hasOwn(this, 'booted') && this.booted === true) {\n      return\n    }\n\n    this.booted = true\n    defineStaticProperty(this, 'args', { initialValue: [], strategy: 'inherit' })\n    defineStaticProperty(this, 'flags', { initialValue: [], strategy: 'inherit' })\n    defineStaticProperty(this, 'aliases', { initialValue: [], strategy: 'inherit' })\n    defineStaticProperty(this, 'commandName', { initialValue: '', strategy: 'inherit' })\n    defineStaticProperty(this, 'description', { initialValue: '', strategy: 'inherit' })\n    defineStaticProperty(this, 'help', { initialValue: '', strategy: 'inherit' })\n    defineStaticProperty(this, 'options', {\n      initialValue: { staysAlive: false, allowUnknownFlags: false },\n      strategy: 'inherit',\n    })\n  }\n\n  /**\n   * Specify the argument the command accepts. The arguments via the CLI\n   * will be accepted in the same order as they are defined.\n   *\n   * Mostly, you will be using the `@args` decorator to define the arguments.\n   *\n   * ```ts\n   * Command.defineArgument('entity', { type: 'string' })\n   * ```\n   */\n  static defineArgument(name: string, options: Partial<Argument> & { type: 'string' | 'spread' }) {\n    this.boot()\n    const arg = { name, argumentName: string.dashCase(name), required: true, ...options }\n    const lastArg = this.args[this.args.length - 1]\n\n    /**\n     * Ensure the arg type is specified\n     */\n    if (!arg.type) {\n      throw new InvalidArgumentsException(\n        `Cannot define argument \"${this.name}.${name}\". Specify the argument type`\n      )\n    }\n\n    /**\n     * Ensure we are not adding arguments after a spread argument\n     */\n    if (lastArg && lastArg.type === 'spread') {\n      throw new InvalidArgumentsException(\n        `Cannot define argument \"${this.name}.${name}\" after spread argument \"${this.name}.${lastArg.name}\". Spread argument should be the last one`\n      )\n    }\n\n    /**\n     * Ensure we are not adding a required argument after an optional\n     * argument\n     */\n    if (arg.required && lastArg && lastArg.required === false) {\n      throw new InvalidArgumentsException(\n        `Cannot define required argument \"${this.name}.${name}\" after optional argument \"${this.name}.${lastArg.name}\"`\n      )\n    }\n\n    if (debug.enabled) {\n      debug('defining arg %O, command: %O', arg, `[class: ${this.name}]`)\n    }\n\n    this.args.push(arg)\n  }\n\n  /**\n   * Specify a flag the command accepts.\n   *\n   * Mostly, you will be using the `@flags` decorator to define a flag.\n   *\n   * ```ts\n   * Command.defineFlag('connection', { type: 'string', required: true })\n   * ```\n   */\n  static defineFlag(\n    name: string,\n    options: Partial<Flag> & { type: 'string' | 'boolean' | 'array' | 'number' }\n  ) {\n    this.boot()\n    const flag = { name, flagName: string.dashCase(name), required: false, ...options }\n\n    /**\n     * Ensure the arg type is specified\n     */\n    if (!flag.type) {\n      throw new InvalidArgumentsException(\n        `Cannot define flag \"${this.name}.${name}\". Specify the flag type`\n      )\n    }\n\n    if (debug.enabled) {\n      debug('defining flag %O, command: %O', flag, `[class: ${this.name}]`)\n    }\n\n    this.flags.push(flag)\n  }\n\n  /**\n   * Returns the options for parsing flags and arguments\n   */\n  static getParserOptions(options?: FlagsParserOptions): {\n    flagsParserOptions: Required<FlagsParserOptions>\n    argumentsParserOptions: ArgumentsParserOptions[]\n  } {\n    this.boot()\n\n    const argumentsParserOptions: ArgumentsParserOptions[] = this.args.map((arg) => {\n      return {\n        type: arg.type,\n        default: arg.default,\n        parse: arg.parse,\n      }\n    })\n\n    const flagsParserOptions: Required<FlagsParserOptions> = lodash.merge(\n      {\n        all: [],\n        string: [],\n        boolean: [],\n        array: [],\n        number: [],\n        alias: {},\n        count: [],\n        coerce: {},\n        default: {},\n      },\n      options\n    )\n\n    this.flags.forEach((flag) => {\n      flagsParserOptions.all.push(flag.flagName)\n\n      if (flag.alias) {\n        flagsParserOptions.alias[flag.flagName] = flag.alias\n      }\n      if (flag.parse) {\n        flagsParserOptions.coerce[flag.flagName] = flag.parse\n      }\n      if (flag.default !== undefined) {\n        flagsParserOptions.default[flag.flagName] = flag.default\n      }\n\n      switch (flag.type) {\n        case 'string':\n          flagsParserOptions.string.push(flag.flagName)\n          break\n        case 'boolean':\n          flagsParserOptions.boolean.push(flag.flagName)\n          break\n        case 'number':\n          flagsParserOptions.number.push(flag.flagName)\n          break\n        case 'array':\n          flagsParserOptions.array.push(flag.flagName)\n          break\n      }\n    })\n\n    return {\n      flagsParserOptions,\n      argumentsParserOptions,\n    }\n  }\n\n  /**\n   * Serializes the command to JSON. The return value satisfies the\n   * {@link CommandMetaData}\n   */\n  static serialize(): CommandMetaData {\n    this.boot()\n    if (!this.commandName) {\n      throw new errors.E_MISSING_COMMAND_NAME([this.name])\n    }\n\n    const [namespace, name] = this.commandName.split(':')\n\n    return {\n      commandName: this.commandName,\n      description: this.description,\n      help: this.help,\n      namespace: name ? namespace : null,\n      aliases: this.aliases,\n      flags: this.flags.map((flag) => {\n        const { parse, ...rest } = flag\n        return rest\n      }),\n      args: this.args.map((arg) => {\n        const { parse, ...rest } = arg\n        return rest\n      }),\n      options: this.options,\n    }\n  }\n\n  /**\n   * Validate the yargs parsed output againts the command.\n   */\n  static validate(parsedOutput: ParsedOutput) {\n    this.boot()\n\n    /**\n     * Validates args and their values\n     */\n    this.args.forEach((arg, index) => {\n      const value = parsedOutput.args[index] as string\n      const hasDefinedArgument = value !== undefined\n\n      if (arg.required && !hasDefinedArgument) {\n        throw new errors.E_MISSING_ARG([arg.name])\n      }\n\n      if (hasDefinedArgument && !arg.allowEmptyValue && (value === '' || !value.length)) {\n        if (debug.enabled) {\n          debug('disallowing empty value \"%s\" for arg: \"%s\"', value, arg.name)\n        }\n\n        throw new errors.E_MISSING_ARG_VALUE([arg.name])\n      }\n    })\n\n    /**\n     * Disallow unknown flags\n     */\n    if (!this.options.allowUnknownFlags && parsedOutput.unknownFlags.length) {\n      const unknowFlag = parsedOutput.unknownFlags[0]\n      const unknowFlagName = unknowFlag.length === 1 ? `-${unknowFlag}` : `--${unknowFlag}`\n      throw new errors.E_UNKNOWN_FLAG([unknowFlagName])\n    }\n\n    /**\n     * Validate flags\n     */\n    this.flags.forEach((flag) => {\n      const hasMentionedFlag = Object.hasOwn(parsedOutput.flags, flag.flagName)\n      const value = parsedOutput.flags[flag.flagName]\n\n      /**\n       * Validate the value by flag type\n       */\n      switch (flag.type) {\n        case 'boolean':\n          /**\n           * If flag is required, then it should be mentioned\n           */\n          if (flag.required && !hasMentionedFlag) {\n            throw new errors.E_MISSING_FLAG([flag.flagName])\n          }\n          break\n        case 'number':\n          /**\n           * If flag is required, then it should be mentioned\n           */\n          if (flag.required && !hasMentionedFlag) {\n            throw new errors.E_MISSING_FLAG([flag.flagName])\n          }\n\n          /**\n           * Regardless of whether flag is required or not. If it is mentioned,\n           * then some value should be provided.\n           *\n           * In case of number input, yargs sends undefined\n           */\n          if (hasMentionedFlag && value === undefined) {\n            throw new errors.E_MISSING_FLAG_VALUE([flag.flagName])\n          }\n\n          if (Number.isNaN(value)) {\n            throw new errors.E_INVALID_FLAG([flag.flagName, 'numeric'])\n          }\n          break\n        case 'string':\n        case 'array':\n          /**\n           * If flag is required, then it should be mentioned\n           */\n          if (flag.required && !hasMentionedFlag) {\n            throw new errors.E_MISSING_FLAG([flag.flagName])\n          }\n\n          /**\n           * Regardless of whether flag is required or not. If it is mentioned,\n           * then some value should be provided, unless empty values are\n           * allowed.\n           *\n           * In case of string, flag with no value receives an empty string\n           * In case of array, flag with no value receives an empty array\n           */\n          if (hasMentionedFlag && !flag.allowEmptyValue && (value === '' || !value.length)) {\n            if (debug.enabled) {\n              debug('disallowing empty value \"%s\" for flag: \"%s\"', value, flag.name)\n            }\n\n            throw new errors.E_MISSING_FLAG_VALUE([flag.flagName])\n          }\n      }\n    })\n  }\n\n  /**\n   * Check if a command has been hypdrated\n   */\n  protected hydrated: boolean = false\n\n  /**\n   * The exit code for the command\n   */\n  exitCode?: number\n\n  /**\n   * The error raised at the time of the executing the command.\n   * The value is undefined if no error is raised.\n   */\n  error?: any\n\n  /**\n   * The result property stores the return value of the \"run\"\n   * method (unless commands sets it explicitly)\n   */\n  result?: any\n\n  /**\n   * Logger to log messages\n   */\n  get logger() {\n    return this.ui.logger\n  }\n\n  /**\n   * Add colors to console messages\n   */\n  get colors(): Colors {\n    return this.ui.colors\n  }\n\n  /**\n   * Is the current command the main command executed from the\n   * CLI\n   */\n  get isMain(): boolean {\n    return this.kernel.getMainCommand() === this\n  }\n\n  /**\n   * Reference to the command name\n   */\n  get commandName() {\n    return (this.constructor as typeof BaseCommand).commandName\n  }\n\n  /**\n   * Reference to the command options\n   */\n  get options() {\n    return (this.constructor as typeof BaseCommand).options\n  }\n\n  /**\n   * Reference to the command args\n   */\n  get args() {\n    return (this.constructor as typeof BaseCommand).args\n  }\n\n  /**\n   * Reference to the command flags\n   */\n  get flags() {\n    return (this.constructor as typeof BaseCommand).flags\n  }\n\n  constructor(\n    protected kernel: Kernel<any>,\n    protected parsed: ParsedOutput,\n    public ui: UIPrimitives,\n    public prompt: Prompt\n  ) {\n    super()\n  }\n\n  /**\n   * Hydrate command by setting class properties from\n   * the parsed output\n   */\n  hydrate() {\n    if (this.hydrated) {\n      return\n    }\n\n    const CommandConstructor = this.constructor as typeof BaseCommand\n\n    /**\n     * Set args as properties on the command instance\n     */\n    CommandConstructor.args.forEach((arg, index) => {\n      Object.defineProperty(this, arg.name, {\n        value: this.parsed.args[index],\n        enumerable: true,\n        writable: true,\n        configurable: true,\n      })\n    })\n\n    /**\n     * Set flags as properties on the command instance\n     */\n    CommandConstructor.flags.forEach((flag) => {\n      Object.defineProperty(this, flag.name, {\n        value: this.parsed.flags[flag.flagName],\n        enumerable: true,\n        writable: true,\n        configurable: true,\n      })\n    })\n\n    this.hydrated = true\n  }\n\n  /**\n   * The run method should include the implementation for the\n   * command.\n   */\n  async run(..._: any[]): Promise<any> {}\n\n  /**\n   * Executes the commands by running the command's run method.\n   */\n  async exec() {\n    this.hydrate()\n\n    try {\n      this.result = await this.run()\n      this.exitCode = this.exitCode ?? 0\n      return this.result\n    } catch (error) {\n      this.error = error\n      this.exitCode = this.exitCode ?? 1\n      throw error\n    }\n  }\n\n  /**\n   * JSON representation of the command\n   */\n  toJSON() {\n    return {\n      commandName: (this.constructor as typeof BaseCommand).commandName,\n      options: (this.constructor as typeof BaseCommand).options,\n      args: this.parsed.args,\n      flags: this.parsed.flags,\n      error: this.error,\n      result: this.result,\n      exitCode: this.exitCode,\n    }\n  }\n\n  /**\n   * Assert the command exists with a given exit code\n   */\n  assertExitCode(code: number) {\n    if (this.exitCode !== code) {\n      throw new AssertionError({\n        message: `Expected '${this.commandName}' command to finish with exit code '${code}'`,\n        actual: this.exitCode,\n        expected: code,\n        operator: 'strictEqual',\n        stackStartFn: this.assertExitCode,\n      })\n    }\n  }\n\n  /**\n   * Assert the command exists with a given exit code\n   */\n  assertNotExitCode(code: number) {\n    if (this.exitCode === code) {\n      throw new AssertionError({\n        message: `Expected '${this.commandName}' command to finish without exit code '${this.exitCode}'`,\n        stackStartFn: this.assertNotExitCode,\n      })\n    }\n  }\n\n  /**\n   * Assert the command exists with zero exit code\n   */\n  assertSucceeded() {\n    return this.assertExitCode(0)\n  }\n\n  /**\n   * Assert the command exists with non-zero exit code\n   */\n  assertFailed() {\n    return this.assertNotExitCode(0)\n  }\n\n  /**\n   * Assert command to log the expected message\n   */\n  assertLog(message: string, stream?: 'stdout' | 'stderr') {\n    const logs = this.logger.getLogs()\n    const logMessages = logs.map((log) => log.message)\n    const matchingLog = logs.find((log) => log.message === message)\n\n    /**\n     * No log found\n     */\n    if (!matchingLog) {\n      throw new AssertionError({\n        message: `Expected log messages to include ${inspect(message)}`,\n        actual: logMessages,\n        expected: [message],\n        operator: 'strictEqual',\n        stackStartFn: this.assertLog,\n      })\n    }\n\n    /**\n     * Log is on a different stream\n     */\n    if (stream && matchingLog.stream !== stream) {\n      throw new AssertionError({\n        message: `Expected log message stream to be ${inspect(stream)}, instead received ${inspect(\n          matchingLog.stream\n        )}`,\n        actual: matchingLog.stream,\n        expected: stream,\n        operator: 'strictEqual',\n        stackStartFn: this.assertLog,\n      })\n    }\n  }\n\n  /**\n   * Assert command to log the expected message\n   */\n  assertLogMatches(matchingRegex: RegExp, stream?: 'stdout' | 'stderr') {\n    const logs = this.logger.getLogs()\n    const matchingLog = logs.find((log) => matchingRegex.test(log.message))\n\n    /**\n     * No log found\n     */\n    if (!matchingLog) {\n      throw new AssertionError({\n        message: `Expected log messages to match ${inspect(matchingRegex)}`,\n        stackStartFn: this.assertLogMatches,\n      })\n    }\n\n    /**\n     * Log is on a different stream\n     */\n    if (stream && matchingLog.stream !== stream) {\n      throw new AssertionError({\n        message: `Expected log message stream to be ${inspect(stream)}, instead received ${inspect(\n          matchingLog.stream\n        )}`,\n        actual: matchingLog.stream,\n        expected: stream,\n        operator: 'strictEqual',\n        stackStartFn: this.assertLogMatches,\n      })\n    }\n  }\n\n  /**\n   * Assert the command prints a table to stdout\n   */\n  assertTableRows(rows: string[][]) {\n    const logs = this.logger.getLogs()\n    const hasAllMatchingRows = rows.every((row) => {\n      const columnsContent = row.join('|')\n      return !!logs.find((log) => log.message === columnsContent)\n    })\n\n    if (!hasAllMatchingRows) {\n      throw new AssertionError({\n        message: `Expected log messages to include a table with the expected rows`,\n        operator: 'strictEqual',\n        stackStartFn: this.assertTableRows,\n      })\n    }\n  }\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport type { BaseCommand } from '../commands/base.js'\nimport type { SpreadArgument, StringArgument } from '../types.js'\n\n/**\n * Namespace for defining arguments using decorators.\n */\nexport const args = {\n  /**\n   * Define argument that accepts a string value\n   */\n  string<Type = string>(options?: Partial<Omit<StringArgument<Type>, 'type'>>) {\n    return function addArg<Key extends string, Target extends { [K in Key]?: Type }>(\n      target: Target,\n      propertyName: Key\n    ) {\n      const Command = target.constructor as typeof BaseCommand\n      Command.defineArgument(propertyName, { ...options, type: 'string' })\n    }\n  },\n\n  /**\n   * Define argument that accepts a spread value\n   */\n  spread<Type extends any = string[]>(options?: Partial<Omit<SpreadArgument<Type>, 'type'>>) {\n    return function addArg<Key extends string, Target extends { [K in Key]?: Type }>(\n      target: Target,\n      propertyName: Key\n    ) {\n      const Command = target.constructor as typeof BaseCommand\n      Command.defineArgument(propertyName, { ...options, type: 'spread' })\n    }\n  },\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport type { BaseCommand } from '../commands/base.js'\nimport type { ArrayFlag, NumberFlag, StringFlag, BooleanFlag } from '../types.js'\n\n/**\n * Namespace for defining flags using decorators.\n */\nexport const flags = {\n  /**\n   * Define option that accepts a string value\n   */\n  string<Type = string>(options?: Partial<Omit<StringFlag<Type>, 'type'>>) {\n    return function addArg<Key extends string, Target extends { [K in Key]?: Type }>(\n      target: Target,\n      propertyName: Key\n    ) {\n      const Command = target.constructor as typeof BaseCommand\n      Command.defineFlag(propertyName, { type: 'string', ...options })\n    }\n  },\n\n  /**\n   * Define option that accepts a boolean value\n   */\n  boolean<Type = boolean>(options?: Partial<Omit<BooleanFlag<Type>, 'type'>>) {\n    return function addArg<Key extends string, Target extends { [K in Key]?: Type }>(\n      target: Target,\n      propertyName: Key\n    ) {\n      const Command = target.constructor as typeof BaseCommand\n      Command.defineFlag(propertyName, { type: 'boolean', ...options })\n    }\n  },\n\n  /**\n   * Define option that accepts a number value\n   */\n  number<Type = number>(options?: Partial<Omit<NumberFlag<Type>, 'type'>>) {\n    return function addArg<Key extends string, Target extends { [K in Key]?: Type }>(\n      target: Target,\n      propertyName: Key\n    ) {\n      const Command = target.constructor as typeof BaseCommand\n      Command.defineFlag(propertyName, { type: 'number', ...options })\n    }\n  },\n\n  /**\n   * Define option that accepts an array of values\n   */\n  array<Type extends any[] = string[]>(options?: Partial<Omit<ArrayFlag<Type>, 'type'>>) {\n    return function addArg<Key extends string, Target extends { [K in Key]?: Type }>(\n      target: Target,\n      propertyName: Key\n    ) {\n      const Command = target.constructor as typeof BaseCommand\n      Command.defineFlag(propertyName, { type: 'array', ...options })\n    }\n  },\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport { ArrayFlag, BooleanFlag, Flag, NumberFlag, StringFlag, UIPrimitives } from '../types.js'\n\n/**\n * The flag formatter formats a flag as per the http://docopt.org/ specification.\n */\nexport class FlagFormatter {\n  #flag: Flag\n  #colors: UIPrimitives['colors']\n\n  constructor(flag: Flag, colors: UIPrimitives['colors']) {\n    this.#flag = flag\n    this.#colors = colors\n  }\n\n  /**\n   * Formats the value flag\n   */\n  #formatValueFlag(flag: Flag, valuePlaceholder: string) {\n    return flag.required ? `=${valuePlaceholder}` : `[=${valuePlaceholder}]`\n  }\n\n  /**\n   * Formats the aliases for the flag\n   */\n  #formatAliases(flag: Flag): string[] {\n    if (!flag.alias) {\n      return []\n    }\n\n    if (typeof flag.alias === 'string') {\n      return [`-${flag.alias}`]\n    }\n\n    return flag.alias.map((alias) => `-${alias}`)\n  }\n\n  /**\n   * Formats the array flag by appending ellipsis `...` and wrapping\n   * the value to indicate if it is required or not\n   */\n  #formatArrayFlag(flag: ArrayFlag<string[]>) {\n    const value = this.#formatValueFlag(flag, `${flag.flagName.toUpperCase()}...`)\n    const aliases = this.#formatAliases(flag)\n    const flagWithValue = `--${flag.flagName}${value}`\n\n    if (aliases.length) {\n      return `  ${this.#colors.green(`${aliases.join(',')}, ${flagWithValue}`)}  `\n    }\n\n    return `  ${this.#colors.green(flagWithValue)}  `\n  }\n\n  /**\n   * Formats the string flag by wrapping the value to indicate\n   * if it is required or not\n   */\n  #formatStringFlag(flag: StringFlag<string>) {\n    const value = this.#formatValueFlag(flag, `${flag.flagName.toUpperCase()}`)\n    const aliases = this.#formatAliases(flag)\n    const flagWithValue = `--${flag.flagName}${value}`\n\n    if (aliases.length) {\n      return `  ${this.#colors.green(`${aliases.join(',')}, ${flagWithValue}`)}  `\n    }\n\n    return `  ${this.#colors.green(flagWithValue)}  `\n  }\n\n  /**\n   * Formats the numeric flag by wrapping the value to indicate\n   * if it is required or not\n   */\n  #formatNumericFlag(flag: NumberFlag<number>) {\n    const value = this.#formatValueFlag(flag, `${flag.flagName.toUpperCase()}`)\n    const aliases = this.#formatAliases(flag)\n    const flagWithValue = `--${flag.flagName}${value}`\n\n    if (aliases.length) {\n      return `  ${this.#colors.green(`${aliases.join(',')}, ${flagWithValue}`)}  `\n    }\n\n    return `  ${this.#colors.green(flagWithValue)}  `\n  }\n\n  /**\n   * Formats the boolean flag. Boolean flags needs no wrapping\n   */\n  #formatBooleanFlag(flag: BooleanFlag<boolean>) {\n    const aliases = this.#formatAliases(flag)\n    const negatedVariant = flag.showNegatedVariantInHelp ? `|--no-${flag.flagName}` : ''\n    const flagWithVariant = `--${flag.flagName}${negatedVariant}`\n\n    if (aliases.length) {\n      return `  ${this.#colors.green(`${aliases.join(',')}, ${flagWithVariant}`)}  `\n    }\n\n    return `  ${this.#colors.green(flagWithVariant)}  `\n  }\n\n  /**\n   * Returns formatted description for the flag\n   */\n  formatDescription(): string {\n    const defaultValue = this.#flag.default !== undefined ? `[default: ${this.#flag.default}]` : ''\n    const separator = defaultValue && this.#flag.description ? ' ' : ''\n    return this.#colors.dim(`${this.#flag.description || ''}${separator}${defaultValue}`)\n  }\n\n  /**\n   * Returns a formatted version of the flag name and aliases\n   */\n  formatOption(): string {\n    switch (this.#flag.type) {\n      case 'array':\n        return this.#formatArrayFlag(this.#flag)\n      case 'string':\n        return this.#formatStringFlag(this.#flag)\n      case 'number':\n        return this.#formatNumericFlag(this.#flag)\n      case 'boolean':\n        return this.#formatBooleanFlag(this.#flag)\n    }\n  }\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport stringWidth from 'string-width'\nimport { justify, TERMINAL_SIZE, wrap } from '@poppinss/cliui/helpers'\n\nimport type { ListTable } from '../types.js'\n\n/**\n * The list formatter formats the list of commands and flags. The option column\n * is justified to have same width accross all the rows.\n */\nexport class ListFormatter {\n  #tables: ListTable[]\n  #largestOptionColumnWidth: number\n\n  constructor(tables: ListTable[]) {\n    this.#tables = tables\n    this.#largestOptionColumnWidth = Math.max(\n      ...this.#tables\n        .map((table) => table.columns.map((column) => stringWidth(column.option)))\n        .flat()\n    )\n  }\n\n  /**\n   * Formats the table to an array of plain text rows.\n   */\n  #formatTable(table: ListTable, terminalWidth: number): string[] {\n    const options = justify(\n      table.columns.map(({ option }) => option),\n      { maxWidth: this.#largestOptionColumnWidth }\n    )\n\n    const descriptions = wrap(\n      table.columns.map(({ description }) => description),\n      {\n        startColumn: this.#largestOptionColumnWidth,\n        endColumn: terminalWidth,\n        trimStart: true,\n      }\n    )\n\n    return table.columns.map((_, index) => `${options[index]}${descriptions[index]}`)\n  }\n\n  /**\n   * Format tables list into an array of rows\n   */\n  format(terminalWidth: number = TERMINAL_SIZE) {\n    return this.#tables.map((table) => {\n      return {\n        heading: table.heading,\n        rows: this.#formatTable(table, terminalWidth),\n      }\n    })\n  }\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport { Validator } from 'jsonschema'\nimport { readFile } from 'node:fs/promises'\nimport { RuntimeException } from '@poppinss/utils'\n\nimport { schemaRoot } from '../schemas/main.js'\nimport type { AbstractBaseCommand, CommandMetaData, UIPrimitives } from './types.js'\n\nconst schema = JSON.parse(\n  await readFile(new URL('./command_metadata_schema.json', schemaRoot), 'utf8')\n)\n\n/**\n * Helper to sort array of strings alphabetically.\n */\nexport function sortAlphabetically(prev: string, curr: string) {\n  if (curr > prev) {\n    return -1\n  }\n\n  if (curr < prev) {\n    return 1\n  }\n\n  return 0\n}\n\n/**\n * Renders an error message and lists suggestions.\n */\nexport function renderErrorWithSuggestions(\n  ui: UIPrimitives,\n  message: string,\n  suggestions: string[]\n) {\n  const instructions = ui\n    .sticker()\n    .fullScreen()\n    .drawBorder((borderChar, colors) => colors.red(borderChar))\n\n  instructions.add(ui.colors.red(message))\n  if (suggestions.length) {\n    instructions.add('')\n    instructions.add(`${ui.colors.dim('Did you mean?')} ${suggestions.slice(0, 4).join(', ')}`)\n  }\n\n  instructions.getRenderer().logError(instructions.prepare())\n}\n\n/**\n * Validates the metadata of a command to ensure it has all the neccessary\n * properties\n */\nexport function validateCommandMetaData(\n  command: unknown,\n  exportPath: string\n): asserts command is CommandMetaData {\n  if (!command || typeof command !== 'object') {\n    throw new RuntimeException(`Invalid command metadata exported from ${exportPath}`)\n  }\n\n  try {\n    new Validator().validate(command, schema, { throwError: true })\n  } catch (error) {\n    throw new RuntimeException(`Invalid command exported from ${exportPath}. ${error.message}`)\n  }\n}\n\n/**\n * Validates the command class. We do not check it against the \"BaseCommand\"\n * class, because the ace version mis-match could make the validation\n * fail.\n */\nexport function validateCommand<Command extends AbstractBaseCommand>(\n  command: unknown,\n  exportPath: string\n): asserts command is Command {\n  if (typeof command !== 'function' || !command.toString().startsWith('class ')) {\n    throw new RuntimeException(\n      `Invalid command exported from ${exportPath}. Expected command to be a class`\n    )\n  }\n\n  const commandConstructor = command as Function & { serialize: () => unknown }\n  if (typeof commandConstructor.serialize !== 'function') {\n    throw new RuntimeException(\n      `Invalid command exported from ${exportPath}. Expected command to extend the \"BaseCommand\"`\n    )\n  }\n\n  validateCommandMetaData(commandConstructor.serialize(), exportPath)\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nexport const schemaRoot = import.meta.url\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport string from '@poppinss/utils/string'\nimport { TERMINAL_SIZE, wrap } from '@poppinss/cliui/helpers'\n\nimport { ArgumentFormatter } from './argument.js'\nimport type { AllowedInfoValues, CommandMetaData, UIPrimitives } from '../types.js'\n\n/**\n * The command formatter exposes API to format command data for the\n * commands list and the command help.\n */\nexport class CommandFormatter {\n  #command: CommandMetaData\n  #colors: UIPrimitives['colors']\n\n  constructor(command: CommandMetaData, colors: UIPrimitives['colors']) {\n    this.#command = command\n    this.#colors = colors\n  }\n\n  /**\n   * Returns the formatted command name to be displayed in the list\n   * of commands\n   */\n  formatListName(aliases: string[]) {\n    const formattedAliases = aliases.length ? ` ${this.#colors.dim(`(${aliases.join(', ')})`)}` : ''\n    return `  ${this.#colors.green(this.#command.commandName)}${formattedAliases}  `\n  }\n\n  /**\n   * Returns the formatted description of the command\n   */\n  formatDescription() {\n    return this.#command.description || ''\n  }\n\n  /**\n   * Returns multiline command help\n   */\n  formatHelp(binaryName?: AllowedInfoValues, terminalWidth: number = TERMINAL_SIZE): string {\n    const binary = binaryName ? `${binaryName}` : ''\n    if (!this.#command.help) {\n      return ''\n    }\n\n    /**\n     * Normalize help text to be an array of rows\n     */\n    const help = Array.isArray(this.#command.help) ? this.#command.help : [this.#command.help]\n\n    /**\n     * Wrap text when goes over the terminal size\n     */\n    return wrap(\n      help.map((line) => string.interpolate(line, { binaryName: binary })),\n      {\n        startColumn: 2,\n        trimStart: false,\n        endColumn: terminalWidth,\n      }\n    ).join('\\n')\n  }\n\n  /**\n   * Returns the formatted description to be displayed in the list\n   * of commands\n   */\n  formatListDescription() {\n    if (!this.#command.description) {\n      return ''\n    }\n    return this.#colors.dim(this.#command.description)\n  }\n\n  /**\n   * Returns an array of strings, each line contains an individual usage\n   */\n  formatUsage(aliases: string[], binaryName?: AllowedInfoValues): string[] {\n    const binary = binaryName ? `${binaryName} ` : ''\n\n    /**\n     * Display options placeholder for flags\n     */\n    const flags = this.#command.flags.length ? this.#colors.dim('[options]') : ''\n\n    /**\n     * Display a list of named args\n     */\n    const args = this.#command.args\n      .map((arg) => new ArgumentFormatter(arg, this.#colors).formatOption())\n      .join(' ')\n\n    /**\n     * Separator between options placeholder and args\n     */\n    const separator = flags && args ? ` ${this.#colors.dim('[--]')} ` : ''\n\n    const mainUsage = [`  ${binary}${this.#command.commandName} ${flags}${separator}${args}`]\n    return mainUsage.concat(\n      aliases.map((alias) => `  ${binary}${alias} ${flags}${separator}${args}`)\n    )\n  }\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport { Argument, UIPrimitives } from '../types.js'\n\n/**\n * The argument formatter formats an argument as per the http://docopt.org/ specification.\n */\nexport class ArgumentFormatter {\n  #argument: Argument\n  #colors: UIPrimitives['colors']\n\n  constructor(argument: Argument, colors: UIPrimitives['colors']) {\n    this.#argument = argument\n    this.#colors = colors\n  }\n\n  /**\n   * Wraps the optional placeholder on option arguments\n   */\n  #formatArgument(argument: Argument, valuePlaceholder: string) {\n    return argument.required ? `${valuePlaceholder}` : `[${valuePlaceholder}]`\n  }\n\n  /**\n   * Returns formatted description for the argument\n   */\n  formatDescription(): string {\n    const defaultValue = this.#argument.default ? `[default: ${this.#argument.default}]` : ''\n    const separator = defaultValue && this.#argument.description ? ' ' : ''\n    return this.#colors.dim(`${this.#argument.description || ''}${separator}${defaultValue}`)\n  }\n\n  /**\n   * Returns a formatted version of the argument name to be displayed\n   * inside a list\n   */\n  formatListOption(): string {\n    switch (this.#argument.type) {\n      case 'spread':\n        return `  ${this.#colors.green(\n          this.#formatArgument(this.#argument, `${this.#argument.argumentName}...`)\n        )}  `\n      case 'string':\n        return `  ${this.#colors.green(\n          this.#formatArgument(this.#argument, `${this.#argument.argumentName}`)\n        )}  `\n    }\n  }\n\n  /**\n   * Returns a formatted version of the argument name to\n   * be displayed next to usage\n   */\n  formatOption(): string {\n    switch (this.#argument.type) {\n      case 'spread':\n        return this.#colors.dim(\n          `${this.#formatArgument(this.#argument, `<${this.#argument.argumentName}...>`)}`\n        )\n      case 'string':\n        return this.#colors.dim(\n          `${this.#formatArgument(this.#argument, `<${this.#argument.argumentName}>`)}`\n        )\n    }\n  }\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport { BaseCommand } from './base.js'\nimport { args } from '../decorators/args.js'\nimport { flags } from '../decorators/flags.js'\nimport { FlagFormatter } from '../formatters/flag.js'\nimport { ListFormatter } from '../formatters/list.js'\nimport { renderErrorWithSuggestions } from '../helpers.js'\nimport { CommandFormatter } from '../formatters/command.js'\nimport type { CommandMetaData, Flag, ListTable } from '../types.js'\n\n/**\n * The list command is used to view a list of commands\n */\nexport class ListCommand extends BaseCommand {\n  /**\n   * Command metadata\n   */\n  static commandName: string = 'list'\n  static description: string = 'View list of available commands'\n  static help = [\n    'The list command displays a list of all the commands:',\n    '  {{ binaryName }} list',\n    '',\n    'You can also display the commands for a specific namespace:',\n    '  {{ binaryName }} list <namespace...>',\n  ]\n\n  /**\n   * Optional flag to filter list by namespace\n   */\n  @args.spread({\n    description: 'Filter list by namespace',\n    required: false,\n  })\n  declare namespaces?: string[]\n\n  @flags.boolean({ description: 'Get list of commands as JSON' })\n  declare json?: boolean\n\n  /**\n   * Returns a table for an array of commands.\n   */\n  #makeCommandsTable(heading: string, commands: CommandMetaData[]): ListTable {\n    return {\n      heading: this.colors.yellow(heading),\n      columns: commands.map((command) => {\n        const aliases = this.kernel.getCommandAliases(command.commandName)\n        const commandFormatter = new CommandFormatter(command, this.colors)\n\n        return {\n          option: commandFormatter.formatListName(aliases),\n          description: commandFormatter.formatListDescription(),\n        }\n      }),\n    }\n  }\n\n  /**\n   * Returns a table for an array of global options\n   */\n  #makeOptionsTable(heading: string, flagsList: Flag[]): ListTable {\n    return {\n      heading: this.colors.yellow(heading),\n      columns: flagsList.map((flag) => {\n        const flagFormatter = new FlagFormatter(flag, this.colors)\n\n        return {\n          option: flagFormatter.formatOption(),\n          description: flagFormatter.formatDescription(),\n        }\n      }),\n    }\n  }\n\n  /**\n   * Returns an array of tables for all the commands or for mentioned\n   * namespaces only\n   */\n  #getCommandsTables(namespaces?: string[]) {\n    if (namespaces && namespaces.length) {\n      return namespaces.map((namespace) => {\n        return this.#makeCommandsTable(namespace, this.kernel.getNamespaceCommands(namespace))\n      })\n    }\n\n    return [\n      this.#makeCommandsTable('Available commands:', this.kernel.getNamespaceCommands()),\n      ...this.kernel\n        .getNamespaces()\n        .map((namespace) =>\n          this.#makeCommandsTable(namespace, this.kernel.getNamespaceCommands(namespace))\n        ),\n    ]\n  }\n\n  /**\n   * Returns table for the global flags\n   */\n  #getOptionsTable() {\n    if (!this.kernel.flags.length) {\n      return []\n    }\n\n    return [this.#makeOptionsTable('Options:', this.kernel.flags)]\n  }\n\n  /**\n   * Validates the namespaces mentioned via the \"namespaces\"\n   * flag\n   */\n  #validateNamespace(): boolean {\n    if (!this.namespaces) {\n      return true\n    }\n\n    const namespaces = this.kernel.getNamespaces()\n    const unknownNamespace = this.namespaces.find((namespace) => !namespaces.includes(namespace))\n\n    /**\n     * Show error when the namespace is not known\n     */\n    if (unknownNamespace) {\n      renderErrorWithSuggestions(\n        this.ui,\n        `Namespace \"${unknownNamespace}\" is not defined`,\n        this.kernel.getNamespaceSuggestions(unknownNamespace)\n      )\n      return false\n    }\n\n    return true\n  }\n\n  /**\n   * The method is used to render a list of options and commands\n   */\n  protected renderList() {\n    const tables = this.#getOptionsTable().concat(this.#getCommandsTables(this.namespaces))\n\n    new ListFormatter(tables).format().forEach((table) => {\n      this.logger.log('')\n      this.logger.log(table.heading)\n      this.logger.log(table.rows.join('\\n'))\n    })\n  }\n\n  protected renderToJSON() {\n    if (this.namespaces && this.namespaces.length) {\n      return this.namespaces\n        .map((namespace) => {\n          return this.kernel.getNamespaceCommands(namespace)\n        })\n        .flat(1)\n    }\n\n    return this.kernel.getNamespaceCommands().concat(\n      this.kernel\n        .getNamespaces()\n        .map((namespace) => this.kernel.getNamespaceCommands(namespace))\n        .flat(1)\n    )\n  }\n\n  /**\n   * Executed by ace directly\n   */\n  async run() {\n    const hasValidNamespaces = this.#validateNamespace()\n    if (!hasValidNamespaces) {\n      this.exitCode = 1\n      return\n    }\n\n    if (this.json) {\n      this.logger.log(JSON.stringify(this.renderToJSON(), null, 2))\n      return\n    }\n\n    this.renderList()\n  }\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport type { AbstractBaseCommand, CommandMetaData, LoadersContract } from '../types.js'\n\n/**\n * List loader exposes the API to register commands as classes\n */\nexport class ListLoader<Command extends AbstractBaseCommand> implements LoadersContract<Command> {\n  #commands: Command[]\n\n  constructor(commands: Command[]) {\n    this.#commands = commands\n  }\n\n  /**\n   * Returns an array of command's metadata\n   */\n  async getMetaData(): Promise<CommandMetaData[]> {\n    return this.#commands.map((command) => command.serialize())\n  }\n\n  /**\n   * Returns the command class constructor for a given command. Null\n   * is returned when unable to lookup the command\n   */\n  async getCommand(metaData: CommandMetaData): Promise<Command | null> {\n    return this.#commands.find((command) => command.commandName === metaData.commandName) || null\n  }\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport { errors as promptsErrors } from '@poppinss/prompts'\nimport { errors, <PERSON><PERSON> } from '../index.js'\nimport { renderErrorWithSuggestions } from './helpers.js'\n\n/**\n * The base exception handler that is used by default to exception\n * ace exceptions.\n *\n * You can extend this class to custom the exception rendering\n * behavior.\n */\nexport class ExceptionHandler {\n  debug: boolean = true\n\n  /**\n   * Known error codes. For these error, only the error message is\n   * reported using the logger\n   */\n  protected knownErrorCodes: string[] = []\n\n  /**\n   * Internal set of known error codes.\n   */\n  protected internalKnownErrorCode = Object.keys(errors)\n\n  /**\n   * Logs error to stderr using logger\n   */\n  protected logError(error: { message: any } & unknown, kernel: Kernel<any>) {\n    kernel.ui.logger.logError(`${kernel.ui.colors.bgRed().white('  ERROR  ')} ${error.message}`)\n  }\n\n  /**\n   * Pretty prints uncaught error in debug mode\n   */\n  protected async prettyPrintError(error: object) {\n    // @ts-expect-error\n    const { default: youchTerminal } = await import('youch-terminal')\n    const { default: Youch } = await import('youch')\n\n    const youch = new Youch(error, {})\n    console.log(youchTerminal(await youch.toJSON(), { displayShortPath: true }))\n  }\n\n  /**\n   * Renders an exception for the console\n   */\n  async render(error: unknown, kernel: Kernel<any>) {\n    /**\n     * Render non object errors or errors without message property\n     * as a string using the logger\n     */\n    if (typeof error !== 'object' || error === null || !('message' in error)) {\n      this.logError({ message: String(error) }, kernel)\n      return\n    }\n\n    /**\n     * Report command not found error with command suggestions\n     */\n    if (error instanceof errors.E_COMMAND_NOT_FOUND) {\n      renderErrorWithSuggestions(\n        kernel.ui,\n        error.message,\n        kernel.getCommandSuggestions(error.commandName)\n      )\n      return\n    }\n\n    /**\n     * Display prompt cancellation error\n     */\n    if (error instanceof promptsErrors.E_PROMPT_CANCELLED) {\n      this.logError({ message: 'Prompt cancelled' }, kernel)\n      return\n    }\n\n    /**\n     * Known errors should always be reported with a message\n     */\n    if (\n      'code' in error &&\n      typeof error.code === 'string' &&\n      (this.internalKnownErrorCode.includes(error.code) ||\n        this.knownErrorCodes.includes(error.code))\n    ) {\n      this.logError({ message: error.message }, kernel)\n      return\n    }\n\n    /**\n     * Allow errors to be self handled.\n     */\n    if ('render' in error && typeof error.render === 'function') {\n      return error.render(error, kernel)\n    }\n\n    /**\n     * Log error message only when not in debug mode\n     */\n    if (!this.debug) {\n      this.logError({ message: error.message }, kernel)\n      return\n    }\n\n    return this.prettyPrintError(error)\n  }\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nexport { Parser } from './src/parser.js'\nexport { Kernel } from './src/kernel.js'\nexport * as errors from './src/errors.js'\nexport { args } from './src/decorators/args.js'\nexport { flags } from './src/decorators/flags.js'\nexport * as cliHelpers from '@poppinss/cliui/helpers'\nexport { BaseCommand } from './src/commands/base.js'\nexport { HelpCommand } from './src/commands/help.js'\nexport { ListCommand } from './src/commands/list.js'\nexport { FsLoader } from './src/loaders/fs_loader.js'\nexport { ListLoader } from './src/loaders/list_loader.js'\nexport { ExceptionHandler } from './src/exception_handler.js'\nexport { IndexGenerator } from './src/generators/index_generator.js'\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport { TERMINAL_SIZE, wrap } from '@poppinss/cliui/helpers'\n\nimport { BaseCommand } from './base.js'\nimport { args } from '../decorators/args.js'\nimport { ListFormatter } from '../formatters/list.js'\nimport { FlagFormatter } from '../formatters/flag.js'\nimport { renderErrorWithSuggestions } from '../helpers.js'\nimport { CommandFormatter } from '../formatters/command.js'\nimport { ArgumentFormatter } from '../formatters/argument.js'\nimport type { CommandMetaData, ListTable } from '../types.js'\n\n/**\n * The Help command is used to view help for a given command\n */\nexport class HelpCommand extends BaseCommand {\n  /**\n   * Command metadata\n   */\n  static commandName: string = 'help'\n  static description: string = 'View help for a given command'\n\n  /**\n   * The command name argument\n   */\n  @args.string({ description: 'Command name', argumentName: 'command' })\n  declare name: string\n\n  /**\n   * Returns the command arguments table\n   */\n  #makeArgumentsTable(heading: string, command: CommandMetaData): ListTable[] {\n    if (!command.args.length) {\n      return []\n    }\n\n    return [\n      {\n        heading: this.colors.yellow(heading),\n        columns: command.args.map((arg) => {\n          const formatter = new ArgumentFormatter(arg, this.colors)\n          return {\n            option: formatter.formatListOption(),\n            description: formatter.formatDescription(),\n          }\n        }),\n      },\n    ]\n  }\n\n  /**\n   * Returns the commands options table\n   */\n  #makeOptionsTable(heading: string, command: CommandMetaData): ListTable[] {\n    if (!command.flags.length) {\n      return []\n    }\n\n    return [\n      {\n        heading: this.colors.yellow(heading),\n        columns: command.flags.map((flag) => {\n          const formatter = new FlagFormatter(flag, this.colors)\n          return {\n            option: formatter.formatOption(),\n            description: formatter.formatDescription(),\n          }\n        }),\n      },\n    ]\n  }\n\n  /**\n   * Validates the command name to ensure it exists\n   */\n  #validateCommandName(): boolean {\n    const command = this.kernel.getCommand(this.name)\n    if (!command) {\n      renderErrorWithSuggestions(\n        this.ui,\n        `Command \"${this.name}\" is not defined`,\n        this.kernel.getCommandSuggestions(this.name)\n      )\n      return false\n    }\n\n    return true\n  }\n\n  /**\n   * Logs command description\n   */\n  protected renderDescription(command: CommandMetaData) {\n    const formatter = new CommandFormatter(command, this.colors)\n    const description = formatter.formatDescription()\n\n    if (!description) {\n      return\n    }\n\n    this.logger.log('')\n    this.logger.log(this.colors.yellow('Description:'))\n    this.logger.log(\n      wrap([description], {\n        startColumn: 2,\n        trimStart: false,\n        endColumn: TERMINAL_SIZE,\n      }).join('\\n')\n    )\n  }\n\n  /**\n   * Logs command usage\n   */\n  protected renderUsage(command: CommandMetaData) {\n    const aliases = this.kernel.getCommandAliases(command.commandName)\n    const formatter = new CommandFormatter(command, this.colors)\n    const usage = formatter.formatUsage(aliases, this.kernel.info.get('binary')).join('\\n')\n\n    this.logger.log('')\n    this.logger.log(this.colors.yellow('Usage:'))\n    this.logger.log(usage)\n  }\n\n  /**\n   * Logs commands arguments and options tables\n   */\n  protected renderList(command: CommandMetaData) {\n    const tables = this.#makeArgumentsTable('Arguments:', command).concat(\n      this.#makeOptionsTable('Options:', command)\n    )\n\n    new ListFormatter(tables).format().forEach((table) => {\n      this.logger.log('')\n      this.logger.log(table.heading)\n      this.logger.log(table.rows.join('\\n'))\n    })\n  }\n\n  /**\n   * Logs command help text\n   */\n  protected renderHelp(command: CommandMetaData) {\n    const formatter = new CommandFormatter(command, this.colors)\n    const help = formatter.formatHelp(this.kernel.info.get('binary'))\n    if (!help) {\n      return\n    }\n\n    this.logger.log('')\n    this.logger.log(this.colors.yellow('Help:'))\n    this.logger.log(help)\n  }\n\n  /**\n   * Executed by ace directly\n   */\n  async run() {\n    const isValidCommand = this.#validateCommandName()\n    if (!isValidCommand) {\n      this.exitCode = 1\n      return\n    }\n\n    const command = this.kernel.getCommand(this.name)!\n    this.renderDescription(command)\n    this.renderUsage(command)\n    this.renderList(command)\n    this.renderHelp(command)\n  }\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport { fileURLToPath } from 'node:url'\nimport { basename, extname, relative } from 'node:path'\nimport { fsReadAll, importDefault, slash } from '@poppinss/utils'\n\nimport { validateCommand } from '../helpers.js'\nimport type { AbstractBaseCommand, CommandMetaData, LoadersContract } from '../types.js'\n\nconst JS_MODULES = ['.js', '.cjs', '.mjs']\n\n/**\n * Fs loader exposes the API to load commands from a directory. All files\n * ending with \".js\", \".cjs\", \".mjs\", \".ts\" and \".mts\" are considered\n * as commands\n */\nexport class FsLoader<Command extends AbstractBaseCommand> implements LoadersContract<Command> {\n  /**\n   * Absolute path to directory from which to load files\n   */\n  #comandsDirectory: string\n\n  /**\n   * File to ignore files\n   */\n  #filter?: (filePath: string) => boolean\n\n  /**\n   * An array of loaded commands\n   */\n  #commands: { command: Command; filePath: string }[] = []\n\n  constructor(comandsDirectory: string, filter?: (filePath: string) => boolean) {\n    this.#comandsDirectory = comandsDirectory\n    this.#filter = filter\n  }\n\n  /**\n   * Returns a collection of commands. The command value\n   * is unknown and must be validated\n   */\n  async #loadCommands(): Promise<Record<string, unknown>> {\n    const commands: Record<string, unknown> = {}\n\n    /**\n     * Scanning all files\n     */\n    const commandFiles = await fsReadAll(this.#comandsDirectory, {\n      pathType: 'url',\n      ignoreMissingRoot: true,\n      filter: (filePath: string) => {\n        const ext = extname(filePath)\n\n        /**\n         * Ignore files prefixed with _\n         */\n        if (basename(filePath).startsWith('_')) {\n          return false\n        }\n\n        if (JS_MODULES.includes(ext)) {\n          return true\n        }\n\n        if (ext === '.ts' && !filePath.endsWith('.d.ts')) {\n          return true\n        }\n\n        return false\n      },\n    })\n\n    /**\n     * Importing files and validating the exports to have a default\n     * export\n     */\n    for (let file of commandFiles) {\n      /**\n       * Remapping .ts files to .js, otherwise the file cannot imported\n       */\n      if (file.endsWith('.ts')) {\n        file = file.replace(/\\.ts$/, '.js')\n      }\n\n      const relativeFileName = slash(relative(this.#comandsDirectory, fileURLToPath(file)))\n\n      /**\n       * Import file if no filters are defined or the filter\n       * allows the file\n       */\n      if (!this.#filter || this.#filter(relativeFileName)) {\n        commands[relativeFileName] = await importDefault(() => import(file), relativeFileName)\n      }\n    }\n\n    return commands\n  }\n\n  /**\n   * Returns the metadata of commands\n   */\n  async getMetaData(): Promise<CommandMetaData[]> {\n    const commandsCollection = await this.#loadCommands()\n\n    Object.keys(commandsCollection).forEach((key) => {\n      const command = commandsCollection[key]\n      validateCommand<Command>(command, `\"${key}\" file`)\n      this.#commands.push({ command, filePath: key })\n    })\n\n    return this.#commands.map(({ command, filePath }) => {\n      return Object.assign({}, command.serialize(), { filePath })\n    })\n  }\n\n  /**\n   * Returns the command class constructor for a given command. Null\n   * is returned when unable to lookup the command\n   */\n  async getCommand(metaData: CommandMetaData): Promise<Command | null> {\n    return (\n      this.#commands.find(({ command }) => {\n        return command.commandName === metaData.commandName\n      })?.command || null\n    )\n  }\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport { join } from 'node:path'\nimport { copyFile, mkdir, writeFile } from 'node:fs/promises'\n\nimport { stubsRoot } from '../../stubs/main.js'\nimport { FsLoader } from '../loaders/fs_loader.js'\n\n/**\n * The index generators creates a commands laoder that can be lazily\n * imported.\n *\n * Also, a command.json index file is created that has metadata for all\n * the files. Doing so, speeds up the commands lookup, as we do not\n * have to import all the classes just to find if a command exists\n * or not.\n */\nexport class IndexGenerator {\n  #commandsDir: string\n\n  constructor(commandsDir: string) {\n    this.#commandsDir = commandsDir\n  }\n\n  /**\n   * Generate index\n   */\n  async generate(): Promise<any> {\n    const commandsMetaData = await new FsLoader(this.#commandsDir).getMetaData()\n\n    const indexJSON = JSON.stringify({ commands: commandsMetaData, version: 1 })\n    const indexFile = join(this.#commandsDir, 'commands.json')\n\n    const loaderFile = join(this.#commandsDir, 'main.js')\n    const loaderStub = join(stubsRoot, 'commands_loader.stub')\n\n    const loaderTypes = join(this.#commandsDir, 'main.d.ts')\n    const loaderTypesStub = join(stubsRoot, 'commands_loader_types.stub')\n\n    await mkdir(this.#commandsDir, { recursive: true })\n    console.log(`artifacts directory: ${this.#commandsDir}`)\n\n    await writeFile(indexFile, indexJSON)\n    console.log('create commands.json')\n\n    await copyFile(loaderStub, loaderFile)\n    console.log('create main.js')\n\n    await copyFile(loaderTypesStub, loaderTypes)\n    console.log('create main.d.ts')\n  }\n}\n", "/*\n * @adonisjs/ace\n *\n * (c) AdonisJS\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\n\nimport { getDirname } from '@poppinss/utils'\n\nexport const stubsRoot = getDirname(import.meta.url)\n"], "mappings": ";;;;;;;;;;;;;;;;AASA,OAAO,iBAAiB;;;ACOjB,IAAM,aAAqC;AAAA,EAChD,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,4BAA4B;AAAA,EAC5B,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,2BAA2B;AAC7B;;;ADNO,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA,EAIlB;AAAA,EAKA,YAAY,SAGT;AACD,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,MAAyB;AACnC,WAAO,YAAY,MAAM,EAAE,GAAG,KAAK,SAAS,oBAAoB,eAAe,WAAW,CAAC;AAAA,EAC7F;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,QAA0C;AAC1D,UAAM,eAAyB,CAAC;AAEhC,aAAS,OAAO,OAAO,KAAK,MAAM,GAAG;AACnC,UAAI,CAAC,KAAK,SAAS,mBAAmB,IAAI,SAAS,GAAG,GAAG;AACvD,qBAAa,KAAK,GAAG;AAAA,MACvB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,cAAyC;AACvD,QAAI,kBAAkB;AAEtB,UAAM,SAAS,KAAK,SAAS,uBAAuB,IAAI,CAAC,QAAQ,UAAU;AACzE,UAAI,OAAO,SAAS,UAAU;AAC5B,YAAIA,SAA2B,aAAa,EAAE,MAAM,KAAK;AACzD,0BAAkB,aAAa,EAAE;AAOjC,YAAI,CAACA,OAAM,QAAQ;AACjB,UAAAA,SAAQ,MAAM,QAAQ,OAAO,OAAO,IAChC,OAAO,UACP,OAAO,YAAY,SACjB,SACA,CAAC,OAAO,OAAO;AAAA,QACvB;AAOA,YAAIA,WAAU,UAAa,OAAO,OAAO;AACvC,UAAAA,SAAQ,OAAO,MAAMA,MAAK;AAAA,QAC5B;AAEA,eAAOA;AAAA,MACT;AAEA,UAAI,QAAQ,aAAa,EAAE,KAAK;AAChC,wBAAkB,QAAQ;AAU1B,UAAI,UAAU,QAAW;AACvB,gBAAQ,OAAO;AAAA,MACjB;AAOA,UAAI,UAAU,UAAa,OAAO,OAAO;AACvC,gBAAQ,OAAO,MAAM,KAAK;AAAA,MAC5B;AAEA,aAAO;AAAA,IACT,CAAC;AAED,UAAM,EAAE,KAAKC,OAAM,MAAM,GAAG,GAAG,KAAK,IAAI;AAExC,WAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU,CAAC;AAAA,MACX,GAAGA,MAAK,MAAM,oBAAoB,KAAK,IAAI,eAAe;AAAA,MAC1D,cAAc,KAAK,kBAAkB,IAAI;AAAA,MACzC,OAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,MAAyB;AAC7B,WAAO,KAAK,gBAAgB,KAAK,YAAY,IAAI,CAAC;AAAA,EACpD;AACF;;;AEpIA,OAAO,WAAW;AAClB,SAAS,aAAa;AACtB,SAAS,cAAc;AACvB,SAAS,gBAAgB;AACzB,SAAS,oBAAAC,yBAAwB;;;ACJjC,SAAS,gBAAgB;AAEzB,IAAO,gBAAQ,SAAS,cAAc;;;ACXtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,SAAS,cAAc;AACvB,SAAS,aAAa,iBAAiB;AAEhC,IAAM,qBAAqB,OAAO;AAKlC,IAAM,yBAAyB;AAAA,EACpC;AAAA,EACA;AACF;AAKO,IAAM,sBAAsB,MAAM,wBAAwB,UAAU;AAAA,EACzE,OAAO,SAAiB;AAAA,EACxB;AAAA,EACA,YAAYC,OAAyB;AACnC,UAAM,YAAYA,MAAK,CAAC,CAAC,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5E,SAAK,cAAcA,MAAK,CAAC;AAAA,EAC3B;AACF;AAKO,IAAM,iBAAiB;AAAA,EAC5B;AAAA,EACA;AACF;AAKO,IAAM,uBAAuB;AAAA,EAClC;AAAA,EACA;AACF;AAKO,IAAM,gBAAgB;AAAA,EAC3B;AAAA,EACA;AACF;AAKO,IAAM,sBAAsB;AAAA,EACjC;AAAA,EACA;AACF;AAKO,IAAM,iBAAiB;AAAA,EAC5B;AAAA,EACA;AACF;AAKO,IAAM,iBAAiB;AAAA,EAC5B;AAAA,EACA;AACF;;;ACvEA,SAAS,eAAe;AACxB,OAAO,YAAY;AACnB,OAAO,eAAe;AACtB,OAAO,YAAY;AACnB,SAAS,sBAAsB;AAG/B,SAAS,sBAAsB,iCAAiC;AAoBzD,IAAM,cAAN,cAA0B,UAAU;AAAA,EAgazC,YACY,QACA,QACH,IACA,QACP;AACA,UAAM;AALI;AACA;AACH;AACA;AAAA,EAGT;AAAA,EAtaA,OAAO,SAAkB;AAAA;AAAA;AAAA;AAAA,EAKzB,OAAO;AAAA;AAAA;AAAA;AAAA,EAKP,OAAO;AAAA;AAAA;AAAA;AAAA,EAKP,OAAO;AAAA;AAAA;AAAA;AAAA,EAKP,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAMP,OAAO;AAAA;AAAA;AAAA;AAAA,EAKP,OAAO;AAAA;AAAA;AAAA;AAAA,EAKP,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAMP,OAAO,OAAO;AACZ,QAAI,OAAO,OAAO,MAAM,QAAQ,KAAK,KAAK,WAAW,MAAM;AACzD;AAAA,IACF;AAEA,SAAK,SAAS;AACd,yBAAqB,MAAM,QAAQ,EAAE,cAAc,CAAC,GAAG,UAAU,UAAU,CAAC;AAC5E,yBAAqB,MAAM,SAAS,EAAE,cAAc,CAAC,GAAG,UAAU,UAAU,CAAC;AAC7E,yBAAqB,MAAM,WAAW,EAAE,cAAc,CAAC,GAAG,UAAU,UAAU,CAAC;AAC/E,yBAAqB,MAAM,eAAe,EAAE,cAAc,IAAI,UAAU,UAAU,CAAC;AACnF,yBAAqB,MAAM,eAAe,EAAE,cAAc,IAAI,UAAU,UAAU,CAAC;AACnF,yBAAqB,MAAM,QAAQ,EAAE,cAAc,IAAI,UAAU,UAAU,CAAC;AAC5E,yBAAqB,MAAM,WAAW;AAAA,MACpC,cAAc,EAAE,YAAY,OAAO,mBAAmB,MAAM;AAAA,MAC5D,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,eAAe,MAAc,SAA4D;AAC9F,SAAK,KAAK;AACV,UAAM,MAAM,EAAE,MAAM,cAAc,OAAO,SAAS,IAAI,GAAG,UAAU,MAAM,GAAG,QAAQ;AACpF,UAAM,UAAU,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC;AAK9C,QAAI,CAAC,IAAI,MAAM;AACb,YAAM,IAAI;AAAA,QACR,2BAA2B,KAAK,IAAI,IAAI,IAAI;AAAA,MAC9C;AAAA,IACF;AAKA,QAAI,WAAW,QAAQ,SAAS,UAAU;AACxC,YAAM,IAAI;AAAA,QACR,2BAA2B,KAAK,IAAI,IAAI,IAAI,4BAA4B,KAAK,IAAI,IAAI,QAAQ,IAAI;AAAA,MACnG;AAAA,IACF;AAMA,QAAI,IAAI,YAAY,WAAW,QAAQ,aAAa,OAAO;AACzD,YAAM,IAAI;AAAA,QACR,oCAAoC,KAAK,IAAI,IAAI,IAAI,8BAA8B,KAAK,IAAI,IAAI,QAAQ,IAAI;AAAA,MAC9G;AAAA,IACF;AAEA,QAAI,cAAM,SAAS;AACjB,oBAAM,gCAAgC,KAAK,WAAW,KAAK,IAAI,GAAG;AAAA,IACpE;AAEA,SAAK,KAAK,KAAK,GAAG;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,WACL,MACA,SACA;AACA,SAAK,KAAK;AACV,UAAM,OAAO,EAAE,MAAM,UAAU,OAAO,SAAS,IAAI,GAAG,UAAU,OAAO,GAAG,QAAQ;AAKlF,QAAI,CAAC,KAAK,MAAM;AACd,YAAM,IAAI;AAAA,QACR,uBAAuB,KAAK,IAAI,IAAI,IAAI;AAAA,MAC1C;AAAA,IACF;AAEA,QAAI,cAAM,SAAS;AACjB,oBAAM,iCAAiC,MAAM,WAAW,KAAK,IAAI,GAAG;AAAA,IACtE;AAEA,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,iBAAiB,SAGtB;AACA,SAAK,KAAK;AAEV,UAAM,yBAAmD,KAAK,KAAK,IAAI,CAAC,QAAQ;AAC9E,aAAO;AAAA,QACL,MAAM,IAAI;AAAA,QACV,SAAS,IAAI;AAAA,QACb,OAAO,IAAI;AAAA,MACb;AAAA,IACF,CAAC;AAED,UAAM,qBAAmD,OAAO;AAAA,MAC9D;AAAA,QACE,KAAK,CAAC;AAAA,QACN,QAAQ,CAAC;AAAA,QACT,SAAS,CAAC;AAAA,QACV,OAAO,CAAC;AAAA,QACR,QAAQ,CAAC;AAAA,QACT,OAAO,CAAC;AAAA,QACR,OAAO,CAAC;AAAA,QACR,QAAQ,CAAC;AAAA,QACT,SAAS,CAAC;AAAA,MACZ;AAAA,MACA;AAAA,IACF;AAEA,SAAK,MAAM,QAAQ,CAAC,SAAS;AAC3B,yBAAmB,IAAI,KAAK,KAAK,QAAQ;AAEzC,UAAI,KAAK,OAAO;AACd,2BAAmB,MAAM,KAAK,QAAQ,IAAI,KAAK;AAAA,MACjD;AACA,UAAI,KAAK,OAAO;AACd,2BAAmB,OAAO,KAAK,QAAQ,IAAI,KAAK;AAAA,MAClD;AACA,UAAI,KAAK,YAAY,QAAW;AAC9B,2BAAmB,QAAQ,KAAK,QAAQ,IAAI,KAAK;AAAA,MACnD;AAEA,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACH,6BAAmB,OAAO,KAAK,KAAK,QAAQ;AAC5C;AAAA,QACF,KAAK;AACH,6BAAmB,QAAQ,KAAK,KAAK,QAAQ;AAC7C;AAAA,QACF,KAAK;AACH,6BAAmB,OAAO,KAAK,KAAK,QAAQ;AAC5C;AAAA,QACF,KAAK;AACH,6BAAmB,MAAM,KAAK,KAAK,QAAQ;AAC3C;AAAA,MACJ;AAAA,IACF,CAAC;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,YAA6B;AAClC,SAAK,KAAK;AACV,QAAI,CAAC,KAAK,aAAa;AACrB,YAAM,IAAW,uBAAuB,CAAC,KAAK,IAAI,CAAC;AAAA,IACrD;AAEA,UAAM,CAAC,WAAW,IAAI,IAAI,KAAK,YAAY,MAAM,GAAG;AAEpD,WAAO;AAAA,MACL,aAAa,KAAK;AAAA,MAClB,aAAa,KAAK;AAAA,MAClB,MAAM,KAAK;AAAA,MACX,WAAW,OAAO,YAAY;AAAA,MAC9B,SAAS,KAAK;AAAA,MACd,OAAO,KAAK,MAAM,IAAI,CAAC,SAAS;AAC9B,cAAM,EAAE,OAAO,GAAG,KAAK,IAAI;AAC3B,eAAO;AAAA,MACT,CAAC;AAAA,MACD,MAAM,KAAK,KAAK,IAAI,CAAC,QAAQ;AAC3B,cAAM,EAAE,OAAO,GAAG,KAAK,IAAI;AAC3B,eAAO;AAAA,MACT,CAAC;AAAA,MACD,SAAS,KAAK;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,SAAS,cAA4B;AAC1C,SAAK,KAAK;AAKV,SAAK,KAAK,QAAQ,CAAC,KAAK,UAAU;AAChC,YAAM,QAAQ,aAAa,KAAK,KAAK;AACrC,YAAM,qBAAqB,UAAU;AAErC,UAAI,IAAI,YAAY,CAAC,oBAAoB;AACvC,cAAM,IAAW,cAAc,CAAC,IAAI,IAAI,CAAC;AAAA,MAC3C;AAEA,UAAI,sBAAsB,CAAC,IAAI,oBAAoB,UAAU,MAAM,CAAC,MAAM,SAAS;AACjF,YAAI,cAAM,SAAS;AACjB,wBAAM,8CAA8C,OAAO,IAAI,IAAI;AAAA,QACrE;AAEA,cAAM,IAAW,oBAAoB,CAAC,IAAI,IAAI,CAAC;AAAA,MACjD;AAAA,IACF,CAAC;AAKD,QAAI,CAAC,KAAK,QAAQ,qBAAqB,aAAa,aAAa,QAAQ;AACvE,YAAM,aAAa,aAAa,aAAa,CAAC;AAC9C,YAAM,iBAAiB,WAAW,WAAW,IAAI,IAAI,UAAU,KAAK,KAAK,UAAU;AACnF,YAAM,IAAW,eAAe,CAAC,cAAc,CAAC;AAAA,IAClD;AAKA,SAAK,MAAM,QAAQ,CAAC,SAAS;AAC3B,YAAM,mBAAmB,OAAO,OAAO,aAAa,OAAO,KAAK,QAAQ;AACxE,YAAM,QAAQ,aAAa,MAAM,KAAK,QAAQ;AAK9C,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AAIH,cAAI,KAAK,YAAY,CAAC,kBAAkB;AACtC,kBAAM,IAAW,eAAe,CAAC,KAAK,QAAQ,CAAC;AAAA,UACjD;AACA;AAAA,QACF,KAAK;AAIH,cAAI,KAAK,YAAY,CAAC,kBAAkB;AACtC,kBAAM,IAAW,eAAe,CAAC,KAAK,QAAQ,CAAC;AAAA,UACjD;AAQA,cAAI,oBAAoB,UAAU,QAAW;AAC3C,kBAAM,IAAW,qBAAqB,CAAC,KAAK,QAAQ,CAAC;AAAA,UACvD;AAEA,cAAI,OAAO,MAAM,KAAK,GAAG;AACvB,kBAAM,IAAW,eAAe,CAAC,KAAK,UAAU,SAAS,CAAC;AAAA,UAC5D;AACA;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAIH,cAAI,KAAK,YAAY,CAAC,kBAAkB;AACtC,kBAAM,IAAW,eAAe,CAAC,KAAK,QAAQ,CAAC;AAAA,UACjD;AAUA,cAAI,oBAAoB,CAAC,KAAK,oBAAoB,UAAU,MAAM,CAAC,MAAM,SAAS;AAChF,gBAAI,cAAM,SAAS;AACjB,4BAAM,+CAA+C,OAAO,KAAK,IAAI;AAAA,YACvE;AAEA,kBAAM,IAAW,qBAAqB,CAAC,KAAK,QAAQ,CAAC;AAAA,UACvD;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKU,WAAoB;AAAA;AAAA;AAAA;AAAA,EAK9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,WAAO,KAAK,GAAG;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAiB;AACnB,WAAO,KAAK,GAAG;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAkB;AACpB,WAAO,KAAK,OAAO,eAAe,MAAM;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AAChB,WAAQ,KAAK,YAAmC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACZ,WAAQ,KAAK,YAAmC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,WAAQ,KAAK,YAAmC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACV,WAAQ,KAAK,YAAmC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,UAAU;AACR,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AAEA,UAAM,qBAAqB,KAAK;AAKhC,uBAAmB,KAAK,QAAQ,CAAC,KAAK,UAAU;AAC9C,aAAO,eAAe,MAAM,IAAI,MAAM;AAAA,QACpC,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,QAC7B,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,cAAc;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAKD,uBAAmB,MAAM,QAAQ,CAAC,SAAS;AACzC,aAAO,eAAe,MAAM,KAAK,MAAM;AAAA,QACrC,OAAO,KAAK,OAAO,MAAM,KAAK,QAAQ;AAAA,QACtC,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,cAAc;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAED,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAAO,GAAwB;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA,EAKtC,MAAM,OAAO;AACX,SAAK,QAAQ;AAEb,QAAI;AACF,WAAK,SAAS,MAAM,KAAK,IAAI;AAC7B,WAAK,WAAW,KAAK,YAAY;AACjC,aAAO,KAAK;AAAA,IACd,SAAS,OAAO;AACd,WAAK,QAAQ;AACb,WAAK,WAAW,KAAK,YAAY;AACjC,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,WAAO;AAAA,MACL,aAAc,KAAK,YAAmC;AAAA,MACtD,SAAU,KAAK,YAAmC;AAAA,MAClD,MAAM,KAAK,OAAO;AAAA,MAClB,OAAO,KAAK,OAAO;AAAA,MACnB,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,UAAU,KAAK;AAAA,IACjB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,MAAc;AAC3B,QAAI,KAAK,aAAa,MAAM;AAC1B,YAAM,IAAI,eAAe;AAAA,QACvB,SAAS,aAAa,KAAK,WAAW,uCAAuC,IAAI;AAAA,QACjF,QAAQ,KAAK;AAAA,QACb,UAAU;AAAA,QACV,UAAU;AAAA,QACV,cAAc,KAAK;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,MAAc;AAC9B,QAAI,KAAK,aAAa,MAAM;AAC1B,YAAM,IAAI,eAAe;AAAA,QACvB,SAAS,aAAa,KAAK,WAAW,0CAA0C,KAAK,QAAQ;AAAA,QAC7F,cAAc,KAAK;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,WAAO,KAAK,eAAe,CAAC;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO,KAAK,kBAAkB,CAAC;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,SAAiB,QAA8B;AACvD,UAAM,OAAO,KAAK,OAAO,QAAQ;AACjC,UAAM,cAAc,KAAK,IAAI,CAAC,QAAQ,IAAI,OAAO;AACjD,UAAM,cAAc,KAAK,KAAK,CAAC,QAAQ,IAAI,YAAY,OAAO;AAK9D,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,eAAe;AAAA,QACvB,SAAS,oCAAoC,QAAQ,OAAO,CAAC;AAAA,QAC7D,QAAQ;AAAA,QACR,UAAU,CAAC,OAAO;AAAA,QAClB,UAAU;AAAA,QACV,cAAc,KAAK;AAAA,MACrB,CAAC;AAAA,IACH;AAKA,QAAI,UAAU,YAAY,WAAW,QAAQ;AAC3C,YAAM,IAAI,eAAe;AAAA,QACvB,SAAS,qCAAqC,QAAQ,MAAM,CAAC,sBAAsB;AAAA,UACjF,YAAY;AAAA,QACd,CAAC;AAAA,QACD,QAAQ,YAAY;AAAA,QACpB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,cAAc,KAAK;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,eAAuB,QAA8B;AACpE,UAAM,OAAO,KAAK,OAAO,QAAQ;AACjC,UAAM,cAAc,KAAK,KAAK,CAAC,QAAQ,cAAc,KAAK,IAAI,OAAO,CAAC;AAKtE,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,eAAe;AAAA,QACvB,SAAS,kCAAkC,QAAQ,aAAa,CAAC;AAAA,QACjE,cAAc,KAAK;AAAA,MACrB,CAAC;AAAA,IACH;AAKA,QAAI,UAAU,YAAY,WAAW,QAAQ;AAC3C,YAAM,IAAI,eAAe;AAAA,QACvB,SAAS,qCAAqC,QAAQ,MAAM,CAAC,sBAAsB;AAAA,UACjF,YAAY;AAAA,QACd,CAAC;AAAA,QACD,QAAQ,YAAY;AAAA,QACpB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,cAAc,KAAK;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,MAAkB;AAChC,UAAM,OAAO,KAAK,OAAO,QAAQ;AACjC,UAAM,qBAAqB,KAAK,MAAM,CAAC,QAAQ;AAC7C,YAAM,iBAAiB,IAAI,KAAK,GAAG;AACnC,aAAO,CAAC,CAAC,KAAK,KAAK,CAAC,QAAQ,IAAI,YAAY,cAAc;AAAA,IAC5D,CAAC;AAED,QAAI,CAAC,oBAAoB;AACvB,YAAM,IAAI,eAAe;AAAA,QACvB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,cAAc,KAAK;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;AC3oBO,IAAM,OAAO;AAAA;AAAA;AAAA;AAAA,EAIlB,OAAsB,SAAuD;AAC3E,WAAO,SAAS,OACd,QACA,cACA;AACA,YAAM,UAAU,OAAO;AACvB,cAAQ,eAAe,cAAc,EAAE,GAAG,SAAS,MAAM,SAAS,CAAC;AAAA,IACrE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,OAAoC,SAAuD;AACzF,WAAO,SAAS,OACd,QACA,cACA;AACA,YAAM,UAAU,OAAO;AACvB,cAAQ,eAAe,cAAc,EAAE,GAAG,SAAS,MAAM,SAAS,CAAC;AAAA,IACrE;AAAA,EACF;AACF;;;AC1BO,IAAM,QAAQ;AAAA;AAAA;AAAA;AAAA,EAInB,OAAsB,SAAmD;AACvE,WAAO,SAAS,OACd,QACA,cACA;AACA,YAAM,UAAU,OAAO;AACvB,cAAQ,WAAW,cAAc,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC;AAAA,IACjE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,QAAwB,SAAoD;AAC1E,WAAO,SAAS,OACd,QACA,cACA;AACA,YAAM,UAAU,OAAO;AACvB,cAAQ,WAAW,cAAc,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC;AAAA,IAClE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,OAAsB,SAAmD;AACvE,WAAO,SAAS,OACd,QACA,cACA;AACA,YAAM,UAAU,OAAO;AACvB,cAAQ,WAAW,cAAc,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC;AAAA,IACjE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAqC,SAAkD;AACrF,WAAO,SAAS,OACd,QACA,cACA;AACA,YAAM,UAAU,OAAO;AACvB,cAAQ,WAAW,cAAc,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC;AAAA,IAChE;AAAA,EACF;AACF;;;ACrDO,IAAM,gBAAN,MAAoB;AAAA,EACzB;AAAA,EACA;AAAA,EAEA,YAAY,MAAY,QAAgC;AACtD,SAAK,QAAQ;AACb,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,MAAY,kBAA0B;AACrD,WAAO,KAAK,WAAW,IAAI,gBAAgB,KAAK,KAAK,gBAAgB;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,MAAsB;AACnC,QAAI,CAAC,KAAK,OAAO;AACf,aAAO,CAAC;AAAA,IACV;AAEA,QAAI,OAAO,KAAK,UAAU,UAAU;AAClC,aAAO,CAAC,IAAI,KAAK,KAAK,EAAE;AAAA,IAC1B;AAEA,WAAO,KAAK,MAAM,IAAI,CAAC,UAAU,IAAI,KAAK,EAAE;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,MAA2B;AAC1C,UAAM,QAAQ,KAAK,iBAAiB,MAAM,GAAG,KAAK,SAAS,YAAY,CAAC,KAAK;AAC7E,UAAM,UAAU,KAAK,eAAe,IAAI;AACxC,UAAM,gBAAgB,KAAK,KAAK,QAAQ,GAAG,KAAK;AAEhD,QAAI,QAAQ,QAAQ;AAClB,aAAO,KAAK,KAAK,QAAQ,MAAM,GAAG,QAAQ,KAAK,GAAG,CAAC,KAAK,aAAa,EAAE,CAAC;AAAA,IAC1E;AAEA,WAAO,KAAK,KAAK,QAAQ,MAAM,aAAa,CAAC;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,MAA0B;AAC1C,UAAM,QAAQ,KAAK,iBAAiB,MAAM,GAAG,KAAK,SAAS,YAAY,CAAC,EAAE;AAC1E,UAAM,UAAU,KAAK,eAAe,IAAI;AACxC,UAAM,gBAAgB,KAAK,KAAK,QAAQ,GAAG,KAAK;AAEhD,QAAI,QAAQ,QAAQ;AAClB,aAAO,KAAK,KAAK,QAAQ,MAAM,GAAG,QAAQ,KAAK,GAAG,CAAC,KAAK,aAAa,EAAE,CAAC;AAAA,IAC1E;AAEA,WAAO,KAAK,KAAK,QAAQ,MAAM,aAAa,CAAC;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,MAA0B;AAC3C,UAAM,QAAQ,KAAK,iBAAiB,MAAM,GAAG,KAAK,SAAS,YAAY,CAAC,EAAE;AAC1E,UAAM,UAAU,KAAK,eAAe,IAAI;AACxC,UAAM,gBAAgB,KAAK,KAAK,QAAQ,GAAG,KAAK;AAEhD,QAAI,QAAQ,QAAQ;AAClB,aAAO,KAAK,KAAK,QAAQ,MAAM,GAAG,QAAQ,KAAK,GAAG,CAAC,KAAK,aAAa,EAAE,CAAC;AAAA,IAC1E;AAEA,WAAO,KAAK,KAAK,QAAQ,MAAM,aAAa,CAAC;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,MAA4B;AAC7C,UAAM,UAAU,KAAK,eAAe,IAAI;AACxC,UAAM,iBAAiB,KAAK,2BAA2B,SAAS,KAAK,QAAQ,KAAK;AAClF,UAAM,kBAAkB,KAAK,KAAK,QAAQ,GAAG,cAAc;AAE3D,QAAI,QAAQ,QAAQ;AAClB,aAAO,KAAK,KAAK,QAAQ,MAAM,GAAG,QAAQ,KAAK,GAAG,CAAC,KAAK,eAAe,EAAE,CAAC;AAAA,IAC5E;AAEA,WAAO,KAAK,KAAK,QAAQ,MAAM,eAAe,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,oBAA4B;AAC1B,UAAM,eAAe,KAAK,MAAM,YAAY,SAAY,aAAa,KAAK,MAAM,OAAO,MAAM;AAC7F,UAAM,YAAY,gBAAgB,KAAK,MAAM,cAAc,MAAM;AACjE,WAAO,KAAK,QAAQ,IAAI,GAAG,KAAK,MAAM,eAAe,EAAE,GAAG,SAAS,GAAG,YAAY,EAAE;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA,EAKA,eAAuB;AACrB,YAAQ,KAAK,MAAM,MAAM;AAAA,MACvB,KAAK;AACH,eAAO,KAAK,iBAAiB,KAAK,KAAK;AAAA,MACzC,KAAK;AACH,eAAO,KAAK,kBAAkB,KAAK,KAAK;AAAA,MAC1C,KAAK;AACH,eAAO,KAAK,mBAAmB,KAAK,KAAK;AAAA,MAC3C,KAAK;AACH,eAAO,KAAK,mBAAmB,KAAK,KAAK;AAAA,IAC7C;AAAA,EACF;AACF;;;AC3HA,OAAO,iBAAiB;AACxB,SAAS,SAAS,eAAe,YAAY;AAQtC,IAAM,gBAAN,MAAoB;AAAA,EACzB;AAAA,EACA;AAAA,EAEA,YAAY,QAAqB;AAC/B,SAAK,UAAU;AACf,SAAK,4BAA4B,KAAK;AAAA,MACpC,GAAG,KAAK,QACL,IAAI,CAAC,UAAU,MAAM,QAAQ,IAAI,CAAC,WAAW,YAAY,OAAO,MAAM,CAAC,CAAC,EACxE,KAAK;AAAA,IACV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAkB,eAAiC;AAC9D,UAAM,UAAU;AAAA,MACd,MAAM,QAAQ,IAAI,CAAC,EAAE,OAAO,MAAM,MAAM;AAAA,MACxC,EAAE,UAAU,KAAK,0BAA0B;AAAA,IAC7C;AAEA,UAAM,eAAe;AAAA,MACnB,MAAM,QAAQ,IAAI,CAAC,EAAE,YAAY,MAAM,WAAW;AAAA,MAClD;AAAA,QACE,aAAa,KAAK;AAAA,QAClB,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAAA,IACF;AAEA,WAAO,MAAM,QAAQ,IAAI,CAAC,GAAG,UAAU,GAAG,QAAQ,KAAK,CAAC,GAAG,aAAa,KAAK,CAAC,EAAE;AAAA,EAClF;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,gBAAwB,eAAe;AAC5C,WAAO,KAAK,QAAQ,IAAI,CAAC,UAAU;AACjC,aAAO;AAAA,QACL,SAAS,MAAM;AAAA,QACf,MAAM,KAAK,aAAa,OAAO,aAAa;AAAA,MAC9C;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;ACtDA,SAAS,iBAAiB;AAC1B,SAAS,gBAAgB;AACzB,SAAS,wBAAwB;;;ACF1B,IAAM,aAAa,YAAY;;;ADOtC,IAAM,SAAS,KAAK;AAAA,EAClB,MAAM,SAAS,IAAI,IAAI,kCAAkC,UAAU,GAAG,MAAM;AAC9E;AAKO,SAAS,mBAAmB,MAAc,MAAc;AAC7D,MAAI,OAAO,MAAM;AACf,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM;AACf,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAKO,SAAS,2BACd,IACA,SACA,aACA;AACA,QAAM,eAAe,GAClB,QAAQ,EACR,WAAW,EACX,WAAW,CAAC,YAAY,WAAW,OAAO,IAAI,UAAU,CAAC;AAE5D,eAAa,IAAI,GAAG,OAAO,IAAI,OAAO,CAAC;AACvC,MAAI,YAAY,QAAQ;AACtB,iBAAa,IAAI,EAAE;AACnB,iBAAa,IAAI,GAAG,GAAG,OAAO,IAAI,eAAe,CAAC,IAAI,YAAY,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE;AAAA,EAC5F;AAEA,eAAa,YAAY,EAAE,SAAS,aAAa,QAAQ,CAAC;AAC5D;AAMO,SAAS,wBACd,SACA,YACoC;AACpC,MAAI,CAAC,WAAW,OAAO,YAAY,UAAU;AAC3C,UAAM,IAAI,iBAAiB,0CAA0C,UAAU,EAAE;AAAA,EACnF;AAEA,MAAI;AACF,QAAI,UAAU,EAAE,SAAS,SAAS,QAAQ,EAAE,YAAY,KAAK,CAAC;AAAA,EAChE,SAAS,OAAO;AACd,UAAM,IAAI,iBAAiB,iCAAiC,UAAU,KAAK,MAAM,OAAO,EAAE;AAAA,EAC5F;AACF;AAOO,SAAS,gBACd,SACA,YAC4B;AAC5B,MAAI,OAAO,YAAY,cAAc,CAAC,QAAQ,SAAS,EAAE,WAAW,QAAQ,GAAG;AAC7E,UAAM,IAAI;AAAA,MACR,iCAAiC,UAAU;AAAA,IAC7C;AAAA,EACF;AAEA,QAAM,qBAAqB;AAC3B,MAAI,OAAO,mBAAmB,cAAc,YAAY;AACtD,UAAM,IAAI;AAAA,MACR,iCAAiC,UAAU;AAAA,IAC7C;AAAA,EACF;AAEA,0BAAwB,mBAAmB,UAAU,GAAG,UAAU;AACpE;;;AE1FA,OAAOC,aAAY;AACnB,SAAS,iBAAAC,gBAAe,QAAAC,aAAY;;;ACI7B,IAAM,oBAAN,MAAwB;AAAA,EAC7B;AAAA,EACA;AAAA,EAEA,YAAY,UAAoB,QAAgC;AAC9D,SAAK,YAAY;AACjB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,UAAoB,kBAA0B;AAC5D,WAAO,SAAS,WAAW,GAAG,gBAAgB,KAAK,IAAI,gBAAgB;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA,EAKA,oBAA4B;AAC1B,UAAM,eAAe,KAAK,UAAU,UAAU,aAAa,KAAK,UAAU,OAAO,MAAM;AACvF,UAAM,YAAY,gBAAgB,KAAK,UAAU,cAAc,MAAM;AACrE,WAAO,KAAK,QAAQ,IAAI,GAAG,KAAK,UAAU,eAAe,EAAE,GAAG,SAAS,GAAG,YAAY,EAAE;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAA2B;AACzB,YAAQ,KAAK,UAAU,MAAM;AAAA,MAC3B,KAAK;AACH,eAAO,KAAK,KAAK,QAAQ;AAAA,UACvB,KAAK,gBAAgB,KAAK,WAAW,GAAG,KAAK,UAAU,YAAY,KAAK;AAAA,QAC1E,CAAC;AAAA,MACH,KAAK;AACH,eAAO,KAAK,KAAK,QAAQ;AAAA,UACvB,KAAK,gBAAgB,KAAK,WAAW,GAAG,KAAK,UAAU,YAAY,EAAE;AAAA,QACvE,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAuB;AACrB,YAAQ,KAAK,UAAU,MAAM;AAAA,MAC3B,KAAK;AACH,eAAO,KAAK,QAAQ;AAAA,UAClB,GAAG,KAAK,gBAAgB,KAAK,WAAW,IAAI,KAAK,UAAU,YAAY,MAAM,CAAC;AAAA,QAChF;AAAA,MACF,KAAK;AACH,eAAO,KAAK,QAAQ;AAAA,UAClB,GAAG,KAAK,gBAAgB,KAAK,WAAW,IAAI,KAAK,UAAU,YAAY,GAAG,CAAC;AAAA,QAC7E;AAAA,IACJ;AAAA,EACF;AACF;;;ADrDO,IAAM,mBAAN,MAAuB;AAAA,EAC5B;AAAA,EACA;AAAA,EAEA,YAAY,SAA0B,QAAgC;AACpE,SAAK,WAAW;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,SAAmB;AAChC,UAAM,mBAAmB,QAAQ,SAAS,IAAI,KAAK,QAAQ,IAAI,IAAI,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK;AAC9F,WAAO,KAAK,KAAK,QAAQ,MAAM,KAAK,SAAS,WAAW,CAAC,GAAG,gBAAgB;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,WAAO,KAAK,SAAS,eAAe;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,YAAgC,gBAAwBC,gBAAuB;AACxF,UAAM,SAAS,aAAa,GAAG,UAAU,KAAK;AAC9C,QAAI,CAAC,KAAK,SAAS,MAAM;AACvB,aAAO;AAAA,IACT;AAKA,UAAM,OAAO,MAAM,QAAQ,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS,OAAO,CAAC,KAAK,SAAS,IAAI;AAKzF,WAAOC;AAAA,MACL,KAAK,IAAI,CAAC,SAASC,QAAO,YAAY,MAAM,EAAE,YAAY,OAAO,CAAC,CAAC;AAAA,MACnE;AAAA,QACE,aAAa;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAAA,IACF,EAAE,KAAK,IAAI;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB;AACtB,QAAI,CAAC,KAAK,SAAS,aAAa;AAC9B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ,IAAI,KAAK,SAAS,WAAW;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,SAAmB,YAA0C;AACvE,UAAM,SAAS,aAAa,GAAG,UAAU,MAAM;AAK/C,UAAMC,SAAQ,KAAK,SAAS,MAAM,SAAS,KAAK,QAAQ,IAAI,WAAW,IAAI;AAK3E,UAAMC,QAAO,KAAK,SAAS,KACxB,IAAI,CAAC,QAAQ,IAAI,kBAAkB,KAAK,KAAK,OAAO,EAAE,aAAa,CAAC,EACpE,KAAK,GAAG;AAKX,UAAM,YAAYD,UAASC,QAAO,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,MAAM;AAEpE,UAAM,YAAY,CAAC,KAAK,MAAM,GAAG,KAAK,SAAS,WAAW,IAAID,MAAK,GAAG,SAAS,GAAGC,KAAI,EAAE;AACxF,WAAO,UAAU;AAAA,MACf,QAAQ,IAAI,CAAC,UAAU,KAAK,MAAM,GAAG,KAAK,IAAID,MAAK,GAAG,SAAS,GAAGC,KAAI,EAAE;AAAA,IAC1E;AAAA,EACF;AACF;;;AEzFO,IAAM,cAAN,cAA0B,YAAY;AAAA;AAAA;AAAA;AAAA,EAI3C,OAAO,cAAsB;AAAA,EAC7B,OAAO,cAAsB;AAAA,EAC7B,OAAO,OAAO;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAiBA,mBAAmB,SAAiB,UAAwC;AAC1E,WAAO;AAAA,MACL,SAAS,KAAK,OAAO,OAAO,OAAO;AAAA,MACnC,SAAS,SAAS,IAAI,CAAC,YAAY;AACjC,cAAM,UAAU,KAAK,OAAO,kBAAkB,QAAQ,WAAW;AACjE,cAAM,mBAAmB,IAAI,iBAAiB,SAAS,KAAK,MAAM;AAElE,eAAO;AAAA,UACL,QAAQ,iBAAiB,eAAe,OAAO;AAAA,UAC/C,aAAa,iBAAiB,sBAAsB;AAAA,QACtD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,SAAiB,WAA8B;AAC/D,WAAO;AAAA,MACL,SAAS,KAAK,OAAO,OAAO,OAAO;AAAA,MACnC,SAAS,UAAU,IAAI,CAAC,SAAS;AAC/B,cAAM,gBAAgB,IAAI,cAAc,MAAM,KAAK,MAAM;AAEzD,eAAO;AAAA,UACL,QAAQ,cAAc,aAAa;AAAA,UACnC,aAAa,cAAc,kBAAkB;AAAA,QAC/C;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,YAAuB;AACxC,QAAI,cAAc,WAAW,QAAQ;AACnC,aAAO,WAAW,IAAI,CAAC,cAAc;AACnC,eAAO,KAAK,mBAAmB,WAAW,KAAK,OAAO,qBAAqB,SAAS,CAAC;AAAA,MACvF,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,MACL,KAAK,mBAAmB,uBAAuB,KAAK,OAAO,qBAAqB,CAAC;AAAA,MACjF,GAAG,KAAK,OACL,cAAc,EACd;AAAA,QAAI,CAAC,cACJ,KAAK,mBAAmB,WAAW,KAAK,OAAO,qBAAqB,SAAS,CAAC;AAAA,MAChF;AAAA,IACJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,QAAI,CAAC,KAAK,OAAO,MAAM,QAAQ;AAC7B,aAAO,CAAC;AAAA,IACV;AAEA,WAAO,CAAC,KAAK,kBAAkB,YAAY,KAAK,OAAO,KAAK,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAA8B;AAC5B,QAAI,CAAC,KAAK,YAAY;AACpB,aAAO;AAAA,IACT;AAEA,UAAM,aAAa,KAAK,OAAO,cAAc;AAC7C,UAAM,mBAAmB,KAAK,WAAW,KAAK,CAAC,cAAc,CAAC,WAAW,SAAS,SAAS,CAAC;AAK5F,QAAI,kBAAkB;AACpB;AAAA,QACE,KAAK;AAAA,QACL,cAAc,gBAAgB;AAAA,QAC9B,KAAK,OAAO,wBAAwB,gBAAgB;AAAA,MACtD;AACA,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKU,aAAa;AACrB,UAAM,SAAS,KAAK,iBAAiB,EAAE,OAAO,KAAK,mBAAmB,KAAK,UAAU,CAAC;AAEtF,QAAI,cAAc,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,UAAU;AACpD,WAAK,OAAO,IAAI,EAAE;AAClB,WAAK,OAAO,IAAI,MAAM,OAAO;AAC7B,WAAK,OAAO,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EAEU,eAAe;AACvB,QAAI,KAAK,cAAc,KAAK,WAAW,QAAQ;AAC7C,aAAO,KAAK,WACT,IAAI,CAAC,cAAc;AAClB,eAAO,KAAK,OAAO,qBAAqB,SAAS;AAAA,MACnD,CAAC,EACA,KAAK,CAAC;AAAA,IACX;AAEA,WAAO,KAAK,OAAO,qBAAqB,EAAE;AAAA,MACxC,KAAK,OACF,cAAc,EACd,IAAI,CAAC,cAAc,KAAK,OAAO,qBAAqB,SAAS,CAAC,EAC9D,KAAK,CAAC;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,MAAM;AACV,UAAM,qBAAqB,KAAK,mBAAmB;AACnD,QAAI,CAAC,oBAAoB;AACvB,WAAK,WAAW;AAChB;AAAA,IACF;AAEA,QAAI,KAAK,MAAM;AACb,WAAK,OAAO,IAAI,KAAK,UAAU,KAAK,aAAa,GAAG,MAAM,CAAC,CAAC;AAC5D;AAAA,IACF;AAEA,SAAK,WAAW;AAAA,EAClB;AACF;AAlJU;AAAA,EAJP,KAAK,OAAO;AAAA,IACX,aAAa;AAAA,IACb,UAAU;AAAA,EACZ,CAAC;AAAA,GApBU,YAqBH;AAGA;AAAA,EADP,MAAM,QAAQ,EAAE,aAAa,+BAA+B,CAAC;AAAA,GAvBnD,YAwBH;;;AC/BH,IAAM,aAAN,MAA0F;AAAA,EAC/F;AAAA,EAEA,YAAY,UAAqB;AAC/B,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAA0C;AAC9C,WAAO,KAAK,UAAU,IAAI,CAAC,YAAY,QAAQ,UAAU,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,WAAW,UAAoD;AACnE,WAAO,KAAK,UAAU,KAAK,CAAC,YAAY,QAAQ,gBAAgB,SAAS,WAAW,KAAK;AAAA,EAC3F;AACF;;;AC1BA,SAAS,UAAU,qBAAqB;AAWjC,IAAM,mBAAN,MAAuB;AAAA,EAC5B,QAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMP,kBAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,EAK7B,yBAAyB,OAAO,KAAK,cAAM;AAAA;AAAA;AAAA;AAAA,EAK3C,SAAS,OAAmC,QAAqB;AACzE,WAAO,GAAG,OAAO,SAAS,GAAG,OAAO,GAAG,OAAO,MAAM,EAAE,MAAM,WAAW,CAAC,IAAI,MAAM,OAAO,EAAE;AAAA,EAC7F;AAAA;AAAA;AAAA;AAAA,EAKA,MAAgB,iBAAiB,OAAe;AAE9C,UAAM,EAAE,SAAS,cAAc,IAAI,MAAM,OAAO,gBAAgB;AAChE,UAAM,EAAE,SAAS,MAAM,IAAI,MAAM,OAAO,OAAO;AAE/C,UAAM,QAAQ,IAAI,MAAM,OAAO,CAAC,CAAC;AACjC,YAAQ,IAAI,cAAc,MAAM,MAAM,OAAO,GAAG,EAAE,kBAAkB,KAAK,CAAC,CAAC;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO,OAAgB,QAAqB;AAKhD,QAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,EAAE,aAAa,QAAQ;AACxE,WAAK,SAAS,EAAE,SAAS,OAAO,KAAK,EAAE,GAAG,MAAM;AAChD;AAAA,IACF;AAKA,QAAI,iBAAiB,eAAO,qBAAqB;AAC/C;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO,sBAAsB,MAAM,WAAW;AAAA,MAChD;AACA;AAAA,IACF;AAKA,QAAI,iBAAiB,cAAc,oBAAoB;AACrD,WAAK,SAAS,EAAE,SAAS,mBAAmB,GAAG,MAAM;AACrD;AAAA,IACF;AAKA,QACE,UAAU,SACV,OAAO,MAAM,SAAS,aACrB,KAAK,uBAAuB,SAAS,MAAM,IAAI,KAC9C,KAAK,gBAAgB,SAAS,MAAM,IAAI,IAC1C;AACA,WAAK,SAAS,EAAE,SAAS,MAAM,QAAQ,GAAG,MAAM;AAChD;AAAA,IACF;AAKA,QAAI,YAAY,SAAS,OAAO,MAAM,WAAW,YAAY;AAC3D,aAAO,MAAM,OAAO,OAAO,MAAM;AAAA,IACnC;AAKA,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,SAAS,EAAE,SAAS,MAAM,QAAQ,GAAG,MAAM;AAChD;AAAA,IACF;AAEA,WAAO,KAAK,iBAAiB,KAAK;AAAA,EACpC;AACF;;;AdjEO,IAAMC,UAAN,MAAM,QAA4C;AAAA,EACvD,eAEI,IAAI,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB,OAAO,kBAAwD;AAAA,IAC7D,OAAO,SAAS,YAAY,QAAQ;AAClC,aAAO,IAAI,QAAQ,QAAQ,YAAY,OAAO,IAAI,OAAO,MAAM;AAAA,IACjE;AAAA,IACA,IAAI,SAAS;AACX,aAAO,QAAQ,KAAK;AAAA,IACtB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,iBAAqC;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5C,OAAO,SAAS;AACd,WAAO,IAAI,QAA2B,KAAK,gBAAgB,KAAK,eAAe;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAuD,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAM/D,iBAAqC,cAAc,YAAY;AAAA,IAC7D,OAAO,UAAU;AAAA,MACf,mBAAmB;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA,EAKA,SAMK,IAAI,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAMf;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAsD;AAAA;AAAA;AAAA;AAAA,EAKtD,WAAqF,CAAC;AAAA;AAAA;AAAA;AAAA,EAKtF,cAAwB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASzB,WAAgC,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxC,mBAA0C,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlD,YACE,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAMV;AAAA;AAAA;AAAA;AAAA,EAKA,KAAmB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzB,SAAS,IAAI,OAAO;AAAA;AAAA;AAAA;AAAA,EAKpB,OAAuC,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA,EAK/C,IAAI,QAAqC;AACvC,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA,EAEA,YAAY,gBAAyB,UAAqC;AACxE,SAAK,kBAAkB;AACvB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,MAAgB;AAC3B,UAAM,mBAAmB,KAAK,UAAU,CAAC,UAAU,CAAC,MAAM,WAAW,GAAG,CAAC;AACzE,QAAI,qBAAqB,IAAI;AAC3B,aAAO;AAAA,QACL,UAAU,CAAC;AAAA,QACX,aAAa;AAAA,QACb,aAAa;AAAA,MACf;AAAA,IACF;AAEA,WAAO;AAAA,MACL,UAAU,KAAK,MAAM,GAAG,gBAAgB;AAAA,MACxC,aAAa,KAAK,gBAAgB;AAAA,MAClC,aAAa,KAAK,MAAM,mBAAmB,CAAC;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAA2B,SAAY,MAAmD;AAK9F,UAAM,SAAS,IAAI,OAAO,QAAQ,iBAAiB,CAAC,EAAE,MAAM,IAAI;AAKhE,YAAQ,SAAS,MAAM;AAKvB,UAAM,kBAAkB,MAAM,KAAK,UAAU,OAAO,SAAS,QAAQ,IAAI;AACzE,oBAAgB,QAAQ;AAExB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,MAAyB,aAAqB,MAA0C;AAC5F,UAAM,UAAU,MAAM,KAAK,KAAQ,WAAW;AAK9C,UAAM,kBAAkB,KAAK,iBAAiB,IAAI,WAAW;AAC7D,QAAI,iBAAiB;AACnB,aAAO,gBAAgB,OAAO,IAAI;AAClC,oBAAM,mCAAmC,aAAa,IAAI;AAAA,IAC5D;AAEA,UAAM,kBAAkB,MAAM,KAAK,QAAW,SAAS,IAAI;AAK3D,UAAM,KAAK,OAAO,OAAO,WAAW,EAAE,IAAI,iBAAiB,KAAK;AAChE,UAAM,KAAK,UAAU,IAAI,iBAAiB,IAAI;AAC9C,UAAM,KAAK,OAAO,OAAO,UAAU,EAAE,IAAI,iBAAiB,KAAK;AAE/D,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,UAAU,aAAqB,UAAoB,MAAgB;AACvE,QAAI;AACF,YAAM,UAAU,MAAM,KAAK,KAAK,WAAW;AAK3C,YAAM,kBAAkB,KAAK,iBAAiB,IAAI,WAAW;AAC7D,UAAI,iBAAiB;AACnB,eAAO,gBAAgB,OAAO,IAAI;AAClC,sBAAM,mCAAmC,aAAa,IAAI;AAAA,MAC5D;AAKA,YAAM,SAAS,IAAI;AAAA,QACjB,QAAQ,iBAAiB,KAAK,eAAe,iBAAiB,EAAE,kBAAkB;AAAA,MACpF,EAAE,MAAM,IAAI;AAKZ,aAAO,WAAW;AAKlB,WAAK,eAAe,SAAS,MAAM;AAMnC,UAAI,eAAe;AACnB,eAAS,CAAC,QAAQ,QAAQ,KAAK,KAAK,kBAAkB;AACpD,YAAI,OAAO,MAAM,MAAM,MAAM,QAAW;AACtC,wBAAM,kCAAkC,MAAM;AAC9C,yBAAe,MAAM,SAAS,SAAS,MAAM,MAAM;AACnD,cAAI,cAAc;AAChB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAKA,cAAQ,SAAS,MAAM;AAKvB,UAAI,cAAc;AAChB,sBAAM,qCAAqC;AAC3C,aAAK,WAAW,KAAK,YAAY;AACjC,aAAK,SAAS;AACd;AAAA,MACF;AAKA,WAAK,eAAe,MAAM,KAAK,UAAU,OAAO,SAAS,QAAQ,IAAI;AACrE,WAAK,aAAa,QAAQ;AAK1B,YAAM,KAAK,OAAO,OAAO,WAAW,EAAE,IAAI,KAAK,cAAe,IAAI;AAClE,YAAM,KAAK,UAAU,IAAI,KAAK,cAAe,IAAI;AACjD,YAAM,KAAK,OAAO,OAAO,UAAU,EAAE,IAAI,KAAK,cAAe,IAAI;AACjE,WAAK,WAAW,KAAK,YAAY,KAAK,aAAc;AACpD,WAAK,SAAS;AAAA,IAChB,SAAS,OAAO;AACd,WAAK,WAAW;AAChB,WAAK,SAAS;AACd,YAAM,KAAK,aAAa,OAAO,OAAO,IAAI;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,GAAG,QAAgB,UAAuC;AACxD,kBAAM,2CAA2C,MAAM;AACvD,SAAK,iBAAiB,IAAI,QAAQ,QAAQ;AAC1C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WACE,MACA,SACA;AACA,QAAI,KAAK,WAAW,QAAQ;AAC1B,YAAM,IAAIC,kBAAiB,mCAAmC,KAAK,MAAM,SAAS;AAAA,IACpF;AAEA,SAAK,eAAe,WAAW,MAAM,OAAO;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,QAAoF;AAC5F,QAAI,KAAK,WAAW,QAAQ;AAC1B,YAAM,IAAIA,kBAAiB,yBAAyB,KAAK,MAAM,SAAS;AAAA,IAC1E;AAEA,SAAK,SAAS,KAAK,MAAM;AACzB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,OAAe,SAAuB;AAC7C,UAAM,CAAC,aAAa,GAAG,UAAU,IAAI,QAAQ,MAAM,GAAG;AACtD,SAAK,SAAS,IAAI,OAAO,WAAW;AAEpC,QAAI,WAAW,QAAQ;AACrB,oBAAM,uDAAuD,OAAO,aAAa,UAAU;AAC3F,WAAK,iBAAiB,IAAI,OAAO,UAAU;AAAA,IAC7C,OAAO;AACL,oBAAM,uCAAuC,OAAO,WAAW;AAAA,IACjE;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,aAA8B;AACvC,kBAAc,KAAK,SAAS,IAAI,WAAW,KAAK;AAChD,WAAO,KAAK,UAAU,IAAI,WAAW;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAiC;AAC/B,WAAO,CAAC,GAAG,KAAK,UAAU,KAAK,CAAC,EAC7B,KAAK,kBAAkB,EACvB,IAAI,CAAC,SAAS,KAAK,UAAU,IAAI,IAAI,EAAG,QAAQ;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,WAAoB;AACvC,QAAI,eAAe,CAAC,GAAG,KAAK,UAAU,KAAK,CAAC;AAK5C,QAAI,WAAW;AACb,qBAAe,aAAa;AAAA,QAC1B,CAAC,SAAS,KAAK,UAAU,IAAI,IAAI,EAAG,SAAS,cAAc;AAAA,MAC7D;AAAA,IACF,OAAO;AACL,qBAAe,aAAa,OAAO,CAAC,SAAS,CAAC,KAAK,UAAU,IAAI,IAAI,EAAG,SAAS,SAAS;AAAA,IAC5F;AAEA,WAAO,aAAa,KAAK,kBAAkB,EAAE,IAAI,CAAC,SAAS,KAAK,UAAU,IAAI,IAAI,EAAG,QAAQ;AAAA,EAC/F;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,aAA6C;AACtD,WAAO,KAAK,UAAU,IAAI,WAAW,GAAG,YAAY;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa;AACX,WAAO,CAAC,GAAG,KAAK,SAAS,KAAK,CAAC;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,OAAuC;AACrD,UAAM,eAAe,KAAK,SAAS,IAAI,KAAK;AAC5C,QAAI,CAAC,cAAc;AACjB,aAAO;AAAA,IACT;AAEA,WAAO,KAAK,UAAU,IAAI,YAAY,GAAG,YAAY;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,aAAqB;AACrC,WAAO,CAAC,GAAG,KAAK,SAAS,QAAQ,CAAC,EAC/B,OAAO,CAAC,CAAC,EAAE,OAAO,MAAM;AACvB,aAAO,YAAY;AAAA,IACrB,CAAC,EACA,IAAI,CAAC,CAAC,KAAK,MAAM,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAA0B;AACxB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,SAA2B;AAK/C,QAAI,KAAK,YAAY,SAAS,OAAO,GAAG;AACtC,aAAO,KAAK,qBAAqB,OAAO,EAAE,IAAI,CAAC,YAAY,QAAQ,WAAW;AAAA,IAChF;AAEA,UAAM,qBAAqB,CAAC,GAAG,KAAK,UAAU,KAAK,CAAC,EAAE,OAAO,CAAC,GAAG,KAAK,SAAS,KAAK,CAAC,CAAC;AAEtF,WAAO,mBACJ,IAAI,CAAC,UAAU;AACd,aAAO;AAAA,QACL;AAAA,QACA,UAAU,SAAS,SAAS,KAAK;AAAA,MACnC;AAAA,IACF,CAAC,EACA,KAAK,CAAC,SAAS,SAAS,KAAK,WAAW,QAAQ,QAAQ,EACxD,OAAO,CAAC,WAAW;AAClB,aAAO,OAAO,YAAY;AAAA,IAC5B,CAAC,EACA,IAAI,CAAC,WAAW,OAAO,KAAK;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,SAA2B;AACjD,WAAO,KAAK,YACT,IAAI,CAAC,UAAU;AACd,aAAO;AAAA,QACL;AAAA,QACA,UAAU,SAAS,SAAS,KAAK;AAAA,MACnC;AAAA,IACF,CAAC,EACA,KAAK,CAAC,SAAS,SAAS,KAAK,WAAW,QAAQ,QAAQ,EACxD,OAAO,CAAC,WAAW;AAClB,aAAO,OAAO,YAAY;AAAA,IAC5B,CAAC,EACA,IAAI,CAAC,WAAW,OAAO,KAAK;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,UAA8B;AACpC,SAAK,OAAO,IAAI,WAAW,QAAQ;AACnC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,UAA8B;AACpC,SAAK,OAAO,IAAI,WAAW,QAAQ;AACnC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,UAAsC;AAC3C,SAAK,OAAO,IAAI,UAAU,QAAQ;AAClC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,UAAuD;AAC/D,SAAK,OAAO,IAAI,aAAa,QAAQ;AACrC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,UAAsD;AAC7D,SAAK,OAAO,IAAI,YAAY,QAAQ;AACpC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAAO;AACX,QAAI,KAAK,WAAW,QAAQ;AAC1B;AAAA,IACF;AAKA,SAAK,eAAe,KAAK;AAKzB,SAAK,UAAU,IAAI,WAAW,CAAC,KAAK,eAAe,CAAC,CAAC;AAKrD,SAAK,SAAS;AAMd,UAAM,aAA0B,oBAAI,IAAI;AAKxC,aAAS,UAAU,KAAK,UAAU;AAChC,UAAI;AAMJ,UAAI,OAAO,WAAW,YAAY;AAChC,yBAAiB,MAAM,OAAO;AAAA,MAChC,OAAO;AACL,yBAAiB;AAAA,MACnB;AAEA,YAAM,WAAW,MAAM,eAAe,YAAY;AAElD,eAAS,QAAQ,CAAC,YAAY;AAC5B,aAAK,UAAU,IAAI,QAAQ,aAAa,EAAE,UAAU,SAAS,QAAQ,eAAe,CAAC;AACrF,gBAAQ,QAAQ,QAAQ,CAAC,UAAU,KAAK,SAAS,OAAO,QAAQ,WAAW,CAAC;AAC5E,gBAAQ,aAAa,WAAW,IAAI,QAAQ,SAAS;AAAA,MACvD,CAAC;AAAA,IACH;AAEA,SAAK,cAAc,CAAC,GAAG,UAAU,EAAE,KAAK,kBAAkB;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,KAAwB,aAAiC;AAI7D,kBAAc,KAAK,SAAS,IAAI,WAAW,KAAK;AAChD,UAAM,KAAK,OAAO,OAAO,SAAS,EAAE,IAAI,WAAW;AAKnD,UAAM,UAAU,KAAK,UAAU,IAAI,WAAW;AAC9C,QAAI,CAAC,SAAS;AACZ,YAAM,IAAW,oBAAoB,CAAC,WAAW,CAAC;AAAA,IACpD;AAEA,UAAM,KAAK,OAAO,OAAO,SAAS,EAAE,IAAI,QAAQ,QAAQ;AAKxD,UAAM,qBAAqB,MAAM,QAAQ,OAAO,WAAW,QAAQ,QAAQ;AAC3E,QAAI,CAAC,oBAAoB;AACvB,YAAM,IAAW,oBAAoB,CAAC,WAAW,CAAC;AAAA,IACpD;AAEA,UAAM,KAAK,OAAO,OAAO,QAAQ,EAAE,IAAI,kBAAkB;AACzD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,KAAwB,aAAqB,MAAgB;AAIjE,QAAI,KAAK,WAAW,QAAQ;AAC1B,YAAM,KAAK,KAAK;AAAA,IAClB;AAKA,QAAI,KAAK,WAAW,aAAa;AAC/B,YAAM,IAAIA;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,KAAK,MAAS,aAAa,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAA0B,SAAY,MAAmD;AAI7F,QAAI,KAAK,WAAW,QAAQ;AAC1B,YAAM,KAAK,KAAK;AAAA,IAClB;AAEA,WAAO,KAAK,QAAQ,SAAS,IAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAAO,MAAgB;AAI3B,QAAI,KAAK,WAAW,WAAW;AAC7B,YAAM,IAAIA,kBAAiB,yDAAyD;AAAA,IACtF;AAKA,QAAI,KAAK,WAAW,aAAa;AAC/B,YAAM,IAAIA;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAKA,QAAI,KAAK,WAAW,QAAQ;AAC1B,YAAM,KAAK,KAAK;AAAA,IAClB;AAEA,SAAK,SAAS;AACd,UAAM,EAAE,aAAa,UAAU,YAAY,IAAI,KAAK,aAAa,IAAI;AAKrE,QAAI,CAAC,aAAa;AAChB,oBAAM,gCAAgC,KAAK,gBAAgB,WAAW;AACtE,aAAO,KAAK,UAAU,KAAK,gBAAgB,aAAa,UAAU,WAAW;AAAA,IAC/E;AAKA,kBAAM,6BAA6B,WAAW;AAC9C,WAAO,KAAK,UAAU,aAAa,UAAU,WAAW;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACb,WAAO;AAAA,EACT;AACF;;;Ae1xBA,YAAY,gBAAgB;;;ACL5B,SAAS,iBAAAC,gBAAe,QAAAC,aAAY;AAc7B,IAAM,cAAN,cAA0B,YAAY;AAAA;AAAA;AAAA;AAAA,EAI3C,OAAO,cAAsB;AAAA,EAC7B,OAAO,cAAsB;AAAA;AAAA;AAAA;AAAA,EAW7B,oBAAoB,SAAiB,SAAuC;AAC1E,QAAI,CAAC,QAAQ,KAAK,QAAQ;AACxB,aAAO,CAAC;AAAA,IACV;AAEA,WAAO;AAAA,MACL;AAAA,QACE,SAAS,KAAK,OAAO,OAAO,OAAO;AAAA,QACnC,SAAS,QAAQ,KAAK,IAAI,CAAC,QAAQ;AACjC,gBAAM,YAAY,IAAI,kBAAkB,KAAK,KAAK,MAAM;AACxD,iBAAO;AAAA,YACL,QAAQ,UAAU,iBAAiB;AAAA,YACnC,aAAa,UAAU,kBAAkB;AAAA,UAC3C;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,SAAiB,SAAuC;AACxE,QAAI,CAAC,QAAQ,MAAM,QAAQ;AACzB,aAAO,CAAC;AAAA,IACV;AAEA,WAAO;AAAA,MACL;AAAA,QACE,SAAS,KAAK,OAAO,OAAO,OAAO;AAAA,QACnC,SAAS,QAAQ,MAAM,IAAI,CAAC,SAAS;AACnC,gBAAM,YAAY,IAAI,cAAc,MAAM,KAAK,MAAM;AACrD,iBAAO;AAAA,YACL,QAAQ,UAAU,aAAa;AAAA,YAC/B,aAAa,UAAU,kBAAkB;AAAA,UAC3C;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAgC;AAC9B,UAAM,UAAU,KAAK,OAAO,WAAW,KAAK,IAAI;AAChD,QAAI,CAAC,SAAS;AACZ;AAAA,QACE,KAAK;AAAA,QACL,YAAY,KAAK,IAAI;AAAA,QACrB,KAAK,OAAO,sBAAsB,KAAK,IAAI;AAAA,MAC7C;AACA,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKU,kBAAkB,SAA0B;AACpD,UAAM,YAAY,IAAI,iBAAiB,SAAS,KAAK,MAAM;AAC3D,UAAM,cAAc,UAAU,kBAAkB;AAEhD,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AAEA,SAAK,OAAO,IAAI,EAAE;AAClB,SAAK,OAAO,IAAI,KAAK,OAAO,OAAO,cAAc,CAAC;AAClD,SAAK,OAAO;AAAA,MACVC,MAAK,CAAC,WAAW,GAAG;AAAA,QAClB,aAAa;AAAA,QACb,WAAW;AAAA,QACX,WAAWC;AAAA,MACb,CAAC,EAAE,KAAK,IAAI;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKU,YAAY,SAA0B;AAC9C,UAAM,UAAU,KAAK,OAAO,kBAAkB,QAAQ,WAAW;AACjE,UAAM,YAAY,IAAI,iBAAiB,SAAS,KAAK,MAAM;AAC3D,UAAM,QAAQ,UAAU,YAAY,SAAS,KAAK,OAAO,KAAK,IAAI,QAAQ,CAAC,EAAE,KAAK,IAAI;AAEtF,SAAK,OAAO,IAAI,EAAE;AAClB,SAAK,OAAO,IAAI,KAAK,OAAO,OAAO,QAAQ,CAAC;AAC5C,SAAK,OAAO,IAAI,KAAK;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKU,WAAW,SAA0B;AAC7C,UAAM,SAAS,KAAK,oBAAoB,cAAc,OAAO,EAAE;AAAA,MAC7D,KAAK,kBAAkB,YAAY,OAAO;AAAA,IAC5C;AAEA,QAAI,cAAc,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,UAAU;AACpD,WAAK,OAAO,IAAI,EAAE;AAClB,WAAK,OAAO,IAAI,MAAM,OAAO;AAC7B,WAAK,OAAO,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC;AAAA,IACvC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKU,WAAW,SAA0B;AAC7C,UAAM,YAAY,IAAI,iBAAiB,SAAS,KAAK,MAAM;AAC3D,UAAM,OAAO,UAAU,WAAW,KAAK,OAAO,KAAK,IAAI,QAAQ,CAAC;AAChE,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AAEA,SAAK,OAAO,IAAI,EAAE;AAClB,SAAK,OAAO,IAAI,KAAK,OAAO,OAAO,OAAO,CAAC;AAC3C,SAAK,OAAO,IAAI,IAAI;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,MAAM;AACV,UAAM,iBAAiB,KAAK,qBAAqB;AACjD,QAAI,CAAC,gBAAgB;AACnB,WAAK,WAAW;AAChB;AAAA,IACF;AAEA,UAAM,UAAU,KAAK,OAAO,WAAW,KAAK,IAAI;AAChD,SAAK,kBAAkB,OAAO;AAC9B,SAAK,YAAY,OAAO;AACxB,SAAK,WAAW,OAAO;AACvB,SAAK,WAAW,OAAO;AAAA,EACzB;AACF;AAhJU;AAAA,EADP,KAAK,OAAO,EAAE,aAAa,gBAAgB,cAAc,UAAU,CAAC;AAAA,GAV1D,YAWH;;;ACzBV,SAAS,qBAAqB;AAC9B,SAAS,UAAU,SAAS,gBAAgB;AAC5C,SAAS,WAAW,eAAe,aAAa;AAKhD,IAAM,aAAa,CAAC,OAAO,QAAQ,MAAM;AAOlC,IAAM,WAAN,MAAwF;AAAA;AAAA;AAAA;AAAA,EAI7F;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAsD,CAAC;AAAA,EAEvD,YAAY,kBAA0B,QAAwC;AAC5E,SAAK,oBAAoB;AACzB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,gBAAkD;AACtD,UAAM,WAAoC,CAAC;AAK3C,UAAM,eAAe,MAAM,UAAU,KAAK,mBAAmB;AAAA,MAC3D,UAAU;AAAA,MACV,mBAAmB;AAAA,MACnB,QAAQ,CAAC,aAAqB;AAC5B,cAAM,MAAM,QAAQ,QAAQ;AAK5B,YAAI,SAAS,QAAQ,EAAE,WAAW,GAAG,GAAG;AACtC,iBAAO;AAAA,QACT;AAEA,YAAI,WAAW,SAAS,GAAG,GAAG;AAC5B,iBAAO;AAAA,QACT;AAEA,YAAI,QAAQ,SAAS,CAAC,SAAS,SAAS,OAAO,GAAG;AAChD,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAMD,aAAS,QAAQ,cAAc;AAI7B,UAAI,KAAK,SAAS,KAAK,GAAG;AACxB,eAAO,KAAK,QAAQ,SAAS,KAAK;AAAA,MACpC;AAEA,YAAM,mBAAmB,MAAM,SAAS,KAAK,mBAAmB,cAAc,IAAI,CAAC,CAAC;AAMpF,UAAI,CAAC,KAAK,WAAW,KAAK,QAAQ,gBAAgB,GAAG;AACnD,iBAAS,gBAAgB,IAAI,MAAM,cAAc,MAAM,OAAO,OAAO,gBAAgB;AAAA,MACvF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAA0C;AAC9C,UAAM,qBAAqB,MAAM,KAAK,cAAc;AAEpD,WAAO,KAAK,kBAAkB,EAAE,QAAQ,CAAC,QAAQ;AAC/C,YAAM,UAAU,mBAAmB,GAAG;AACtC,sBAAyB,SAAS,IAAI,GAAG,QAAQ;AACjD,WAAK,UAAU,KAAK,EAAE,SAAS,UAAU,IAAI,CAAC;AAAA,IAChD,CAAC;AAED,WAAO,KAAK,UAAU,IAAI,CAAC,EAAE,SAAS,SAAS,MAAM;AACnD,aAAO,OAAO,OAAO,CAAC,GAAG,QAAQ,UAAU,GAAG,EAAE,SAAS,CAAC;AAAA,IAC5D,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,WAAW,UAAoD;AACnE,WACE,KAAK,UAAU,KAAK,CAAC,EAAE,QAAQ,MAAM;AACnC,aAAO,QAAQ,gBAAgB,SAAS;AAAA,IAC1C,CAAC,GAAG,WAAW;AAAA,EAEnB;AACF;;;AC5HA,SAAS,YAAY;AACrB,SAAS,UAAU,OAAO,iBAAiB;;;ACD3C,SAAS,kBAAkB;AAEpB,IAAM,YAAY,WAAW,YAAY,GAAG;;;ADa5C,IAAM,iBAAN,MAAqB;AAAA,EAC1B;AAAA,EAEA,YAAY,aAAqB;AAC/B,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,WAAyB;AAC7B,UAAM,mBAAmB,MAAM,IAAI,SAAS,KAAK,YAAY,EAAE,YAAY;AAE3E,UAAM,YAAY,KAAK,UAAU,EAAE,UAAU,kBAAkB,SAAS,EAAE,CAAC;AAC3E,UAAM,YAAY,KAAK,KAAK,cAAc,eAAe;AAEzD,UAAM,aAAa,KAAK,KAAK,cAAc,SAAS;AACpD,UAAM,aAAa,KAAK,WAAW,sBAAsB;AAEzD,UAAM,cAAc,KAAK,KAAK,cAAc,WAAW;AACvD,UAAM,kBAAkB,KAAK,WAAW,4BAA4B;AAEpE,UAAM,MAAM,KAAK,cAAc,EAAE,WAAW,KAAK,CAAC;AAClD,YAAQ,IAAI,wBAAwB,KAAK,YAAY,EAAE;AAEvD,UAAM,UAAU,WAAW,SAAS;AACpC,YAAQ,IAAI,sBAAsB;AAElC,UAAM,SAAS,YAAY,UAAU;AACrC,YAAQ,IAAI,gBAAgB;AAE5B,UAAM,SAAS,iBAAiB,WAAW;AAC3C,YAAQ,IAAI,kBAAkB;AAAA,EAChC;AACF;", "names": ["value", "args", "RuntimeException", "args", "string", "TERMINAL_SIZE", "wrap", "TERMINAL_SIZE", "wrap", "string", "flags", "args", "<PERSON><PERSON>", "RuntimeException", "TERMINAL_SIZE", "wrap", "wrap", "TERMINAL_SIZE"]}