# flydrive

<hr>
<br />

<div align="center">
  <h3>Multi-driver storage library for Node.js</h3>
  <p>

FlyDrive is a file storage library for Node.js. It provides a unified API to interact with the local file system and cloud storage solutions like **S3**, **R2**, and **GCS**.

  </p>
</div>

<br />

<div align="center">

[![gh-workflow-image]][gh-workflow-url] [![npm-image]][npm-url] ![][typescript-image] [![license-image]][license-url]

</div>

<div align="center">
  <h3>
    <a href="https://flydrive.dev/docs/introduction">
      Documentation
    </a>
    <span> | </span>
    <a href=".github/CONTRIBUTING.md">
      Contributing
    </a>
  </h3>
</div>

<div align="center">
  <sub>Built with ❤︎ by <a href="https://github.com/thetutlage"><PERSON><PERSON><PERSON></a>
</div>

<br />
<hr>
<br />

![](https://github.com/thetutlage/static/blob/main/sponsorkit/sponsors.png?raw=true)

[gh-workflow-image]: https://img.shields.io/github/actions/workflow/status/flydrive-js/core/checks.yml?style=for-the-badge
[gh-workflow-url]: https://github.com/flydrive-js/core/actions/workflows/checks.yml 'Github action'
[npm-image]: https://img.shields.io/npm/v/flydrive/latest.svg?style=for-the-badge&logo=npm
[npm-url]: https://www.npmjs.com/package/flydrive/v/latest 'npm'
[typescript-image]: https://img.shields.io/badge/Typescript-294E80.svg?style=for-the-badge&logo=typescript
[license-url]: LICENSE.md
[license-image]: https://img.shields.io/github/license/flydrive-js/core?style=for-the-badge
