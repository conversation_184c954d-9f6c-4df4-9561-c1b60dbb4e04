{"name": "get-port", "version": "7.1.0", "description": "Get an available port", "license": "MIT", "repository": "sindresorhus/get-port", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=16"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["port", "find", "finder", "portfinder", "free", "available", "connection", "connect", "open", "net", "tcp", "scan", "random", "preferred", "chosen"], "devDependencies": {"@types/node": "^20.2.5", "ava": "^5.3.0", "tsd": "^0.28.1", "xo": "^0.54.2"}}