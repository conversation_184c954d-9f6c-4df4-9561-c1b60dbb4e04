export default class DependencyTree {
    #private;
    constructor(options: {
        root?: string;
    });
    addRoot(path: string): void;
    /**
     * Get the version of a file
     */
    getVersion(path: string): number;
    /**
     * Add a dependency to a file
     */
    addDependency(parentPath: string, dependency: {
        path: string;
        reloadable?: boolean;
    }): void;
    /**
     * Add a dependent to a file
     */
    addDependent(dependentPath: string, parentPath: string): void;
    /**
     * Invalidate a file and all its dependents
     */
    invalidateFileAndDependents(filePath: string): Set<string>;
    /**
     * Remove a file from the dependency tree
     */
    remove(path: string): void;
    /**
     * Check if a file is reloadable.
     * Basically the algorithm is :
     * - For a given file, we will go up the whole dependency tree until we can reach the ROOT file
     *  = the entry point of the application/the executed script
     * - If we can reach the ROOT file without encountering any reloadable file, then it means we
     *  need to do a FULL RELOAD.
     * - If all paths to reach the ROOT file go through reloadable files, then it means we can do HMR !
     */
    isReloadable(path: string): boolean;
    dump(): {
        path: string;
        boundary: boolean;
        reloadable: boolean;
        version: number;
        dependencies: string[];
        dependents: string[];
    }[];
}
