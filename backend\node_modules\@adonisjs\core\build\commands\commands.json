{"commands": [{"commandName": "add", "description": "Install and configure a package", "help": "", "namespace": null, "aliases": [], "flags": [{"name": "verbose", "flagName": "verbose", "required": false, "type": "boolean", "description": "Display logs in verbose mode"}, {"name": "packageManager", "flagName": "package-manager", "required": false, "type": "string", "description": "Select the package manager you want to use"}, {"name": "dev", "flagName": "dev", "required": false, "type": "boolean", "description": "Should we install the package as a dev dependency", "alias": "D"}, {"name": "force", "flagName": "force", "required": false, "type": "boolean", "description": "Forcefully overwrite existing files"}], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "Package name", "type": "string"}], "options": {"allowUnknownFlags": true}, "filePath": "add.js"}, {"commandName": "build", "description": "Build application for production by compiling frontend assets and TypeScript source to JavaScript", "help": ["Create the production build using the following command.", "```", "{{ binaryName }} build", "```", "", "The assets bundler dev server runs automatically after detecting vite config or webpack config files", "You may pass vite CLI args using the --assets-args command line flag.", "```", "{{ binaryName }} build --assets-args=\"--debug --base=/public\"", "```"], "namespace": null, "aliases": [], "flags": [{"name": "ignoreTsErrors", "flagName": "ignore-ts-errors", "required": false, "type": "boolean", "description": "Ignore TypeScript errors and continue with the build process"}, {"name": "packageManager", "flagName": "package-manager", "required": false, "type": "string", "description": "Define the package manager to copy the appropriate lock file"}, {"name": "assets", "flagName": "assets", "required": false, "type": "boolean", "description": "Build frontend assets", "showNegatedVariantInHelp": true, "default": true}, {"name": "assetsArgs", "flagName": "assets-args", "required": false, "type": "array", "description": "Define CLI arguments to pass to the assets bundler"}], "args": [], "options": {}, "filePath": "build.js"}, {"commandName": "configure", "description": "Configure a package after it has been installed", "help": "", "namespace": null, "aliases": [], "flags": [{"name": "verbose", "flagName": "verbose", "required": false, "type": "boolean", "description": "Display logs in verbose mode", "alias": "v"}, {"name": "force", "flagName": "force", "required": false, "type": "boolean", "description": "Forcefully overwrite existing files", "alias": "f"}], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "Package name", "type": "string"}], "options": {"allowUnknownFlags": true}, "filePath": "configure.js"}, {"commandName": "eject", "description": "Eject scaffolding stubs to your application root", "help": "", "namespace": null, "aliases": [], "flags": [{"name": "pkg", "flagName": "pkg", "required": false, "type": "string", "description": "Mention package name for searching stubs", "default": "@adonisjs/core"}], "args": [{"name": "stub<PERSON><PERSON>", "argumentName": "stub-path", "required": true, "description": "Path to the stubs directory or a single stub file", "type": "string"}], "options": {}, "filePath": "eject.js"}, {"commandName": "env:add", "description": "Add a new environment variable", "help": "", "namespace": "env", "aliases": [], "flags": [{"name": "type", "flagName": "type", "required": false, "type": "string", "description": "Type of the variable"}, {"name": "enum<PERSON><PERSON><PERSON>", "flagName": "enum-values", "required": false, "type": "array", "description": "Allowed values for the enum type in a comma-separated list", "default": [""]}], "args": [{"name": "name", "argumentName": "name", "required": false, "description": "Variable name. Will be converted to screaming snake case", "type": "string"}, {"name": "value", "argumentName": "value", "required": false, "description": "Variable value", "type": "string"}], "options": {"allowUnknownFlags": true}, "filePath": "env/add.js"}, {"commandName": "generate:key", "description": "Generate a cryptographically secure random application key", "help": "", "namespace": "generate", "aliases": [], "flags": [{"name": "show", "flagName": "show", "required": false, "type": "boolean", "description": "Display the key on the terminal, instead of writing it to .env file"}, {"name": "force", "flagName": "force", "required": false, "type": "boolean", "description": "Force update .env file in production environment"}], "args": [], "options": {}, "filePath": "generate_key.js"}, {"commandName": "inspect:rcfile", "description": "Inspect the RC file with its default values", "help": "", "namespace": "inspect", "aliases": [], "flags": [], "args": [], "options": {}, "filePath": "inspect_rcfile.js"}, {"commandName": "list:routes", "description": "List application routes. This command will boot the application in the console environment", "help": "", "namespace": "list", "aliases": [], "flags": [{"name": "middleware", "flagName": "middleware", "required": false, "type": "array", "description": "View routes that includes all the mentioned middleware names. Use * to see routes that are using one or more middleware"}, {"name": "ignoreMiddleware", "flagName": "ignore-middleware", "required": false, "type": "array", "description": "View routes that does not include all the mentioned middleware names. Use * to see routes that are using zero middleware"}, {"name": "json", "flagName": "json", "required": false, "type": "boolean", "description": "Get routes list as a JSON string"}, {"name": "table", "flagName": "table", "required": false, "type": "boolean", "description": "View list of routes as a table"}], "args": [{"name": "match", "argumentName": "match", "required": false, "description": "Find routes matching the given keyword. Route name, pattern and controller name will be searched against the keyword", "type": "string"}], "options": {"startApp": true}, "filePath": "list/routes.js"}, {"commandName": "make:command", "description": "Create a new ace command class", "help": "", "namespace": "make", "aliases": [], "flags": [], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "Name of the command", "type": "string"}], "options": {}, "filePath": "make/command.js"}, {"commandName": "make:controller", "description": "Create a new HTTP controller class", "help": "", "namespace": "make", "aliases": [], "flags": [{"name": "singular", "flagName": "singular", "required": false, "type": "boolean", "description": "Generate controller in singular form", "alias": "s"}, {"name": "resource", "flagName": "resource", "required": false, "type": "boolean", "description": "Generate resourceful controller with methods to perform CRUD actions on a resource", "alias": "r"}, {"name": "api", "flagName": "api", "required": false, "type": "boolean", "description": "Generate resourceful controller without the \"edit\" and the \"create\" methods", "alias": "a"}], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "The name of the controller", "type": "string"}, {"name": "actions", "argumentName": "actions", "required": false, "description": "Create controller with custom method names", "type": "spread"}], "options": {"allowUnknownFlags": true}, "filePath": "make/controller.js"}, {"commandName": "make:event", "description": "Create a new event class", "help": "", "namespace": "make", "aliases": [], "flags": [], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "Name of the event", "type": "string"}], "options": {"allowUnknownFlags": true}, "filePath": "make/event.js"}, {"commandName": "make:exception", "description": "Create a new custom exception class", "help": "", "namespace": "make", "aliases": [], "flags": [], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "Name of the exception", "type": "string"}], "options": {"allowUnknownFlags": true}, "filePath": "make/exception.js"}, {"commandName": "make:listener", "description": "Create a new event listener class", "help": "", "namespace": "make", "aliases": [], "flags": [{"name": "event", "flagName": "event", "required": false, "type": "string", "description": "Generate an event class alongside the listener", "alias": "e"}], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "Name of the event listener", "type": "string"}], "options": {"allowUnknownFlags": true}, "filePath": "make/listener.js"}, {"commandName": "make:middleware", "description": "Create a new middleware class for HTTP requests", "help": "", "namespace": "make", "aliases": [], "flags": [{"name": "stack", "flagName": "stack", "required": false, "type": "string", "description": "The stack in which to register the middleware", "alias": "s"}], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "Name of the middleware", "type": "string"}], "options": {"allowUnknownFlags": true}, "filePath": "make/middleware.js"}, {"commandName": "make:preload", "description": "Create a new preload file inside the start directory", "help": "", "namespace": "make", "aliases": [], "flags": [{"name": "register", "flagName": "register", "required": false, "type": "boolean", "description": "Auto register the preload file inside the .adonisrc.ts file", "showNegatedVariantInHelp": true, "alias": "r"}, {"name": "environments", "flagName": "environments", "required": false, "type": "array", "description": "Define the preload file's environment. Accepted values are \"web,console,test,repl\"", "alias": "e"}], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "Name of the preload file", "type": "string"}], "options": {}, "filePath": "make/preload.js"}, {"commandName": "make:provider", "description": "Create a new service provider class", "help": "", "namespace": "make", "aliases": [], "flags": [{"name": "register", "flagName": "register", "required": false, "type": "boolean", "description": "Auto register the provider inside the .adonisrc.ts file", "showNegatedVariantInHelp": true, "alias": "r"}, {"name": "environments", "flagName": "environments", "required": false, "type": "array", "description": "Define the provider environment. Accepted values are \"web,console,test,repl\"", "alias": "e"}], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "Name of the provider", "type": "string"}], "options": {}, "filePath": "make/provider.js"}, {"commandName": "make:service", "description": "Create a new service class", "help": "", "namespace": "make", "aliases": [], "flags": [], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "Name of the service", "type": "string"}], "options": {"allowUnknownFlags": true}, "filePath": "make/service.js"}, {"commandName": "make:test", "description": "Create a new Japa test file", "help": "", "namespace": "make", "aliases": [], "flags": [{"name": "suite", "flagName": "suite", "required": false, "type": "string", "description": "The suite for which to create the test file", "alias": "s"}], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "Name of the test file", "type": "string"}], "options": {}, "filePath": "make/test.js"}, {"commandName": "make:validator", "description": "Create a new file to define VineJS validators", "help": "", "namespace": "make", "aliases": [], "flags": [{"name": "resource", "flagName": "resource", "required": false, "type": "boolean", "description": "Create a file with pre-defined validators for create and update actions"}], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "Name of the validator file", "type": "string"}], "options": {"allowUnknownFlags": true}, "filePath": "make/validator.js"}, {"commandName": "make:view", "description": "Create a new Edge.js template file", "help": "", "namespace": "make", "aliases": [], "flags": [], "args": [{"name": "name", "argumentName": "name", "required": true, "description": "Name of the template", "type": "string"}], "options": {}, "filePath": "make/view.js"}, {"commandName": "repl", "description": "Start a new REPL session", "help": "", "namespace": null, "aliases": [], "flags": [], "args": [], "options": {"startApp": true, "staysAlive": true}, "filePath": "repl.js"}, {"commandName": "serve", "description": "Start the development HTTP server along with the file watcher to perform restarts on file change", "help": ["Start the development server with file watcher using the following command.", "```", "{{ binaryName }} serve --watch", "```", "", "You can also start the server with HMR support using the following command.", "```", "{{ binaryName }} serve --hmr", "```", "", "The assets bundler dev server runs automatically after detecting vite config or webpack config files", "You may pass vite CLI args using the --assets-args command line flag.", "```", "{{ binaryName }} serve --assets-args=\"--debug --base=/public\"", "```"], "namespace": null, "aliases": [], "flags": [{"name": "hmr", "flagName": "hmr", "required": false, "type": "boolean", "description": "Start the server with HMR support"}, {"name": "watch", "flagName": "watch", "required": false, "type": "boolean", "description": "Watch filesystem and restart the HTTP server on file change", "alias": "w"}, {"name": "poll", "flagName": "poll", "required": false, "type": "boolean", "description": "Use polling to detect filesystem changes", "alias": "p"}, {"name": "clear", "flagName": "clear", "required": false, "type": "boolean", "description": "Clear the terminal for new logs after file change", "showNegatedVariantInHelp": true, "default": true}, {"name": "assets", "flagName": "assets", "required": false, "type": "boolean", "description": "Start assets bundler dev server", "showNegatedVariantInHelp": true, "default": true}, {"name": "assetsArgs", "flagName": "assets-args", "required": false, "type": "array", "description": "Define CLI arguments to pass to the assets bundler"}], "args": [], "options": {"staysAlive": true}, "filePath": "serve.js"}, {"commandName": "test", "description": "Run tests along with the file watcher to re-run tests on file change", "help": "", "namespace": null, "aliases": [], "flags": [{"name": "files", "flagName": "files", "required": false, "type": "array", "description": "Filter tests by the filename"}, {"name": "tags", "flagName": "tags", "required": false, "type": "array", "description": "Filter tests by tags"}, {"name": "groups", "flagName": "groups", "required": false, "type": "array", "description": "Filter tests by parent group title"}, {"name": "tests", "flagName": "tests", "required": false, "type": "array", "description": "Filter tests by test title"}, {"name": "reporters", "flagName": "reporters", "required": false, "type": "array", "description": "Activate one or more test reporters"}, {"name": "watch", "flagName": "watch", "required": false, "type": "boolean", "description": "Watch filesystem and re-run tests on file change"}, {"name": "poll", "flagName": "poll", "required": false, "type": "boolean", "description": "Use polling to detect filesystem changes"}, {"name": "timeout", "flagName": "timeout", "required": false, "type": "number", "description": "Define default timeout for all tests"}, {"name": "retries", "flagName": "retries", "required": false, "type": "number", "description": "Define default retries for all tests"}, {"name": "failed", "flagName": "failed", "required": false, "type": "boolean", "description": "Execute tests failed during the last run"}, {"name": "clear", "flagName": "clear", "required": false, "type": "boolean", "description": "Clear the terminal for new logs after file change", "showNegatedVariantInHelp": true, "default": true}, {"name": "assets", "flagName": "assets", "required": false, "type": "boolean", "description": "Start assets bundler dev server.", "showNegatedVariantInHelp": true, "default": true}, {"name": "assetsArgs", "flagName": "assets-args", "required": false, "type": "array", "description": "Define CLI arguments to pass to the assets bundler"}], "args": [{"name": "suites", "argumentName": "suites", "required": false, "description": "Mention suite names to run tests for selected suites", "type": "spread"}], "options": {"allowUnknownFlags": true, "staysAlive": true}, "filePath": "test.js"}], "version": 1}