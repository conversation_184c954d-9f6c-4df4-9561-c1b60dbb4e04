{"name": "fontkit", "version": "1.9.0", "description": "An advanced font engine for Node and the browser", "keywords": ["opentype", "font", "typography", "subset", "emoji", "glyph", "layout"], "scripts": {"test": "run-s build mocha", "mocha": "mocha", "build": "parcel build", "prepublish": "run-s clean trie:** build", "trie:data": "node src/opentype/shapers/generate-data.js", "trie:use": "node src/opentype/shapers/gen-use.js", "trie:indic": "node src/opentype/shapers/gen-indic.js", "clean": "shx rm -rf src/opentype/shapers/data.trie src/opentype/shapers/use.trie src/opentype/shapers/use.json src/opentype/shapers/indic.trie src/opentype/shapers/indic.json dist", "coverage": "c8 mocha"}, "type": "module", "main": "dist/main.cjs", "module": "dist/module.mjs", "source": "src/index.js", "exports": {"import": "./dist/module.mjs", "require": "./dist/main.cjs"}, "targets": {"main": {"engines": {"browsers": "Chrome 70"}}}, "files": ["src", "dist", "iconv-lite.cjs"], "author": "<PERSON> Go<PERSON>t <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/foliojs/fontkit.git"}, "dependencies": {"@swc/helpers": "^0.3.13", "brotli": "^1.3.2", "clone": "^2.1.2", "deep-equal": "^2.0.5", "dfa": "^1.2.0", "restructure": "^2.0.1", "tiny-inflate": "^1.0.3", "unicode-properties": "^1.3.1", "unicode-trie": "^2.0.0"}, "devDependencies": {"c8": "^7.11.3", "codepoints": "^1.2.0", "concat-stream": "^2.0.0", "iconv-lite": "^0.4.24", "mocha": "^10.0.0", "npm-run-all": "^4.1.5", "parcel": "^2.5.0", "shx": "^0.3.4"}, "alias": {"./iconv-lite.cjs": "clone"}}