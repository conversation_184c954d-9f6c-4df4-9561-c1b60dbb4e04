{"author": "<PERSON> <<EMAIL>>", "bugs": {"url": "http://github.com/sebbo2002/ical-generator/issues"}, "dependencies": {"uuid-random": "^1.3.2"}, "description": "ical-generator is a small piece of code which generates ical calendar files", "devDependencies": {"@eslint/js": "^9.8.0", "@qiwi/semantic-release-gh-pages-plugin": "^5.2.12", "@sebbo2002/semantic-release-jsr": "^1.0.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/exec": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/npm": "^12.0.1", "@touch4it/ical-timezones": "^1.9.0", "@types/eslint__js": "^8.42.3", "@types/express": "^4.17.21", "@types/luxon": "^3.4.2", "@types/mocha": "^10.0.7", "c8": "^10.1.2", "dayjs": "^1.11.12", "eslint": "^9.8.0", "eslint-plugin-jsonc": "^2.16.0", "esm": "^3.2.25", "license-checker": "^25.0.1", "luxon": "^3.4.4", "mocha": "^10.6.0", "mochawesome": "^7.1.3", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "nyc": "^17.0.0", "rrule": "^2.8.1", "semantic-release": "^24.0.0", "semantic-release-license": "^1.0.2", "source-map-support": "^0.5.21", "ts-node": "^10.9.2", "tsup": "^8.2.4", "typedoc": "^0.26.3", "typescript": "^5.5.4", "typescript-eslint": "^8.0.0-alpha.62"}, "engines": {"node": ">=18.0.0"}, "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "files": ["/src", "/dist"], "homepage": "https://github.com/sebbo2002/ical-generator", "keywords": ["ical", "ics", "icalendar", "generator", "calendar", "subscription", "outlook", "rfc", "rfc5545", "events", "alarms"], "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.js", "name": "ical-generator", "peerDependencies": {"@touch4it/ical-timezones": ">=1.6.0", "@types/luxon": ">= 1.26.0", "@types/mocha": ">= 8.2.1", "dayjs": ">= 1.10.0", "luxon": ">= 1.26.0", "moment": ">= 2.29.0", "moment-timezone": ">= 0.5.33", "rrule": ">= 2.6.8"}, "peerDependenciesMeta": {"@touch4it/ical-timezones": {"optional": true}, "@types/luxon": {"optional": true}, "@types/mocha": {"optional": true}, "@types/node": {"optional": true}, "dayjs": {"optional": true}, "luxon": {"optional": true}, "moment": {"optional": true}, "moment-timezone": {"optional": true}, "rrule": {"optional": true}}, "preferGlobal": false, "repository": {"type": "git", "url": "https://github.com/sebbo2002/ical-generator.git"}, "runkitExampleFilename": "examples/example-runkit.js", "scripts": {"build": "tsup && cp ./dist/index.d.ts ./dist/index.d.cts", "build-all": "./.github/workflows/build.sh", "coverage": "c8 mocha", "develop": "TS_NODE_TRANSPILE_ONLY=true node --no-warnings --enable-source-maps --loader ts-node/esm src/bin/start.ts", "example": "node ./dist/examples/push.js", "license-check": "license-checker --production --summary", "lint": "eslint .", "start": "node ./dist/bin/start.js", "test": "mocha"}, "type": "module", "version": "7.2.0"}