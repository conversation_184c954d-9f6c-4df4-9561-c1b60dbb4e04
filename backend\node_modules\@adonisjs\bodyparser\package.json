{"name": "@adonisjs/bodyparser", "version": "10.1.0", "description": "BodyParser middleware for AdonisJS http server to read and parse request body", "type": "module", "files": ["build", "!build/bin", "!build/tests", "!build/tests_helpers"], "engines": {"node": ">=18.16.0"}, "exports": {".": "./build/index.js", "./factories": "./build/factories/main.js", "./bodyparser_middleware": "./build/src/bodyparser_middleware.js", "./types": "./build/src/types.js"}, "scripts": {"pretest": "npm run lint", "test": "cross-env NODE_DEBUG=adonisjs:bodyparser c8 npm run quick:test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit", "clean": "del-cli build", "precompile": "npm run lint && npm run clean", "compile": "tsup-node && tsc --emitDeclarationOnly --declaration", "build": "npm run compile", "version": "npm run build", "prepublishOnly": "npm run build", "release": "release-it", "quick:test": "node --import=ts-node-maintained/register/esm --enable-source-maps bin/test.ts"}, "devDependencies": {"@adonisjs/application": "^8.4.1", "@adonisjs/encryption": "^6.0.2", "@adonisjs/eslint-config": "^2.0.0", "@adonisjs/events": "^9.0.2", "@adonisjs/fold": "^10.1.3", "@adonisjs/http-server": "^7.6.1", "@adonisjs/logger": "^6.0.6", "@adonisjs/prettier-config": "^1.4.4", "@adonisjs/tsconfig": "^1.4.0", "@japa/assert": "^4.0.1", "@japa/file-system": "^2.3.2", "@japa/runner": "^4.2.0", "@poppinss/file-generator": "^2.1.4", "@release-it/conventional-changelog": "^10.0.1", "@swc/core": "1.10.7", "@types/bytes": "^3.1.5", "@types/fs-extra": "^11.0.4", "@types/inflation": "^2.0.4", "@types/media-typer": "^1.1.3", "@types/node": "^22.15.18", "@types/supertest": "^6.0.3", "c8": "^10.1.3", "cross-env": "^7.0.3", "del-cli": "^6.0.0", "eslint": "^9.26.0", "fs-extra": "^11.3.0", "prettier": "^3.5.3", "reflect-metadata": "^0.2.2", "release-it": "^19.0.2", "supertest": "^7.1.1", "ts-node-maintained": "^10.9.5", "tsup": "^8.4.0", "typescript": "^5.8.3", "undici": "^7.9.0"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@poppinss/macroable": "^1.0.4", "@poppinss/multiparty": "^2.0.1", "@poppinss/utils": "^6.9.3", "@types/qs": "^6.9.18", "bytes": "^3.1.2", "file-type": "^20.5.0", "inflation": "^2.1.0", "media-typer": "^1.1.0", "qs": "^6.14.0", "raw-body": "^3.0.0"}, "peerDependencies": {"@adonisjs/http-server": "^7.4.0"}, "keywords": ["ad<PERSON><PERSON><PERSON><PERSON>", "bodyparser", "multipart"], "homepage": "https://github.com/adonisjs/bodyparser#readme", "repository": {"type": "git", "url": "git+https://github.com/adonisjs/bodyparser.git"}, "bugs": {"url": "https://github.com/adonisjs/bodyparser/issues"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "publishConfig": {"access": "public", "provenance": true}, "tsup": {"entry": ["./index.ts", "./src/types.ts", "./src/bodyparser_middleware.ts", "./factories/main.ts"], "outDir": "./build", "clean": true, "format": "esm", "dts": false, "sourcemap": false, "target": "esnext"}, "release-it": {"git": {"requireCleanWorkingDir": true, "requireUpstream": true, "commitMessage": "chore(release): ${version}", "tagAnnotation": "v${version}", "push": true, "tagName": "v${version}"}, "github": {"release": true}, "npm": {"publish": true, "skipChecks": true}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "angular"}}}}, "c8": {"reporter": ["text", "html"], "exclude": ["tests/**", "test_factories/**", ".yalc/**"]}, "prettier": "@adonisjs/prettier-config"}