/**
 * We use CRC32 just to add a recognizable checksum to tokens. This helps
 * secret scanning tools like https://docs.github.com/en/github/administering-a-repository/about-secret-scanning easily detect tokens generated by a given program.
 *
 * You can learn more about appending checksum to a hash here in this Github
 * article. https://github.blog/2021-04-05-behind-githubs-new-authentication-token-formats/
 *
 * Code taken from:
 * https://github.com/tsxper/crc32/blob/main/src/CRC32.ts
 */
export declare class CRC32 {
    #private;
    calculate(input: string): number;
    forString(input: string): number;
    forBytes(bytes: Uint8Array, accumulator?: number): number;
}
